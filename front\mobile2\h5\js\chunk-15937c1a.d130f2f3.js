(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-15937c1a"],{"6eb8":function(e,t,n){"use strict";n("c064")},"74a0":function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"i",(function(){return i})),n.d(t,"f",(function(){return d})),n.d(t,"h",(function(){return l})),n.d(t,"g",(function(){return c})),n.d(t,"d",(function(){return u})),n.d(t,"e",(function(){return p})),n.d(t,"c",(function(){return m})),n.d(t,"j",(function(){return f})),n.d(t,"b",(function(){return v}));var o=n("b775");const r=window.sysConfig.services.wecom+"/RedEnvelopes",s=window.sysConfig.services.weChat+"/RedEnvelopes";function a(e){return Object(o["a"])({url:r+"/findCompanyRedEnvelopes",params:{sceneType:e}})}function i(e){return Object(o["a"])({url:r+"/sendReEnvelopesToCustomer",method:"post",data:e})}function d(e){return Object(o["a"])({url:r+"/sendCompanyEnvelopesToCustomer",method:"post",data:e})}function l(e){return Object(o["a"])({url:r+"/sendPersonReEnvelopesToGroup",method:"post",data:e})}function c(e){return Object(o["a"])({url:r+"/sendCompanyEnvelopesToGroup",method:"post",data:e})}function u(){return Object(o["a"])({url:s+"/findRedEnvelopesInfo"})}function p(e){return Object(o["a"])({url:s+"/receiveRedEnvelopes",params:e})}function m(e){return Object(o["a"])({url:s+"/checkRecevieRedEnvelopes",params:e})}function f(e){return Object(o["a"])({url:s+"/updateState",method:"post",data:e})}function v(e){return Object(o["a"])({url:s+"/receiveRedEnvelopesLists",params:e})}},ba04:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"red-packet"},[t("div",{staticClass:"personal"},[t("van-button",{attrs:{type:"info",size:"mini"},on:{click:function(t){return e.showDialog("personal")}}},[e._v("+ 发送个人红包")])],1),t("div",[t("div",{staticClass:"title"},[e._v("企业红包")]),t("van-list",{attrs:{finished:e.finished,"finished-text":"没有更多了",error:e.loadFail,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(t){e.loadFail=t}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,(function(n,o){return t("div",{key:o,staticClass:"fxbw red-packet-list"},[t("div",{staticClass:"red-packet-message"},[e._v(e._s(n.name))]),t("div",{staticClass:"ar red-packet-list--right"},[t("div",{staticClass:"money"},[e._v("￥"+e._s(n.money))]),t("van-button",{attrs:{type:"info",size:"small"},on:{click:function(t){return e.showDialog("enterprise",n)}}},[e._v("发送")])],1)])})),0)],1),t("van-action-sheet",{attrs:{description:(e.redPacketType.includes("personal")?"个人":"企业")+"红包"},model:{value:e.visible,callback:function(t){e.visible=t},expression:"visible"}},[t("van-form",{ref:"form",attrs:{colon:""},on:{submit:e.sendRedPacket}},[e.redPacketType.includes("enterprise")?t("van-field",{attrs:{label:"红包金额(元)",value:"￥"+e.form.redEnvelopeAmount,readonly:""}}):e._e(),2==e.sceneType?t("van-field",{attrs:{name:"radio",label:"红包类型",required:""},scopedSlots:e._u([{key:"input",fn:function(){return[t("van-radio-group",{attrs:{direction:"horizontal"},model:{value:e.form.redEnvelopesType,callback:function(t){e.$set(e.form,"redEnvelopesType",t)},expression:"form.redEnvelopesType"}},[t("van-radio",{attrs:{name:1}},[e._v("普通红包")]),t("van-radio",{attrs:{name:2}},[e._v("拼手气红包")])],1)]},proxy:!0}],null,!1,1586286663)}):e._e(),e.redPacketType.includes("personal")?[t("van-field",{attrs:{type:"text",label:{1:"红包金额(元)",2:"红包总额(元)"}[e.sceneType],placeholder:"请输入金额",required:"",rules:e.rules.redEnvelopeAmount},model:{value:e.form.redEnvelopeAmount,callback:function(t){e.$set(e.form,"redEnvelopeAmount",t)},expression:"form.redEnvelopeAmount"}}),t("div",{staticClass:"sub-des"},[e._v("精确到小数点后两位，可输入0.01~200")])]:e._e(),2==e.sceneType?t("van-field",{attrs:{type:"digit",label:"红包个数",placeholder:"请输入大于1的正整数",required:"",rules:e.rules.redEnvelopeNum},model:{value:e.form.redEnvelopeNum,callback:function(t){e.$set(e.form,"redEnvelopeNum",t)},expression:"form.redEnvelopeNum"}}):e._e(),t("van-field",{attrs:{type:"text",label:"红包名称",placeholder:"请输入名称",maxlength:"16","show-word-limit":""},model:{value:e.form.redEnvelopeName,callback:function(t){e.$set(e.form,"redEnvelopeName",t)},expression:"form.redEnvelopeName"}}),t("div",{staticStyle:{width:"90%",margin:"20px auto 40px"}},[t("van-button",{attrs:{round:"",block:"",size:"normal",type:"info","native-type":"submit"}},[e._v("确认发送")])],1)],2)],1)],1)},r=[],s=(n("e9f5"),n("7d54"),n("74a0")),a=n("ed08"),i={name:"",components:{},data(){let e=this;return{loading:!1,finished:!1,loadFail:!1,sceneType:void 0,list:[],visible:!1,redPacketType:"personal",form:{redEnvelopeAmount:1,redEnvelopeName:"",redEnvelopeNum:1,sendUserId:"",redEnvelopesType:1,chatId:"",externalUserid:""},rules:{redEnvelopeAmount:[{required:!0},{pattern:/(^(0\.0[1-9]|0\.[1-9]\d|[1-9]\d?(\.\d{1,2})?|1\d{2}(\.\d{1,2})?)$|^200(\.\d{1,2})?$)/,message:"输入错误"}],redEnvelopeNum:[{required:!0},{validator(t){if(1==e.form.redEnvelopesType){if(!/(^([1-9]\d{0,1}|[1-5]\d{2})$|^600$)/.test(t))return!1;if(e.form.redEnvelopeAmount){let n=Math.max(Math.floor(3*e.form.redEnvelopeAmount),1),o=n>=t;return o||e.$notify({type:"danger",message:`当前红包金额最多可发${n}个红包`}),o}return!0}return/(^(0\.0[1-9]|0\.[1-9]\d|[1-9]\d?(\.\d{1,2})?|1\d{2}(\.\d{1,2})?)$|^200(\.\d{1,2})?$)/.test(t)},message:"输入错误"}]}}},computed:{userId(){return this.$store.state.userId}},watch:{},created(){},mounted(){this.init()},methods:{init(){this.$toast.loading({duration:0,forbidClick:!0});let e=void 0,t=this;wx.invoke("getContext",{},(async function(n){if("getContext:ok"==n.err_msg){if(e=n.entry,!["single_chat_tools","group_chat_tools"].includes(e))return void t.$toast("入口错误："+e);try{let n="getCurExternalContact";"single_chat_tools"===e?t.sceneType=1:"group_chat_tools"===e&&(t.sceneType=2,n="getCurExternalChat"),t.getList(),wx.invoke(n,{},e=>{e.err_msg==n+":ok"?(t.form.externalUserid=e.userId,t.form.chatId=e.chatId):t.$dialog({message:"进入失败："+JSON.stringify(e)}),t.$toast.clear()})}catch(o){t.$dialog({message:"err"+JSON.stringify(o.message)})}}}))},getList(){this.loading=!0,this.finished=!1,Object(s["a"])(this.sceneType).then(({data:e,total:t})=>{e.forEach(e=>{e.money=(e.money/100).toFixed(2)}),this.list=e,this.loading=!1,this.refreshing=!1,this.finished=!0}).catch(()=>{this.loadFail=!0})},showDialog(e,t={}){Object.assign(this.form,{redEnvelopeAmount:t.money||1,redenvelopesId:t.id,redEnvelopeName:t.name||"恭喜发财，大吉大利",redEnvelopeNum:t.redEnvelopeNum||1}),this.redPacketType=e,this.visible=!0,this.$nextTick(()=>{})},sendRedPacket(){this.$toast.loading({duration:0,forbidClick:!0});let e="";e=1==this.sceneType?{personal:s["i"],enterprise:s["f"]}[this.redPacketType]:{personal:s["h"],enterprise:s["g"]}[this.redPacketType];let t=Object.assign({},this.form);t.redEnvelopeAmount*=100,t.sendUserId=this.userId,e(t).then(({data:e,msg:n})=>{this.$toast.clear(),this.send(t,e||n)}).finally(()=>{this.$toast.clear()})},async send(e,t){let n=this,o={};try{let r=Object(a["f"])(window.location.search),s=Object(a["f"])(window.location.hash);Object.assign(e,{orderId:t,sceneType:n.sceneType,appId:sessionStorage.appId,tenantId:sessionStorage.tenantId},r,s);for(const t in e)e.hasOwnProperty.call(e,t)&&!e[t]&&delete e[t];delete e.code;let i=Object(a["e"])(e),d=location.protocol+"//"+location.host+window.sysConfig.BASE_URL,l=`${d}?${i}#/redPacketReceive`;o={msgtype:"news",news:{link:l,title:e.redEnvelopeName,desc:"点我领取红包",imgUrl:d+"static/redPacketThumb.png"}}}catch(r){n.$dialog({message:"err:"+JSON.stringify(r.message)})}wx.invoke("sendChatMessage",o,(function(e){e.err_msg}))}}},d=i,l=(n("6eb8"),n("2877")),c=Object(l["a"])(d,o,r,!1,null,"1c44f41a",null);t["default"]=c.exports},c064:function(e,t,n){}}]);