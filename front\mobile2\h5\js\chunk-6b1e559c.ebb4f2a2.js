(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6b1e559c"],{"086d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e._self._c;return t("div",{staticClass:"receive"},[e.opened?t("div",{staticClass:"red-packet-received"},[t("div",{staticClass:"red-packet-received-thumb"}),t("div",{staticClass:"red-packet-received-info ac"},[t("van-image",{attrs:{width:"60",height:"60",radius:"4",src:e.redPacket.logo}}),t("div",{staticClass:"corp-name"},[e._v(" "+e._s(e.redPacket.corpName)+"发出的红包 "),2==e.redPacket.sceneType?t("span",{staticClass:"red-icon"},[e._v(" "+e._s({1:"普",2:"拼"}[e.redPacket.redEnvelopesType])+" ")]):e._e()]),t("div",{staticClass:"red-packet-name"},[e._v(e._s(e.redPacket.redEnvelopeName))]),0!=e.redPacket.currentAcceptMoney?[t("div",{staticClass:"received-num-wrap"},[t("span",{staticClass:"red-packet-received-num"},[e._v(e._s(e.redPacket.currentAcceptMoney))]),e._v(" 元 ")]),t("div",{staticClass:"red-packet-text"},[e._v("已存入零钱，可直接使用")])]:e._e()],2),t("van-cell-group",{attrs:{title:e.title}},e._l(e.redPacket.accpestCustomerList,(function(a,r){return t("van-cell",{key:r,attrs:{center:""},scopedSlots:e._u([{key:"title",fn:function(){return[t("van-image",{attrs:{width:"60",height:"60",src:a.avatar}}),t("div",{staticClass:"bfc-d user"},[t("div",{staticClass:"user-name"},[e._v(e._s(a.customerName))]),t("div",{staticClass:"red-packet-text"},[e._v(e._s(e.dateFormat(a.accpectTime)))])])]},proxy:!0}],null,!0)},[t("div",{staticClass:"money"},[e._v(e._s(a.accpectMoney)+"元")])])})),1)],1):t("div",{staticClass:"red-packet-thumb-wrap"},[t("div",{staticClass:"red-packet-info"},[t("van-image",{attrs:{width:"60",height:"60",radius:"4",src:e.redPacket.logo}}),t("div",{staticClass:"corp-name"},[e._v(e._s(e.redPacket.corpName))]),t("div",{staticClass:"red-packet-name"},[e._v(e._s(e.redPacket.redEnvelopeName))]),e.errorMsg?t("div",{staticClass:"red-packet-error"},[e._v(e._s(e.errorMsg))]):e._e()],1),t("van-image",{class:["open-btn",e.animate&&"tarns"],attrs:{width:"30",height:"30",radius:"4",src:a("105e")}}),t("img",{staticClass:"red-packet-thumb",attrs:{src:a("9804")},on:{click:e.receive}})],1),t("audio",{ref:"audio",attrs:{src:a("d4bc")}})])},s=[],c=(a("e9f5"),a("7d54"),a("74a0")),n=a("ed08"),i={name:"",components:{},data(){return{list:[],opened:!1,openId:0,redPacket:{},errorMsg:"",animate:!1,over:!1}},computed:{title(){let e=this.redPacket,t=e.totalMoney,a=e.redEnvelopeNum;return 0!=e.currentAcceptMoney?`己领取${(e.accpestCustomerList||[]).length}/${a}个红包，共${e.accpectMoney}/${t}元`:`${a}个红包共${t}元，已被抢光`}},watch:{},created(){this.init()},mounted(){},methods:{init(){this.$toast.loading({duration:0,forbidClick:!0}),Object(n["d"])().then(({openId:e,nickName:t,avatar:a})=>{if(e){let r=Object(n["f"])(window.location.search),s=Object(n["f"])(window.location.hash);Object.assign(r,s),this.redPacket=r,this.openId=e,this.receiveName=t,this.avatar=a,this.getRedPacketInfo(),this.getReceiveStatus()}else this.$toast.clear()}).catch(()=>{this.$toast("授权异常，请刷新重试")})},getRedPacketInfo(){Object(c["d"])(this.redPacket.tenantId).then(({data:e})=>{this.redPacket=Object.assign({},this.redPacket,e)})},getReceiveStatus(){let e={orderNo:this.redPacket.orderId,openId:this.openId};Object(c["c"])(e).then(({data:e})=>{2==e.sendState?this.getReceiveList(!0):this.$toast.clear(),e.sendState>2&&(this.errorMsg={3:"发放失败",4:"退款中",5:"已退款",6:"已取消"}[e.sendState])})},receive(){if(this.errorMsg)return void this.$toast(this.errorMsg);if(!this.openId)return;this.$toast.loading({duration:0,forbidClick:!0});let e={orderNo:this.redPacket.orderId,openId:this.openId,appId:this.redPacket.appId,chatId:this.redPacket.chatId,externalUserid:this.redPacket.externalUserid,receiveName:this.receiveName,avatar:this.avatar,tenantId:this.redPacket.tenantId};Object(c["e"])(e).then(e=>{if(200==e.code){let t=e.data;wx.checkJsApi({jsApiList:["requestMerchantTransfer"],success:e=>{e.checkResult["requestMerchantTransfer"]?WeixinJSBridge.invoke("requestMerchantTransfer",{mchId:t.mchId,appId:t.appId,package:t.packageId},e=>{"requestMerchantTransfer:ok"===e.err_msg?Object(c["j"])({id:t.envelopesRecordId,sendState:2}).then(({data:e})=>{this.getReceiveList()}):"requestMerchantTransfer:cancel"===e.err_msg&&(Object(c["j"])({id:t.envelopesRecordId,sendState:6}),this.$toast.clear())}):alert("你的微信版本过低，请更新至最新版本。")}})}else this.errorMsg=e.msg,this.$toast.clear()})},getReceiveList(e){let t={orderNo:this.redPacket.orderId,openId:this.openId,appId:this.redPacket.appId,chatId:this.redPacket.chatId};Object(c["b"])(t).then(({data:t})=>{t.currentAcceptMoney/=100,t.totalMoney/=100,t.accpectMoney/=100,t.accpestCustomerList.forEach(e=>{e.accpectMoney/=100}),this.$toast.clear(),e?(this.opened=!0,Object.assign(this.redPacket,t)):(this.$refs.audio.play(),this.animate=!0,setTimeout(()=>{this.opened=!0,Object.assign(this.redPacket,t)},1e3))})},dateFormat(e){return e.substring(0,19).replace(/[T]/g," ")}}},d=i,o=(a("2783"),a("2877")),u=Object(o["a"])(d,r,s,!1,null,"dd099398",null);t["default"]=u.exports},"105e":function(e,t,a){e.exports=a.p+"img/open.b147c32a.png"},2783:function(e,t,a){"use strict";a("e043")},"74a0":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"i",(function(){return i})),a.d(t,"f",(function(){return d})),a.d(t,"h",(function(){return o})),a.d(t,"g",(function(){return u})),a.d(t,"d",(function(){return p})),a.d(t,"e",(function(){return h})),a.d(t,"c",(function(){return l})),a.d(t,"j",(function(){return v})),a.d(t,"b",(function(){return m}));var r=a("b775");const s=window.sysConfig.services.wecom+"/RedEnvelopes",c=window.sysConfig.services.weChat+"/RedEnvelopes";function n(e){return Object(r["a"])({url:s+"/findCompanyRedEnvelopes",params:{sceneType:e}})}function i(e){return Object(r["a"])({url:s+"/sendReEnvelopesToCustomer",method:"post",data:e})}function d(e){return Object(r["a"])({url:s+"/sendCompanyEnvelopesToCustomer",method:"post",data:e})}function o(e){return Object(r["a"])({url:s+"/sendPersonReEnvelopesToGroup",method:"post",data:e})}function u(e){return Object(r["a"])({url:s+"/sendCompanyEnvelopesToGroup",method:"post",data:e})}function p(){return Object(r["a"])({url:c+"/findRedEnvelopesInfo"})}function h(e){return Object(r["a"])({url:c+"/receiveRedEnvelopes",params:e})}function l(e){return Object(r["a"])({url:c+"/checkRecevieRedEnvelopes",params:e})}function v(e){return Object(r["a"])({url:c+"/updateState",method:"post",data:e})}function m(e){return Object(r["a"])({url:c+"/receiveRedEnvelopesLists",params:e})}},9804:function(e,t,a){e.exports=a.p+"img/redPacketThumb.c1be6334.png"},d4bc:function(e,t,a){e.exports=a.p+"media/redPacket.20416949.mp3"},e043:function(e,t,a){}}]);