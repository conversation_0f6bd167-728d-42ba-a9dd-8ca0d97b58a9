<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeProductOrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.WeProductOrderRefund">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="refund_no" property="refundNo" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_user_id" property="refundUserId" />
        <result column="remark" property="remark" />
        <result column="refund_fee" property="refundFee" />
        <result column="refund_state" property="refundState" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, refund_no, refund_time, refund_user_id, remark, refund_fee, refund_state, create_time, create_by, create_by_id, update_time, update_by, update_by_id, del_flag
    </sql>

</mapper>
