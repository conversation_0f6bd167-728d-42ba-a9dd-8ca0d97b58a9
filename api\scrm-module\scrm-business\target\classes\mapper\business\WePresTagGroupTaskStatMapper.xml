<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WePresTagGroupTaskStatMapper">


    <insert id="batchSave">
        insert into we_pres_tag_group_stat (task_id, user_id, external_userid, sent, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.taskId}, #{item.userId}, #{item.externalUserId}, #{item.sent}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <select id="cropSendResultList" parameterType="org.scrm.domain.taggroup.WePresTagGroupTaskStat" resultType="org.scrm.domain.taggroup.WePresTagGroupTaskStat">
        select distinct
        wc.customer_name as customerName,
        (case wgmsr.`status` when 0 then 0 when 1 then 1 when 2 then 0 when 3 then 1 else 0 end) as sent,
        (
        select count(*) > 0
        from
        we_group_member wgm
        left join we_group_code_actual ac on ac.chat_id =  wgm.chat_id and ac.del_flag = 0
        where ac.group_code_id = task.group_code_id and wgm.user_id = wgmsr.external_userid
        ) as inGroup
        from
        we_group_message_send_result wgmsr
        left join we_customer wc on wc.external_userid = wgmsr.external_userid
        left join we_pres_tag_group task on task.message_template_id = wgmsr.msg_template_id
        where
        wgmsr.del_flag = 0
        and task.task_id = #{taskId}
        <if test="customerName != null and customerName != ''">
            and wc.customer_name like concat('%', #{customerName}, '%')
        </if>
        <if test="(sent != null and sent != '') or sent == 0">
            and (case wgmsr.`status` when 0 then 0 when 1 then 1 when 2 then 0 when 3 then 1 else 0 end) = #{sent}
        </if>
        <if test="(inGroup != null and inGroup != '') or inGroup == 0">
            and  (
            select count(*) > 0
            from
            we_group_member wgm
            left join we_group_code_actual ac on ac.chat_id =  wgm.chat_id and ac.del_flag = 0
            where ac.group_code_id = task.group_code_id and wgm.user_id = wgmsr.external_userid
            ) = #{inGroup}
        </if>
    </select>

    <select id="singleSendResultList" parameterType="org.scrm.domain.taggroup.WePresTagGroupTaskStat" resultType="org.scrm.domain.taggroup.WePresTagGroupTaskStat">
        SELECT DISTINCT
            cu.customer_name AS customerName,
            (
            SELECT count(*) > 0
            FROM we_group_member wgm
            LEFT JOIN we_group_code wgc ON FIND_IN_SET(wgc.chat_id_list,wgm.chat_id)
            LEFt JOIn we_pres_tag_group task On wgc.id = task.group_code_id
            WHERE
            task.task_id = st.task_id AND wgm.user_id = st.external_userid
            ) AS inGroup,
            st.sent
        FROM
        we_pres_tag_group_stat st
        left join we_customer cu on cu.external_userid = st.external_userid and cu.del_flag = 0
        where st.del_flag = 0  and st.task_id = #{taskId}
        <if test="customerName != null and customerName != ''">
            and cu.customer_name like concat('%', #{customerName}, '%')
        </if>
        <if test="(sent != null and sent != '') or sent == 0">
            and st.sent = #{sent}
        </if>
        <if test="(inGroup != null and inGroup != '') or inGroup == 0">
            and (
                SELECT count(*) > 0
                FROM we_group_member wgm
                LEFT JOIN we_group_code wgc ON FIND_IN_SET(wgc.chat_id_list,wgm.chat_id)
                LEFt JOIn we_pres_tag_group task On wgc.id = task.group_code_id
                WHERE
                task.task_id = st.task_id AND wgm.user_id = st.external_userid
            ) = #{inGroup}
        </if>
    </select>
</mapper>