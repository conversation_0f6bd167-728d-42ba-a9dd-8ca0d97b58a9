{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js!D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753236965456}, {"path": "D:\\project\\scrm\\front\\mobile2\\babel.config.js", "mtime": 1750650120014}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 1751130691203}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}