<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCustomerLinkAttachmentsMapper">

    <resultMap id="BaseResultMap" type="org.scrm.domain.WeCustomerLinkAttachments">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="knowCustomerId" column="know_customer_id" jdbcType="BIGINT"/>
            <result property="msgType" column="msg_type" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="mediaId" column="media_id" jdbcType="VARCHAR"/>
            <result property="msgId" column="msg_id" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
            <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
            <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
            <result property="appId" column="app_id" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="realType" column="real_type" jdbcType="TINYINT"/>
            <result property="materialId" column="material_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,know_customer_id,msg_type,
        content,media_id,msg_id,
        title,description,file_url,
        link_url,pic_url,app_id,
        create_by,create_by_id,create_time,
        update_by,update_by_id,update_time,
        real_type,material_id,del_flag
    </sql>
</mapper>
