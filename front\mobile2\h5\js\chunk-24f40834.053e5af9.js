(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-24f40834"],{"19ef":function(t,e,s){"use strict";s.r(e);var n=function(){var t,e=this,n=e._self._c;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"bg"},[n("van-field",{attrs:{readonly:"",label:"",placeholder:"请选择所在地区"},on:{click:function(t){e.addressShow=!0}},model:{value:e.form.area,callback:function(t){e.$set(e.form,"area",t)},expression:"form.area"}}),n("van-field",{attrs:{readonly:"",clickable:"",value:e.currentItem.storeName,placeholder:"请选择门店"},on:{click:function(t){e.showPicker=!0}}}),null!==(t=e.columns)&&void 0!==t&&t.length?[n("div",{staticClass:"tip"},[e._v(" "+e._s(e.welcomeMsg)+" ")]),n("div",{staticClass:"code_content"},[e.currentItem.groupCodeUrl?n("img",{staticClass:"code_img",attrs:{src:e.currentItem.groupCodeUrl,alt:""},on:{touchstart:e.setStart,touchend:e.setEnd}}):n("img",{staticClass:"code_img",attrs:{src:s("9651"),alt:""}})]),e.currentItem.groupCodeUrl?n("div",{staticClass:"sub-des"},[e._v(" 长按识别二维码添加添加门店群 ")]):e._e(),n("div",{staticClass:"tip",staticStyle:{"margin-top":"20px","font-size":"16px"}},[n("van-icon",{attrs:{name:"location-o"}}),e._v(" "+e._s(e.currentItem.address)+" ")],1)]:[n("div",{staticClass:"tip"},[e._v(" "+e._s(e.data.outOfRangeTip)+" ")]),n("div",{staticClass:"code_content"},[1===e.data.codeState?n("img",{staticClass:"code_img",attrs:{src:s("9651"),alt:""}}):n("img",{staticClass:"code_img",attrs:{src:e.data.customerServiceUrl,alt:""},on:{touchstart:e.setStart,touchend:e.setEnd}})]),1!==e.data.codeState?n("div",{staticClass:"sub-des"},[e._v(" 长按识别二维码添加客服 ")]):e._e()],n("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.showPicker,callback:function(t){e.showPicker=t},expression:"showPicker"}},[n("van-picker",{attrs:{"value-key":"storeName","show-toolbar":"",columns:e.columns},on:{cancel:function(t){e.showPicker=!1},confirm:e.onConfirm}})],1),n("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.addressShow,callback:function(t){e.addressShow=t},expression:"addressShow"}},[n("van-cascader",{attrs:{title:"请选择所在地区",options:e.addressOptions,"field-names":{text:"name",value:"name"}},on:{close:function(t){e.addressShow=!1},finish:e.selectedArea},model:{value:e.area,callback:function(t){e.area=t},expression:"area"}})],1)],2)},o=[],i=(s("e9f5"),s("ab43"),s("ed08")),a=s("2934"),r=s("4e79"),d={name:"store-group-code",data(){return{loading:!1,area:"",userInfo:{},tenantId:"",addressOptions:[],addressShow:!1,welcomeMsg:"",form:{area:"",areaId:"",latitude:"",longitude:""},showPicker:!1,columns:[],value:"",data:{outOfRangeTip:"",codeState:1,customerServiceUrl:""},currentItem:{storeName:"",address:""},showTip:!1,query:{currentLat:"",currentLng:"",storeCodeId:"",unionid:"",source:2,tenantId:""}}},methods:{setStart(){this.timer=setTimeout(()=>{this.query.storeCodeId=this.currentItem.id,this.query.unionid=this.userInfo.unionId,this.query.tenantId=sessionStorage.getItem("tenantId")},500)},setEnd(){clearTimeout(this.timer)},selectedArea({selectedOptions:t}){this.addressShow=!1,delete this.form.latitude,delete this.form.longitude,this.form.area=t.map(t=>t.name).join(""),this.getStoreList()},getAreaList(){Object(a["b"])().then(t=>{this.addressOptions=t})},onConfirm(t,e){t&&(this.currentItem=t),this.showPicker=!1},getStoreList(t){this.loading=!0,Object(r["b"])({storeCodeType:2,unionid:this.userInfo.unionId,longitude:this.form.longitude,latitude:this.form.latitude,area:this.form.area,tenantId:sessionStorage.getItem("tenantId")}).then(e=>{if(200===e.code)if(this.welcomeMsg=e.data.welcomeMsg,this.columns=e.data.weStoreCodes||[],this.columns&&0!==this.columns.length)if(t){this.currentItem=this.columns[0],this.form.area=this.currentItem.area;let t=Object(i["g"])(this.currentItem.area);t[2]&&(this.area=[2])}else this.currentItem=this.columns[0];else this.currentItem={storeName:"",address:""},this.getNullFn();this.loading=!1})},getNullFn(){Object(r["a"])({storeCodeType:2,unionid:this.userInfo.unionId,longitude:this.form.longitude,latitude:this.form.latitude,area:this.form.area}).then(t=>{200===t.code&&t.data&&(this.data=t.data)})},back(){document.addEventListener("WeixinJSBridgeReady",(function(){WeixinJSBridge.call("closeWindow")}),!1),WeixinJSBridge.call("closeWindow")}},async mounted(){if(await Object(i["d"])(),Object(i["c"])("code")&&Object(i["c"])("state")){this.userInfo=JSON.parse(sessionStorage.getItem("userinfo")),this.getAreaList();let t=this;this.loading=!0,Object(a["k"])(window.location.href.split("#")[0]).then(e=>{if(200===e.code){let{timestamp:s,nonceStr:n,signature:o}=e.data;wx.config({beta:!0,appId:sessionStorage.getItem("weAppId"),timestamp:s,nonceStr:n,signature:o,jsApiList:["getLocation"],success:function(t){},fail:e=>{t.loading=!1,alert("config失败:"+JSON.stringify(e)),e.errMsg.indexOf("function not exist")>-1&&alert("版本过低请升级")}}),wx.ready((function(){wx.getLocation({type:"wgs84",success:function(e){t.form.latitude=e.latitude,t.form.longitude=e.longitude,t.query.currentLat=e.latitude,t.query.currentLng=e.longitude,t.getStoreList("init")},fail:function(e){console.log("获取定位位置信息失败",e),alert("获取定位位置信息失败"),t.loading=!1},cancel:function(e){t.back()}})}))}})}},created(){this.tenantId=this.$route.query.tenantId,sessionStorage.setItem("tenantId",this.tenantId)}},c=d,u=(s("4fd1"),s("2877")),l=Object(u["a"])(c,n,o,!1,null,"279d251e",null);e["default"]=l.exports},"4e79":function(t,e,s){"use strict";s.d(e,"c",(function(){return i})),s.d(e,"d",(function(){return a})),s.d(e,"e",(function(){return r})),s.d(e,"b",(function(){return d})),s.d(e,"a",(function(){return c}));var n=s("b775");const o=window.sysConfig.services.weChat;function i(t){return Object(n["a"])({url:o+"/groupCode/getActualCode/"+t})}function a(t){return Object(n["a"])({url:o+"/groupCode/findWeCommunityNewGroupById/"+t})}function r(t){return Object(n["a"])({url:o+"/groupCode/findWePresTagGroupById/"+t})}function d(t){return Object(n["a"])({url:o+"/storeCode/findStoreCode",params:t})}function c(t){return Object(n["a"])({url:o+"/storeCode/findWeStoreCodeConfig",params:t})}},"4fd1":function(t,e,s){"use strict";s("8d10")},"8d10":function(t,e,s){},9651:function(t,e,s){t.exports=s.p+"img/no_code.821c4c8d.svg"}}]);