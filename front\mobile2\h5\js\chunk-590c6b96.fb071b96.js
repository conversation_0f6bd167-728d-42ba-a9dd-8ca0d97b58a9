(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-590c6b96"],{"33d1":function(t,e,n){"use strict";n("8c4a")},3415:function(t,e,n){"use strict";n("f1fa")},7998:function(t,e,n){"use strict";n.d(e,"c",(function(){return a})),n.d(e,"b",(function(){return s})),n.d(e,"a",(function(){return c}));var r=n("b775");const o=window.sysConfig.services.wecom,i=o+"/community/h5";function a(t,e){return Object(r["a"])({url:i+"/"+t,params:{type:e}})}function s(t,e){return Object(r["a"])({url:i+"/scope/"+t,params:{type:e}})}function c(t,e,n){return Object(r["a"])({url:i+"/changeStatus",params:{emplId:t,taskId:e,type:n}})}},"8c4a":function(t,e,n){},af15:function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t._self._c;return e("div",[e("van-tabs",{model:{value:t.isDone,callback:function(e){t.isDone=e},expression:"isDone"}},[e("van-tab",{attrs:{title:"待处理("+t.todo.length+")",name:0}}),e("van-tab",{attrs:{title:"已处理("+t.done.length+")",name:1}})],1),e("div",{staticClass:"type-area"},[e("van-button",{attrs:{round:"",size:"small",type:0===t.taskType?"info":"default"},on:{click:function(e){t.taskType=0}}},[t._v("全部")]),e("van-button",{attrs:{round:"",size:"small",type:2===t.taskType?"info":"default"},on:{click:function(e){t.taskType=2}}},[t._v(" 群SOP ")])],1),t.tasks&&0!==t.tasks.length?[t._l(t.tasks,(function(n){return[[e("Sop",{key:n.ruleId,attrs:{task:n,state:!!t.isDone},on:{refresh:t.getTasks}})]]}))]:[e("van-empty",{attrs:{description:"暂无数据"}})],e("van-overlay",{attrs:{show:t.loading,"class-name":"overlay"}},[e("van-loading",{attrs:{type:"spinner"}})],1)],2)},o=[],i=n("7998"),a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"task-wrapper"},[e("div",{staticClass:"header"},[e("span",[t._v(" 群SOP ")]),e("div",[t._v(t._s(t.task.createTime))])]),e("div",{staticClass:"group"},[e("van-cell",{staticClass:"group-tags",attrs:{title:"发送给客户群"},scopedSlots:t._u([{key:"default",fn:function(){return[t.task.groupList&&t.task.groupList.length>0?t._l(t.task.groupList.slice(0,2),(function(n){return e("van-tag",{key:n.chatId,attrs:{size:"large"}},[t._v(" "+t._s(n.groupName)+" ")])})):t._e(),t.task.groupList&&t.task.groupList.length>2?[e("van-tag",{attrs:{size:"large"}},[t._v(" ... ")])]:t._e()]},proxy:!0}])}),e("van-cell",{attrs:{title:"发送时间",value:t.sendRange}})],1),e("div",{staticClass:"content"},[e("div",[t._v(" "+t._s(t.task.content)+" ")]),e("div",{staticClass:"copy-wrapper"},[e("van-button",{class:"copy-btn_"+t.task.ruleId,attrs:{"data-clipboard-text":t.task.content}},[t._v(" 复制 ")])],1)]),e("div",{staticClass:"image-list"},[t._l(t.task.materialList,(function(n){return[0===n.mediaType?e("van-image",{key:n.id,attrs:{width:"80",src:n.materialUrl}}):t._e()]})),t._l(t.task.picList,(function(t){return[e("van-image",{key:t,attrs:{width:"80",src:t}})]}))],2),e("div",[e("van-cell",{attrs:{title:"已完成成员","is-link":"",value:t.showDone},on:{click:function(e){return t.goState(1)}}}),e("van-cell",{attrs:{title:"未完成成员","is-link":"",value:t.showTodo},on:{click:t.goState}})],1),t.state?t._e():e("div",{staticClass:"send-button"},[e("van-button",{attrs:{type:"info",size:"mini"},on:{click:function(e){return t.send()}}},[t._v("发送")])],1)]),e("div",{staticClass:"bottom-line"})])},s=[],c=(n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("b311")),u=n.n(c),l={name:"SOPPanel",props:{task:{type:Object,required:!0},state:{type:Boolean,required:!0},isAdmin:{type:Boolean,default:!0}},data(){return{}},methods:{goState(t){this.isAdmin&&this.$router.push({name:"taskState",query:{taskId:this.task.ruleId,active:1===t?1:0,taskType:2}})},send(){try{const t=this.$store.state&&this.$store.state.userId,e=this.task.ruleId;let n=this;this.$toast.loading({message:"正在发送...",duration:0,forbidClick:!0}),wx.invoke("shareToExternalChat",{title:this.task.title,desc:this.task.content,link:this.task.materialList&&this.task.materialList[0]&&this.task.materialList[0].materialUrl||" ",imgUrl:this.task.materialList&&this.task.materialList[0]&&this.task.materialList[0].materialUrl||" "},(async function(r){if("shareToExternalChat:ok"==r.err_msg){try{await Object(i["a"])(t,e,1)}catch(o){return n.$toast.clear(),void n.$dialog({message:"接口失败："+JSON.stringify(o)})}n.$toast.clear(),this.$emit("refresh")}else"shareToExternalChat:cancel"==r.err_msg||n.$dialog({message:"发送失败："+JSON.stringify(r)});n.$toast.clear()}))}catch(t){this.$dialog({message:"代码错误："+JSON.stringify(t.message)})}}},computed:{sendRange(){return this.task.startExeTime&&this.task.stopExeTime?this.task.startExeTime+" - "+this.task.stopExeTime:""},todoMembers(){if(!(this.task&&this.task.scopeList&&this.task.scopeList.length>0))return[];const t=[];for(let e of this.task.scopeList)e.done||t.push(e);return t},showTodo(){if(0===this.todoMembers.length)return"无";const t=this.todoMembers.map(t=>t.name||""),e=t.filter(t=>""!==t);return this.todoMembers.length<=2?e.join("、"):e[0]+"、"+e[1]+"等"+this.todoMembers.length+"人"},doneMembers(){if(!this.task||!this.task.scopeList||0===this.task.scopeList.length)return[];const t=[];for(let e of this.task.scopeList)e.done&&t.push(e);return t},showDone(){if(0===this.doneMembers.length)return"无";const t=this.doneMembers.map(t=>t.name||""),e=t.filter(t=>""!==t);return this.doneMembers.length<=2?e.join("、"):e[0]+"、"+e[1]+"等"+this.doneMembers.length+"人"}},mounted(){this.clipboard=new u.a(".copy-btn_"+this.task.ruleId),this.clipboard.on("success",t=>{this.showCopy=!1}),this.clipboard.on("error",t=>{this.showCopy=!1})}},f=l,d=(n("33d1"),n("2877")),p=Object(d["a"])(f,a,s,!1,null,"8b439cb0",null),h=p.exports,y={components:{Sop:h},data(){return{isDone:0,taskType:0,loading:!1,todo:[],done:[]}},methods:{getTasks(){this.loading=!0,Object(i["c"])(this.$store.state.userId,this.taskType).then(({data:t})=>{this.todo=t.todo||[],this.done=t.done||[],this.loading=!1}).catch(t=>{this.loading=!1})}},computed:{userId(){return this.$store.state.userId},tasks(){return 1===this.isDone?this.done:this.todo}},watch:{taskType(t){this.userId&&this.getTasks()},userId(t){t&&this.getTasks()}},created(){this.taskType=parseInt(this.$route.query.type)}},v=y,m=(n("3415"),Object(d["a"])(v,r,o,!1,null,"55742598",null));e["default"]=m.exports},b311:function(t,e,n){
/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */
(function(e,n){t.exports=n()})(0,(function(){return function(){var t={686:function(t,e,n){"use strict";n.d(e,{default:function(){return I}});var r=n(279),o=n.n(r),i=n(370),a=n.n(i),s=n(817),c=n.n(s);function u(t){try{return document.execCommand(t)}catch(e){return!1}}var l=function(t){var e=c()(t);return u("cut"),e},f=l;function d(t){var e="rtl"===document.documentElement.getAttribute("dir"),n=document.createElement("textarea");n.style.fontSize="12pt",n.style.border="0",n.style.padding="0",n.style.margin="0",n.style.position="absolute",n.style[e?"right":"left"]="-9999px";var r=window.pageYOffset||document.documentElement.scrollTop;return n.style.top="".concat(r,"px"),n.setAttribute("readonly",""),n.value=t,n}var p=function(t,e){var n=d(t);e.container.appendChild(n);var r=c()(n);return u("copy"),n.remove(),r},h=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body},n="";return"string"===typeof t?n=p(t,e):t instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(null===t||void 0===t?void 0:t.type)?n=p(t.value,e):(n=c()(t),u("copy")),n},y=h;function v(t){return v="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},v(t)}var m=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.action,n=void 0===e?"copy":e,r=t.container,o=t.target,i=t.text;if("copy"!==n&&"cut"!==n)throw new Error('Invalid "action" value, use either "copy" or "cut"');if(void 0!==o){if(!o||"object"!==v(o)||1!==o.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===n&&o.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===n&&(o.hasAttribute("readonly")||o.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes')}return i?y(i,{container:r}):o?"cut"===n?f(o):y(o,{container:r}):void 0},g=m;function b(t){return b="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function k(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function w(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function T(t,e,n){return e&&w(t.prototype,e),n&&w(t,n),t}function S(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&x(t,e)}function x(t,e){return x=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},x(t,e)}function E(t){var e=O();return function(){var n,r=C(t);if(e){var o=C(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return _(this,n)}}function _(t,e){return!e||"object"!==b(e)&&"function"!==typeof e?L(t):e}function L(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function O(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function C(t){return C=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},C(t)}function j(t,e){var n="data-clipboard-".concat(t);if(e.hasAttribute(n))return e.getAttribute(n)}var A=function(t){S(n,t);var e=E(n);function n(t,r){var o;return k(this,n),o=e.call(this),o.resolveOptions(r),o.listenClick(t),o}return T(n,[{key:"resolveOptions",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.action="function"===typeof t.action?t.action:this.defaultAction,this.target="function"===typeof t.target?t.target:this.defaultTarget,this.text="function"===typeof t.text?t.text:this.defaultText,this.container="object"===b(t.container)?t.container:document.body}},{key:"listenClick",value:function(t){var e=this;this.listener=a()(t,"click",(function(t){return e.onClick(t)}))}},{key:"onClick",value:function(t){var e=t.delegateTarget||t.currentTarget,n=this.action(e)||"copy",r=g({action:n,container:this.container,target:this.target(e),text:this.text(e)});this.emit(r?"success":"error",{action:n,text:r,trigger:e,clearSelection:function(){e&&e.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(t){return j("action",t)}},{key:"defaultTarget",value:function(t){var e=j("target",t);if(e)return document.querySelector(e)}},{key:"defaultText",value:function(t){return j("text",t)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{container:document.body};return y(t,e)}},{key:"cut",value:function(t){return f(t)}},{key:"isSupported",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["copy","cut"],e="string"===typeof t?[t]:t,n=!!document.queryCommandSupported;return e.forEach((function(t){n=n&&!!document.queryCommandSupported(t)})),n}}]),n}(o()),I=A},828:function(t){var e=9;if("undefined"!==typeof Element&&!Element.prototype.matches){var n=Element.prototype;n.matches=n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector}function r(t,n){while(t&&t.nodeType!==e){if("function"===typeof t.matches&&t.matches(n))return t;t=t.parentNode}}t.exports=r},438:function(t,e,n){var r=n(828);function o(t,e,n,r,o){var i=a.apply(this,arguments);return t.addEventListener(n,i,o),{destroy:function(){t.removeEventListener(n,i,o)}}}function i(t,e,n,r,i){return"function"===typeof t.addEventListener?o.apply(null,arguments):"function"===typeof n?o.bind(null,document).apply(null,arguments):("string"===typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,(function(t){return o(t,e,n,r,i)})))}function a(t,e,n,o){return function(n){n.delegateTarget=r(n.target,e),n.delegateTarget&&o.call(t,n)}}t.exports=i},879:function(t,e){e.node=function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},e.nodeList=function(t){var n=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===n||"[object HTMLCollection]"===n)&&"length"in t&&(0===t.length||e.node(t[0]))},e.string=function(t){return"string"===typeof t||t instanceof String},e.fn=function(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e}},370:function(t,e,n){var r=n(879),o=n(438);function i(t,e,n){if(!t&&!e&&!n)throw new Error("Missing required arguments");if(!r.string(e))throw new TypeError("Second argument must be a String");if(!r.fn(n))throw new TypeError("Third argument must be a Function");if(r.node(t))return a(t,e,n);if(r.nodeList(t))return s(t,e,n);if(r.string(t))return c(t,e,n);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function a(t,e,n){return t.addEventListener(e,n),{destroy:function(){t.removeEventListener(e,n)}}}function s(t,e,n){return Array.prototype.forEach.call(t,(function(t){t.addEventListener(e,n)})),{destroy:function(){Array.prototype.forEach.call(t,(function(t){t.removeEventListener(e,n)}))}}}function c(t,e,n){return o(document.body,t,e,n)}t.exports=i},817:function(t){function e(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var n=t.hasAttribute("readonly");n||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),n||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var r=window.getSelection(),o=document.createRange();o.selectNodeContents(t),r.removeAllRanges(),r.addRange(o),e=r.toString()}return e}t.exports=e},279:function(t){function e(){}e.prototype={on:function(t,e,n){var r=this.e||(this.e={});return(r[t]||(r[t]=[])).push({fn:e,ctx:n}),this},once:function(t,e,n){var r=this;function o(){r.off(t,o),e.apply(n,arguments)}return o._=e,this.on(t,o,n)},emit:function(t){var e=[].slice.call(arguments,1),n=((this.e||(this.e={}))[t]||[]).slice(),r=0,o=n.length;for(r;r<o;r++)n[r].fn.apply(n[r].ctx,e);return this},off:function(t,e){var n=this.e||(this.e={}),r=n[t],o=[];if(r&&e)for(var i=0,a=r.length;i<a;i++)r[i].fn!==e&&r[i].fn._!==e&&o.push(r[i]);return o.length?n[t]=o:delete n[t],this}},t.exports=e,t.exports.TinyEmitter=e}},e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}return function(){n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,{a:e}),e}}(),function(){n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}}(),function(){n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),n(686)}().default}))},f1fa:function(t,e,n){}}]);