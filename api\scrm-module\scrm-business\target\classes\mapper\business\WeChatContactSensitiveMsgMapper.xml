<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeChatContactSensitiveMsgMapper">



    <resultMap type="org.scrm.domain.WeChatContactSensitiveMsg" id="WeChatContactSensitiveMsgResult">
                <result property="id" column="id" jdbcType="INTEGER"/>
                <result property="msgId" column="msg_id" jdbcType="VARCHAR"/>
                <result property="sendStatus" column="send_status" jdbcType="INTEGER"/>
                <result property="patternWords" column="pattern_words" jdbcType="VARCHAR"/>
                <result property="content" column="content" jdbcType="VARCHAR"/>
                <result property="fromId" column="from_id" jdbcType="VARCHAR"/>
                <result property="msgTime" column="msg_time" jdbcType="TIMESTAMP"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
                <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            </resultMap>

    <sql id="selectWeChatContactSensitiveMsgVo">
        select id, msg_id, send_status, pattern_words, content, from_id, msg_time, create_time, create_by, create_by_id, update_time, update_by, update_by_id, del_flag from we_chat_contact_sensitive_msg
    </sql>

    <select id="getListByQuery" resultType="org.scrm.domain.msgaudit.vo.WeChatContactSensitiveMsgVo">
        select distinct wccsm.id,
               wccsm.msg_id,
               wccsm.send_status as status,
               wccsm.pattern_words,
               wccsm.content,
               wccsm.from_id,
               wccsm.msg_time,
               su.user_name as from_name
        from we_chat_contact_sensitive_msg wccsm
        inner join sys_user su on wccsm.from_id = su.we_user_id and su.del_flag = 0
        <where>
            <if test="scopeType != null and scopeType == 1">
                and wccsm.from_id = #{auditScopeId}
            </if>
            <if test="scopeType != null and scopeType == 2">
                and (su.dept_id = #{auditScopeId} OR su.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE FIND_IN_SET
                (#{auditScopeId},ancestors)))
            </if>
            <if test="keyword != null and keyword != ''">
                and wccsm.pattern_words like concat('%', #{keyword}, '%')
            </if>
        </where>
        order by wccsm.msg_time desc
    </select>

</mapper>
