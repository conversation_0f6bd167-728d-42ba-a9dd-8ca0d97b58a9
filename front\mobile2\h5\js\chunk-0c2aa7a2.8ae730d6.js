(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0c2aa7a2"],{"1d11":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t._self._c;return e("van-tabs",[e("van-tab",{attrs:{title:`未读消息(${t.num})`,name:"0"}},[e("List",{ref:"listunread",attrs:{type:"unread"},on:{read:t.getNum}})],1),e("van-tab",{attrs:{title:"已读",name:"1"}},[e("List",{ref:"listread",attrs:{type:"read"}})],1)],1)},r=[],s=n("764a"),u={name:"",components:{List:()=>n.e("chunk-99ade3ee").then(n.bind(null,"753e"))},data(){return{num:0}},computed:{},watch:{},created(){this.getNum()},mounted(){},methods:{getNum(){Object(s["b"])().then(({data:t})=>{this.num=t}),this.$refs.listunread.$refs.loadList.getList(1),this.$refs.listread.$refs.loadList.getList(1)}}},i=u,c=n("2877"),d=Object(c["a"])(i,a,r,!1,null,"c23e701a",null);e["default"]=d.exports},"764a":function(t,e,n){"use strict";n.d(e,"a",(function(){return d})),n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return f}));var a=n("b775");const{get:r,post:s,put:u,del:i}=a["b"],c="/message/notification";function d(t){return r(`${c}/${t.type}/list`)}function o(t){return r(c+"/num")}function f(){return s(c+"/read")}}}]);