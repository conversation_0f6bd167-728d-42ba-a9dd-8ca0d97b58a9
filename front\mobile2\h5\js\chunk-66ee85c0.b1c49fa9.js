(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-66ee85c0"],{"23ba":function(t,e,i){"use strict";i.d(e,"c",(function(){return a})),i.d(e,"e",(function(){return c})),i.d(e,"g",(function(){return u})),i.d(e,"h",(function(){return o})),i.d(e,"a",(function(){return d})),i.d(e,"f",(function(){return l})),i.d(e,"b",(function(){return h})),i.d(e,"d",(function(){return f}));var n=i("b775");const s=window.sysConfig.services.wecom,r=s+"/groupchat";function a(t){return Object(n["a"])({url:r+"/get/"+t})}function c(t){return Object(n["a"])({url:r+"/member/page/list",params:t})}function u(t){return Object(n["a"])({url:r+"/makeGroupTag",method:"post",data:t})}function o(){return Object(n["a"])({url:r+"/synch"})}function d({pageNum:t,pageSize:e,chatId:i}){return Object(n["a"])({url:r+"/findGroupTrajectory/"+i,params:{pageNum:t,pageSize:e}})}function l(t){return Object(n["a"])({url:s+"/tag/list",params:t})}function h(t){return Object(n["a"])({url:s+"/customer/findWeCustomerListByApp",params:t})}function f(t){return Object(n["a"])({url:s+"/groupchat/page/listByApp",params:t})}},"7cc0":function(t,e,i){"use strict";i("d21c")},"9d91":function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page"},[e("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:function(e){return t.getList(1)}},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":t.finishedText,error:t.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(e){t.error=e},load:function(e){return t.getList()}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[t.list.length?t._l(t.list,(function(i,n){return e("div",{key:n,staticClass:"content card flex"},[e("van-image",{staticClass:"img",attrs:{src:i.avatar}}),e("div",{staticStyle:{flex:"auto"}},[e("div",{staticClass:"fxbw aic"},[e("div",[e("span",{staticClass:"name"},[t._v(t._s(i.name))]),e("span",{style:{color:1===i.customerType?"#00D695":"#0079DE"}},[t._v(" "+t._s({1:"@微信",2:"@企业"}[i.customerType])+" ")])]),e("div",{staticClass:"label active"},[t._v(t._s(t.type[i.type]))])]),e("div",{},[t._v("入群时间："+t._s(t.dateFormat(i.joinTime)))]),e("div",{staticClass:"fxbw"},[e("div",{},[t._v("入群方式："+t._s(t.joinScene[i.joinScene]))]),e("div",{staticClass:"c9"},[t._v("邀请人："+t._s(i.invitorUserName))])])])],1)})):t._e()],2)],1)],1)},s=[],r=(i("14d9"),i("23ba")),a=i("ed08"),c={name:"",components:{},data(){return{refreshing:!1,loading:!1,finished:!1,finishedText:"暂无数据",error:!1,query:{pageNum:1,pageSize:10,chatId:"sdfsdf"},list:[],joinScene:{1:"直接邀请入群",2:"通过邀请链接入群",3:"通过扫描群二维码入群"},type:{1:"成员",2:"客户",3:"群主"},dateFormat:a["b"]}},computed:{},watch:{},created(){let t=this.$route.query;this.query.chatId=t&&t.id,this.getList()},mounted(){},methods:{getList(t){this.loading=!0,this.finished=!1,t&&(this.query.pageNum=t),1==t&&(this.list=[]),this.refreshing&&(this.list=[],this.refreshing=!1),Object(r["e"])(this.query).then(({rows:t,total:e})=>{this.list.push(...t),this.loading=!1,this.refreshing=!1,this.list.length>=+e?(0==this.list.length?(this.query.pageNum=1,this.finishedText="暂无数据"):this.finishedText="没有更多了",this.finished=!0):this.query.pageNum++}).catch(t=>{this.loading=!1,this.finished=!0,this.error=!0,alert(t)})}}},u=c,o=(i("7cc0"),i("2877")),d=Object(o["a"])(u,n,s,!1,null,"f412735a",null);e["default"]=d.exports},d21c:function(t,e,i){}}]);