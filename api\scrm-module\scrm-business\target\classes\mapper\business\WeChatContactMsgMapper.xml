<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeChatContactMsgMapper">

    <resultMap type="org.scrm.domain.WeChatContactMsg" id="WeChatContactMsgResult">
        <result property="id" column="id"/>
        <result property="msgId" column="msg_id"/>
        <result property="fromId" column="from_id"/>
        <result property="toList" column="to_list"/>
        <result property="roomId" column="room_id"/>
        <result property="action" column="action"/>
        <result property="msgType" column="msg_type"/>
        <result property="msgTime" column="msg_time"/>
        <result property="contact" column="contact"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="chatSql">select msg_id,
                             from_id,
                             to_list as receiver,
                             `action`,
                             msg_time,
                             msg_type,
                             is_external,
                             contact
                      from we_chat_contact_msg</sql>

    <select id="selectExternalChatList" resultType="org.scrm.domain.msgaudit.vo.WeChatContactMsgVo">
        select t.*, wc.customer_name as name, wc.avatar
        from (
                 select receiver, msg_time, `action`, msg_type, contact
                 from (
                          select to_list as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where from_id = #{fromId}
                            and is_external = 0
                            and room_id = ''
                          union
                          select from_id as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where FIND_IN_SET(#{fromId}, to_list)
                            and is_external = 0
                            and room_id = ''
                          order by msg_time desc
                      ) as msg
                 group by receiver
                 order by msg.msg_time desc) t
                 inner join we_customer wc on t.receiver = wc.external_userid and wc.add_user_id = #{fromId}
    </select>

    <select id="selectAloneChatList" resultType="org.scrm.domain.msgaudit.vo.WeChatContactMsgVo">
        select t.*, ifnull(wu.user_name, wc.customer_name) as name, ifnull(wu.avatar, wc.avatar) as avatar
        from (select receiver, msg_time, `action`, msg_type, contact
        from (select to_list as receiver, `action`, msg_time, msg_type, contact from we_chat_contact_msg
        where from_id = #{fromId}
        and room_id = ''
        <if test="contact != null and contact != ''">
            and contact like concat('%', #{contact}, '%')
        </if>
        union
        select from_id as receiver, `action`, msg_time, msg_type, contact from we_chat_contact_msg
        where FIND_IN_SET(#{fromId}, to_list)
        and room_id = ''
        <if test="contact != null and contact != ''">
            and contact like concat('%', #{contact}, '%')
        </if>
        order by msg_time desc) as msg
        group by receiver order by msg.msg_time desc) as t
        left join sys_user wu on t.receiver = wu.we_user_id and (case when LOCATE('wm', t.receiver) or LOCATE('wo',
        t.receiver) then 0 else 1 end) = 1
        left join we_customer wc on t.receiver = wc.external_userid and (case when LOCATE('wm', t.receiver) or
        LOCATE('wo', t.receiver) then 0 else 1 end) = 0;
    </select>


    <select id="selectInternalChatList" resultType="org.scrm.domain.msgaudit.vo.WeChatContactMsgVo">
        select t.*, wu.user_name as name, wu.avatar
        from (
                 select receiver, msg_time, `action`, msg_type, contact
                 from (
                          select to_list as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where from_id = #{fromId}
                            and is_external = 1
                            and room_id = ''
                          union
                          select from_id as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where FIND_IN_SET(#{fromId}, to_list)
                            and is_external = 1
                            and room_id = ''
                          order by msg_time desc
                      ) as msg
                 group by receiver
                 order by msg.msg_time desc) t
                 inner join sys_user wu on t.receiver = wu.we_user_id
    </select>

    <select id="selectGroupChatList" resultType="org.scrm.domain.msgaudit.vo.WeChatContactMsgVo">
        select t.*, wg.group_name as name, '' as avatar
        from (
                 select receiver, msg_time, `action`, msg_type, contact
                 from (
                          select room_id as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where from_id = #{fromId}
                            and room_id &lt;&gt; ''
                          union
                          select room_id as receiver, `action`, msg_time, msg_type, contact
                          from we_chat_contact_msg
                          where FIND_IN_SET(#{fromId}, to_list)
                            and room_id &lt;&gt; ''
                          order by msg_time desc
                      ) as msg
                 group by receiver
                 order by msg.msg_time desc) t
                 inner join we_group wg on t.receiver = wg.chat_id
    </select>

    <select id="selectFullSearchChatList" resultType="org.scrm.domain.msgaudit.vo.WeChatContactMsgVo">
        select
        from_id,
        msg_time,
        room_id,
        `action`,
        msg_type,
        contact
        from we_chat_contact_msg t
        where t.del_flg=0
            <if test="fromId != null and fromId != ''">
                and (t.from_id = #{fromId} or t.to_list = #{fromId})
            </if>

            <if test="toList != null and toList != ''">
                and (t.to_list = #{toList} or t.from_id = #{toList})
            </if>
            <choose>
                <when test="roomId != null and roomId != ''">
                    and t.room_id = #{roomId}
                </when>
                <when test="fullSearch == 1">

                </when>
                <otherwise>
                    and t.room_id = ''
                </otherwise>
            </choose>

            <if test="msgType != null and msgType != ''">
                and find_in_set(t.msg_type,#{msgType})
            </if>

            <if test="action != null and action != ''">
                and t.action = #{action}
            </if>
            <if test="contact != null and contact != ''">
                and t.contact like concat('%', #{contact}, '%')
            </if>

            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND date_format(t.msg_time, '%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            </if>

            <if test="endTime != null"><!-- 结束时间检索 -->
                AND date_format(t.msg_time, '%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
            </if>

            <if test="userName != null and userName != ''">
                and exists(select 1 from sys_user su where su.user_name like concat('%', #{userName}, '%') and t.from_id = su.we_user_id)
            </if>

            <if test="customerName != null and customerName != ''">
                and (exists(select 1 from we_group_member wgm where wgm.name like concat('%', #{customerName}, '%') and
                t.from_id = wgm.user_id and wgm.type = 2)
                or exists(select 1 from we_customer wc where wc.customer_name like concat('%', #{customerName}, '%') and
                t.from_id = wc.external_userid ))
            </if>
    </select>

    <select id="findIYqueMsgAuditDto" resultType="org.scrm.domain.hotword.dto.IYqueMsgAuditDto">
        SELECT
            msg.msg_id as msgId,
            msg.from_id as fromId,
            CASE
                WHEN msg.from_id LIKE 'wm%' OR msg.from_id LIKE 'wo%' THEN
                    (SELECT GROUP_CONCAT(DISTINCT wc.customer_name) FROM we_customer wc WHERE wc.external_userid = msg.from_id)
                ELSE
                    (SELECT GROUP_CONCAT(DISTINCT su.user_name)  FROM sys_user su WHERE su.we_user_id = msg.from_id)
                END AS fromName,
            msg.to_list as acceptId,
            CASE
                WHEN msg.to_list LIKE 'wm%' OR msg.to_list LIKE 'wo%' THEN
                    (SELECT GROUP_CONCAT(DISTINCT wc.customer_name) FROM we_customer wc WHERE wc.external_userid = msg.to_list)
                ELSE
                    (SELECT GROUP_CONCAT(DISTINCT su.user_name)  FROM sys_user su WHERE su.we_user_id = msg.to_list)
                END AS acceptName,
            msg.msg_type as msgType,
            msg.msg_time as msgTime,
            REPLACE(
                    SUBSTRING(
                            contact,
                            LOCATE('"content":"', contact) + 11,
                            LOCATE('"', contact, LOCATE('"content":"', contact) + 11) - (LOCATE('"content":"', contact) + 11)
                    ),
                    '\\"', '"'
            ) AS content
        FROM
            we_chat_contact_msg msg
        <where>
            <if test="msgAuditQuery.msgType != null and msgAuditQuery.msgType !=''">
                msg.msg_type=#{msgAuditQuery.msgType}
            </if>
            <if test="msgAuditQuery.beginTime !=null and msgAuditQuery.beginTime != '' and msgAuditQuery.endTime !='' and msgAuditQuery.endTime != null">
                AND  date_format( msg.msg_time,'%Y-%m-%d') BETWEEN #{msgAuditQuery.beginTime} AND #{msgAuditQuery.endTime}
            </if>
        </where>
    </select>
</mapper>
