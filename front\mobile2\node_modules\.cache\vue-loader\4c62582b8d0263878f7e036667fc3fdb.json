{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=style&index=0&id=271a595a&prod&lang=less&scoped=true", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753176720619}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751130694554}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751130710731}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751130698336}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1751130697755}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\style-resources-loader\\lib\\index.js", "mtime": 1751130698073}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}