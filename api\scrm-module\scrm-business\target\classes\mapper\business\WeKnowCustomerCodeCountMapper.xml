<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeKnowCustomerCodeCountMapper">



    <select id="findWeKnowCustomerCountTrend" resultType="org.scrm.domain.know.vo.WeKnowCustomerCountTrendOrTableVo">
        SELECT date,
            (
                    SELECT
                    COUNT(DISTINCT external_userid)
                    FROM
                    we_customer
                    WHERE
                    del_flag = 0
                    AND track_state != 5
                    AND DATE ( add_time )= date
                    AND state=#{state}
            ) AS addCustomerNumber,
            (
                    SELECT
                      COUNT(DISTINCT external_userid)
                    FROM
                    we_customer
                    WHERE
                    ( track_state = 5 OR del_flag = 1 )
                    AND DATE ( add_time )= date
                    AND state=#{state}
                        ) AS lostCustomerNumber,
            (
                SELECT
                  COUNT(DISTINCT unionid)
                FROM
                we_know_customer_code_count
                WHERE
                DATE ( create_time )= date
                AND new_or_old = 0
                AND know_customer_id=#{knowCustomerId}
                ) AS newCustomerScanNumber,
            (
                SELECT
                 COUNT(DISTINCT unionid)
                FROM
                we_know_customer_code_count
                WHERE
                DATE ( create_time )= date
                AND new_or_old = 1
                AND know_customer_id=#{knowCustomerId}
                ) AS oldCustomerScanNumber
        FROM
            sys_dim_date
        WHERE
            date_format( date, '%Y-%m-%d' )  BETWEEN #{beginTime} AND #{endTime}
        ORDER BY date ASC
    </select>




</mapper>
