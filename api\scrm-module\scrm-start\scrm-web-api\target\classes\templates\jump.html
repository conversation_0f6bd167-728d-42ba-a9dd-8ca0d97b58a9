<html>

<head>
  <!-- 此文件提供给短链后端部署 -->

  <title>打开小程序</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1, maximum-scale=1">
  <script>
    window.data = {}
    window.onerror = e => {
      console.error(e)
      alert('发生错误' + e)
    }
  </script>
  <!-- weui 样式 -->
  <link rel="stylesheet" href="https://res.wx.qq.com/open/libs/weui/2.4.1/weui.min.css">
  </link>
  <!-- 调试用的移动端 console -->
  <!-- <script src="https://cdn.jsdelivr.net/npm/eruda"></script> -->
  <!-- <script>eruda.init();</script> -->
  <!-- 公众号 JSSDK -->
  <!-- <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script> -->
  <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js"></script>
  <script>
    function docReady(fn) {
      if (document.readyState === 'complete' || document.readyState === 'interactive') {
        fn()
      } else {
        document.addEventListener('DOMContentLoaded', fn);
      }
    }

    docReady(async function () {
      var ua = navigator.userAgent.toLowerCase()
      var isWXWork = ua.match(/wxwork/i) == 'wxwork'
      var isWeixin = !isWXWork && ua.match(/micromessenger/i) == 'micromessenger'
      var isMobile = false
      var isDesktop = false
      if (navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|IEMobile)/i)) {
        isMobile = true
      } else {
        isDesktop = true
      }

      //  "type":"0-文章 1-公众号二维码 2-个人二维码 3-群二维码 4-员工活码 5-客群活码 6-门店导购活码 7-个人小程序 8-门店群活码 9-企业小程序 10-小程序二维码"
      if (window.data.errorMsg) {
        alert('发生错误：' + window.data.errorMsg)
        return
      } else if (window.data.type == 0) {
        location.href = (window.data.linkPath)
        return
      }

      if (isWeixin) {

        if ([5, 6, 8].includes(+window.data.type)) {
          var canvas = document.createElement('canvas')
          var ctx = canvas.getContext('2d')
          var img = new Image()
          img.setAttribute('crossOrigin', 'anonymous')
          img.onload = function () {
            canvas.width = img.width
            canvas.height = img.height
            ctx.drawImage(img, 0, 0)
            var imageData = ctx.getImageData(0, 0, img.width, img.height)
            var code = jsQR(imageData.data, imageData.width, imageData.height, {
              inversionAttempts: "dontInvert",
            })

            location.href = ((code && code.data) || window.data.url_scheme)
          }
          img.src = window.data.qrCode
          return
        }

        // var containerEl = document.getElementById('wechat-web-container')
        // containerEl.classList.remove('hidden')
        // containerEl.classList.add('full', 'wechat-web-container')

        // var launchBtn = document.getElementById('launch-btn')
        // launchBtn.addEventListener('ready', function (e) {
        //   console.log('开放标签 ready')
        // })
        // launchBtn.addEventListener('launch', function (e) {
        //   console.log('开放标签 success')
        // })
        // launchBtn.addEventListener('error', function (e) {
        //   console.log('开放标签 fail', e.detail)
        // })

        // wx.config({
        //   debug: true, // 调试时可开启
        //   appId: 'wx2567dbb9d3fd9556', // <!-- replace -->
        //   timestamp: 0, // 必填，填任意数字即可
        //   nonceStr: 'nonceStr', // 必填，填任意非空字符串即可
        //   signature: 'signature', // 必填，填任意非空字符串即可
        //   jsApiList: ['chooseImage'], // 必填，随意一个接口即可
        //   openTagList: ['wx-open-launch-weapp'], // 填入打开小程序的开放标签名
        // })
      }
      if (isDesktop) {
        // 在 pc 上则给提示引导到手机端打开
        var containerEl = document.getElementById('desktop-web-container')
        containerEl.classList.remove('hidden')
        containerEl.classList.add('full', 'desktop-web-container')
      } else {
        var containerEl = document.getElementById('public-web-container')
        containerEl.classList.remove('hidden')
        containerEl.classList.add('full', 'public-web-container')


        var buttonEl = document.getElementById('public-web-jump-button')
        var buttonLoadingEl = document.getElementById('public-web-jump-button-loading')
        try {
          await openWeapp(() => {
            buttonEl.classList.remove('weui-btn_loading')
            buttonLoadingEl.classList.add('hidden')
          })
        } catch (e) {
          buttonEl.classList.remove('weui-btn_loading')
          buttonLoadingEl.classList.add('hidden')
          throw e
        }
      }
    })

    async function openWeapp(onBeforeJump) {
      if (onBeforeJump) {
        onBeforeJump()
      }
      location.href = window.data.url_scheme
    }
  </script>
  <style>
    .hidden {
      display: none;
    }

    .full {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    .public-web-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .public-web-container p {
      position: absolute;
      top: 40%;
    }

    .public-web-container a {
      position: absolute;
      bottom: 40%;
    }

    /* .wechat-web-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .wechat-web-container p {
      position: absolute;
      top: 40%;
    }

    .wechat-web-container wx-open-launch-weapp {
      position: absolute;
      bottom: 40%;
      left: 0;
      right: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    } */

    .desktop-web-container {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .desktop-web-container p {
      position: absolute;
      top: 40%;
    }
  </style>
</head>

<body>
  <div class="page full">
    <div id="public-web-container" class="hidden">
      <p class="">正在打开...</p> <!-- replace -->
      <a id="public-web-jump-button" href="javascript:" class="weui-btn weui-btn_primary weui-btn_loading"
        onclick="openWeapp()">
        <span id="public-web-jump-button-loading" class="weui-primary-loading weui-primary-loading_transparent"><i
            class="weui-primary-loading__dot"></i></span>
        打开小程序
      </a>
    </div>
    <div id="desktop-web-container" class="hidden">
      <p class="">请在手机打开网页链接</p>
    </div>
  </div>
</body>

</html>