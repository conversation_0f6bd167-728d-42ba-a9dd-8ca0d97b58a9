(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-25acf0e6"],{"015e":function(t,i,s){"use strict";s.d(i,"c",(function(){return l})),s.d(i,"d",(function(){return u})),s.d(i,"a",(function(){return r})),s.d(i,"b",(function(){return c})),s.d(i,"e",(function(){return o}));var e=s("b775");const a=window.sysConfig.services.wecom,n=a+"/qi";function l(t){return Object(e["a"])({url:n+"/weekly/getDetail/"+t})}function u(t,i){return Object(e["a"])({url:n+"/weekly/detail/list/"+t,params:i})}function r(t){return Object(e["a"])({url:n+"/notice/list",params:t})}function c(t){return Object(e["a"])({url:n+"/list",params:t})}function o(t){return Object(e["a"])({url:n+"/notice/update/replyStatus/"+t,method:"put"})}},"2bf88":function(t,i,s){"use strict";s("dc59")},cca4:function(t,i,s){"use strict";s.r(i);var e=function(){var t=this,i=t._self._c;return i("div",[i("div",{staticClass:"search"},[i("van-search",{attrs:{"show-action":"",placeholder:"请输入搜索关键词"},on:{search:t.onSearch},scopedSlots:t._u([{key:"action",fn:function(){return[i("div",{staticClass:"button",on:{click:t.onSearch}},[t._v("搜索")])]},proxy:!0}]),model:{value:t.query.userName,callback:function(i){t.$set(t.query,"userName",i)},expression:"query.userName"}})],1),i("div",{staticClass:"list"},[i("van-list",{attrs:{finished:t.finished,"finished-text":"~已经到底啦"},on:{load:t.onLoad},model:{value:t.loading,callback:function(i){t.loading=i},expression:"loading"}},t._l(t.list,(function(s,e){return i("div",{key:e,staticClass:"list-item"},[i("div",{staticClass:"title"},[i("span",{staticClass:"dept"},[t._v(t._s(s.deptName))]),i("span",{staticClass:"name"},[t._v(t._s(s.userName))])]),i("van-divider",{style:{color:"#E5E6EB;",borderColor:"#E5E6EB",padding:"16px 0",margin:"0"}}),i("div",{staticClass:"value"},[i("div",{staticClass:"value-unit"},[i("span",{staticClass:"value-title"},[t._v("超时次数")]),i("div",{staticClass:"value-num"},[t._v(t._s(s.timeOutNum))])]),i("div",{staticClass:"value-unit"},[i("span",{staticClass:"value-title"},[t._v("超时率")]),i("div",{staticClass:"value-num"},[t._v(t._s(100*Number(s.timeOutRate)+"%"))])]),i("div",{staticClass:"value-unit"},[i("span",{staticClass:"value-title"},[t._v("客户会话"),i("br"),t._v("超时率")]),i("div",{staticClass:"value-num"},[t._v(t._s(100*Number(s.chatTimeOutRate)+"%"))])]),i("div",{staticClass:"value-unit"},[i("span",{staticClass:"value-title"},[t._v("客群会话"),i("br"),t._v("超时率")]),i("div",{staticClass:"value-num"},[t._v(t._s(100*Number(s.groupChatTimeOutRate)+"%"))])])])],1)})),0)],1)])},a=[],n=s("015e"),l={name:"record-detail",data(){return{loading:!1,finished:!1,list:[],total:0,id:"",query:{pageSize:10,pageNum:1,userName:""}}},methods:{onLoad(){this.list.length<this.total?(this.query.pageNum++,this.loading=!0,Object(n["d"])(this.id,this.query).then(t=>{this.list=this.list.contact(t.rows),this.total=Number(t.total),this.list.length==this.total&&(this.finished=!0),this.loading=!1}).finally(()=>{this.loading=!1})):(this.finished=!0,this.loading=!1)},onSearch(){this.query.pageNum=1,this.getList()},getList(){this.loading=!0,Object(n["d"])(this.id,this.query).then(t=>{this.list=t.rows,this.total=Number(t.total),this.list.length==this.total&&(this.finished=!0),this.loading=!1}).finally(()=>{this.loading=!1})}},mounted(){this.id=this.$route.query.id,this.getList()}},u=l,r=(s("2bf88"),s("2877")),c=Object(r["a"])(u,e,a,!1,null,"179c0ce3",null);i["default"]=c.exports},dc59:function(t,i,s){}}]);