(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7225e60a"],{"2c4a":function(t,e,s){"use strict";s.d(e,"b",(function(){return a})),s.d(e,"a",(function(){return r})),s.d(e,"e",(function(){return c})),s.d(e,"c",(function(){return d})),s.d(e,"d",(function(){return u}));var n=s("b775");const o=window.sysConfig.services.weChat,i=o+"/lxqr";function a(t){return Object(n["a"])({url:i+"/getQrcode",method:"post",data:t})}function r(t){return Object(n["a"])({url:i+"/receive/award",method:"post",data:t})}function c(){return Object(n["a"])({url:o+"/RedEnvelopes/findRedEnvelopesInfo"})}function d(t){return Object(n["a"])({url:i+"/getReceiveList",method:"post",data:t})}function u(t){return Object(n["a"])({url:i+"/checkIsReceive",method:"post",data:t})}},"520d":function(t,e,s){t.exports=s.p+"img/redPacket.5f805eaf.png"},"6ab7":function(t,e,s){"use strict";s.r(e);var n=function(){var t=this,e=t._self._c;return t.getCode?e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"pullNews",class:"redBox"},[e("div",{staticClass:"red-box"},[e("div",{staticClass:"box"},[e("img",{staticClass:"red-img",attrs:{src:s("520d"),alt:""}}),e("div",{staticClass:"open",on:{click:t.openClick}},[e("img",{attrs:{src:s("b1a0"),alt:""}})])])]),e("van-popup",{style:{height:"322px"},attrs:{round:"",position:"bottom",closeable:"","close-icon-position":"top-right"},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[e("div",{staticClass:"contentQR"},[e("div",{staticClass:"codeQR"},[e("img",{attrs:{src:t.qrCodeUrl,alt:""}})]),e("div",{staticClass:"codeQRText"},[t._v("长按添加员工，立刻参与活动")])])])],1):t._e()},o=[],i=s("ed08"),a=s("2934"),r=s("2c4a"),c={data(){return{show:!1,qrCodeUrl:"",userInfo:{},type:1,loading:!1,getCode:!1}},async created(){this.type=this.$route.query.type,await Object(i["d"])(),this.getCode=!0,sessionStorage.getItem("userinfo")&&(this.userInfo=JSON.parse(sessionStorage.getItem("userinfo")),Object(a["k"])(window.location.href.split("#")[0]).then(t=>{if(200===t.code){let{timestamp:e,nonceStr:s,signature:n}=t.data;wx.config({beta:!0,debug:!1,appId:sessionStorage.getItem("weAppId"),timestamp:e,nonceStr:s,signature:n,jsApiList:["getLocation","chooseImage","previewImage"],openTagList:["wx-open-launch-weapp"],success:function(t){location.reload()},fail:t=>{this.$toast.clear(),alert("config失败:"+JSON.stringify(t)),t.errMsg.indexOf("function not exist")>-1&&alert("版本过低请升级")}})}}))},methods:{getData(){Object(r["b"])({qrId:this.$route.query.id,unionid:this.userInfo.unionId}).then(t=>{this.loading=!1,200===t.code?(this.show=!0,this.qrCodeUrl=t.data.qrCode):-1===t.code&&this.$dialog.confirm({title:"提示",message:"您已经是企业客户啦，暂不符合该拉新活动要求。"}).then(()=>{}).catch(()=>{})}).catch(()=>{this.loading=!1})},openClick(){this.loading=!0,this.getData()},aaa(t){let e=t.target.dataset.src;wx.previewImage({current:e,urls:[e]})}}},d=c,u=(s("a9cb"),s("2877")),l=Object(u["a"])(d,n,o,!1,null,"13d9f735",null);e["default"]=l.exports},8365:function(t,e,s){},a9cb:function(t,e,s){"use strict";s("8365")},b1a0:function(t,e,s){t.exports=s.p+"img/open.8c86a616.png"}}]);