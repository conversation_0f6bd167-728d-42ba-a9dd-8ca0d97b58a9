<template>
  <div class="swarmsSOP">
    <div>
      <van-sticky>
        <div style="background: #fafafa;">
          <!-- 拨打电话SOP不显示客户信息头部 -->
          <div v-if="!isPhoneCallSop" class="swarmsSOP_message">
            <div class="swarmsSOP_message_top">
              <div class="swarmsSOP_message_top_left">
                <div class="swarmsSOP_message_top_left_userImg">
                  <img v-if="form.avatar" :src="form.avatar" alt="" />
                </div>
                <div class="swarmsSOP_message_top_left_info">
                  <div>
                    {{ form.customerName }}
                    <span :style="{ color: form.customerType === 1 ? '#4bde03' : '#f9a90b' }">
                      {{ { 1: '@微信', 2: form.corpName ? '@' + form.corpName : '@企业微信' }[form.customerType] }}
                    </span>
                  </div>
                  <div>
                    <img v-if="form.gender === 1" src="../../../../assets/man.png" />
                    <img v-else-if="form.gender === 2" src="../../../../assets/woman.png" />
                  </div>
                </div>
              </div>
              <div class="swarmsSOP_message_top_right">
                <div class="track-state">{{ trackState }}</div>

              </div>
            </div>
          </div>
          <div class="swarmsSOP_tabBar">
            <div @click="setChange(0)" :class="tabBar == 0 ? 'swarmsSOP_tabBar_li1' : 'swarmsSOP_tabBar_li'">
              {{ isPhoneCallSop ? '待拨打' : '待推送' }}
            </div>
            <div @click="setChange(1)" :class="tabBar == 1 ? 'swarmsSOP_tabBar_li1' : 'swarmsSOP_tabBar_li'">
              {{ isPhoneCallSop ? '已拨打' : '已推送' }}
            </div>
          </div>
        </div>
      </van-sticky>

      <div class="swarmsSOP_box" style="display: flex; flex-direction: column;">
        <!-- 拨打电话SOP的特殊界面 -->
        <div v-if="isPhoneCallSop">
          <!-- 无数据提示 -->
          <div v-if="!phoneCallCustomers.length" class="phone-call-tip">
            <div class="tip-icon">📞</div>
            <div class="tip-text">暂无拨打电话任务</div>
            <div class="tip-desc">当有客户需要电话跟进时，任务会在这里显示</div>
          </div>

          <!-- 客户列表 -->
          <div v-else class="phone-call-customers">
            <div v-for="(customer, index) in phoneCallCustomers" :key="index" class="customer-item">
              <div class="customer-info">
                <div class="customer-avatar">
                  <img v-if="customer.avatar" :src="customer.avatar" alt="头像" />
                  <div v-else class="default-avatar">{{ customer.customerName ? customer.customerName.charAt(0) : '客' }}</div>
                </div>
                <div class="customer-details">
                  <div class="customer-name">{{ customer.customerName || '未知客户' }}</div>
                  <div class="customer-phone" style="pointer-events: none;">{{ customer.customerPhone || '未设置电话' }}</div>
                  <div class="customer-status">
                    <span :class="getCustomerStatusClass(customer)">{{ getCustomerStatusText(customer) }}</span>
                  </div>
                </div>
              </div>
              <div class="customer-actions">
                <!-- 在已推送tab(tabBar === 1)中不显示拨打电话按钮 -->
                <div v-if="tabBar !== 1">
                  <div class="action-buttons">
                    <div
                      v-if="customer.customerPhone"
                      :class="['phone-call-btn', { 'disabled': customer.executeState === 1 }]"
                      @click="makePhoneCallForCustomer(customer)"
                    >
                      📞 {{ customer.executeState === 1 ? '已拨打' : '拨打电话' }}
                    </div>
                    <div v-else class="phone-call-btn disabled">
                      📞 无电话号码
                    </div>
                    <!-- 新增语音通话按钮 -->
                    <div
                      v-if="customer.externalUserid"
                      :class="['voice-call-btn', { 'disabled': customer.executeState === 1 }]"
                      @click="makeVoiceCallForCustomer(customer)"
                    >
                      🎤 {{ customer.executeState === 1 ? '已通话' : '语音通话' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 普通SOP界面 -->
        <div v-else>
          <NoData v-if="!dataList.length"></NoData>
          <template v-else>
            <div v-for="(item, index) in dataList" :key="index" class="swarmsSOP_content">
            <div
              :class="{
                swarmsSOP_content_top1: item.type === 1,
                swarmsSOP_content_top2: item.type === 2,
                swarmsSOP_content_top3: item.type === 3,
              }"
            >
              <div style="margin-bottom: 12px;" class="swarmsSOP_content_top_text">{{ item.sopName }}</div>
              <div v-if="item.type === 1 && tabBar === 0" class="swarmsSOP_content_top_text">
                距推送结束剩余{{ item.time }}
              </div>
              <div v-if="item.type === 2 && tabBar === 0" class="swarmsSOP_content_top_text">
                距推送时间已过{{ item.time }}
              </div>
              <div v-if="item.type === 3 && tabBar === 0" class="swarmsSOP_content_top_text">未到推送时间</div>
            </div>
            <div @click="item.open = !item.open" class="swarmsSOP_content_title">
              <div>SOP内容</div>
              <div class="swarmsSOP_message_content_box_li_right">
                <img v-if="!item.open" src="@/assets/Applications/topj3.png" alt="" />
                <img style="transform: rotate(180deg);" v-if="item.open" src="@/assets/Applications/topj3.png" alt="" />
              </div>
            </div>
            <div class="swarmsSOP_content_li" v-if="item.open">
              <template v-for="(unit, key) in item.list">
                <div class="unit" :key="key">
                  <ShowSendInfo :key="key + 111" :obj="unit.weQrAttachments"></ShowSendInfo>
                  <div class="operation" :key="key" v-if="tabBar === 0">
                    <span style="color: #00bf2f;" v-if="item.type !== 3 && unit.executeState === 1">已发送</span>
                    <span style="color: #ed4014;" v-if="item.type !== 3 && unit.executeState === 0">待发送</span>
                    <span v-if="item.type === 3">未到推送时间</span>
                    <van-button
                      type="info"
                      v-if="unit.executeState === 0 && item.type !== 3"
                      @click="send(unit.weQrAttachments, unit.executeTargetAttachId)"
                    >
                      发送
                    </van-button>
                  </div>
                </div>
              </template>
            </div>
          </div>
          </template>
        </div>
      </div>
    </div>
    <Loading :isLoad="isLoad" />

    <!-- 调试面板 -->
    <div v-if="showDebugPanel" class="debug-panel">
      <div class="debug-header">
        <span>调试信息</span>
        <div class="debug-controls">
          <button @click="debugDataStatus" class="debug-btn">调试</button>
          <button @click="copyDebugLogs" class="debug-btn">复制</button>
          <button @click="clearDebugLogs" @dblclick="forceClearDebugLogs" class="debug-btn" title="单击清除，双击完全清除">清除</button>
          <button @click="showDebugPanel = false" class="debug-btn close-btn">关闭</button>
        </div>
      </div>
      <div class="debug-content" ref="debugContent">
        <div v-for="(log, index) in debugLogs" :key="index" :class="['debug-log', log.type]">
          <span class="debug-level">{{ log.type.toUpperCase() }}</span>
          <span class="debug-time">{{ log.time }}</span>
          <span class="debug-message">{{ log.message }}</span>
          <pre v-if="log.data" class="debug-data">{{ typeof log.data === 'object' ? JSON.stringify(log.data, null, 2) : log.data }}</pre>
        </div>
      </div>
    </div>

    <!-- 调试按钮 -->
    <div class="debug-toggle" @click="showDebugPanel = !showDebugPanel">
      🐛
    </div>
  </div>
</template>

<script>
import NoData from '@/components/NoData.vue'
import Loading from '@/components/Loading.vue'
import { getCustomerSopContent, getSuccess } from '@/api/sop'
import { compareTime } from '@/utils/index.js'
import { makePhoneCall, hasPhoneCallSop } from '@/utils/phoneCall.js'
import ShowSendInfo from '@/components/ShowSendInfo'
import { getMaterialMediaId } from '@/api/chat'
import { getStageList } from '@/api/common'
export default {
  name: 'customer-sop',
  components: {
    ShowSendInfo,
    Loading,
    NoData,
  },
  data() {
    return {
      isLoad: false,
      trackState: '',
      externalUserId: '',
      tabBar: 0,
      form: {
        avatar: '',
        customerName: '',
        customerType: null,
        externalUserid: '',
        gender: null,
        trackState: null,
        weCustomerSops: [],
      },
      dataList: [],
      // 调试相关
      showDebugPanel: false,
      debugLogs: [],
      isClearing: false // 标记是否正在清除日志
    }
  },
  props: {
    userId: {
      type: String,
      default: '',
    },
    sopType: {
      type: Number,
      default: null,
    },
    sopBaseId: {
      type: String,
      default: '',
    },
    executeTargetAttachId: {
      type: String,
      default: '',
    },
  },
  watch: {
    userId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.externalUserId = val
          this.getData(0)
        } else if (this.isPhoneCallSop) {
          // 拨打电话SOP模式下，即使没有具体客户ID也要加载数据
          this.getData(0)
        }
      },
    },
    sopType: {
      immediate: true,
      handler(val) {
        if (val === 14 && !this.userId) {
          // 拨打电话SOP模式下，加载数据
          this.getData(0)
        }
      },
    },
  },
  computed: {
    // 判断是否为拨打电话SOP
    isPhoneCallSop() {
      return this.sopType === 14 // MessageNoticeType.PHONE_CALL_SOP.getType()
    },
    // 页面标题根据SOP类型变化
    pageTitle() {
      return this.isPhoneCallSop ? '拨打电话SOP' : '客户SOP'
    },
    // 拨打电话SOP的客户列表
    phoneCallCustomers() {
      if (!this.isPhoneCallSop || !this.form || !this.form.weCustomerSops) {
        return []
      }

      // 修复：拨打电话SOP不需要按时间段展开，因为用户点击的是特定时间段的消息
      // 每个客户应该使用自己在该时间段的独立executeTargetAttachId
      const customers = this.form.weCustomerSops.map(sop => {
        // 从返回的数据中获取该客户在当前时间段的executeTargetAttachId
        let executeTargetAttachId = null
        if (sop.weCustomerSopContents && sop.weCustomerSopContents.length > 0) {
          executeTargetAttachId = String(sop.weCustomerSopContents[0].executeTargetAttachId)
          console.log(`[DEBUG] 客户 ${sop.customerName} 的executeTargetAttachId: ${executeTargetAttachId}, URL参数: ${this.executeTargetAttachId}`)
        }

        // 创建响应式的客户对象
        const customerObj = {
          customerName: sop.customerName,
          customerPhone: sop.customerPhone,
          externalUserid: sop.externalUserid,
          avatar: null, // 暂时没有头像数据
          sopName: sop.sopName,
          businessType: sop.businessType,
          sopContents: sop.weCustomerSopContents || [],
          sopBaseId: sop.sopBaseId, // 添加sopBaseId用于记录拨打
          executeTargetId: this.getExecuteTargetId(sop), // 添加executeTargetId
          executeTargetAttachId: executeTargetAttachId, // 用于区分不同时间段
          _sopRef: sop // 保持对原始sop对象的引用，确保响应性
        }

        // 使用getter让executeState始终返回最新的callStatus
        Object.defineProperty(customerObj, 'executeState', {
          get() {
            // 拨打电话SOP：每个时间段的每个客户都有独立的拨打状态
            // 根据当前executeTargetAttachId精确匹配对应时间段的callStatus
            if (sop.weCustomerSopContents && sop.weCustomerSopContents.length > 0) {
              // 查找匹配当前executeTargetAttachId的时间段内容
              const targetContent = sop.weCustomerSopContents.find(content =>
                String(content.executeTargetAttachId) === String(executeTargetAttachId)
              )

              if (targetContent && targetContent.callStatus !== undefined) {
                // 找到匹配的时间段，返回该时间段的独立callStatus
                return targetContent.callStatus || 0
              }

              // 如果没有找到匹配的时间段，说明数据有问题，返回0
              console.warn(`[DEBUG] 未找到匹配的时间段内容，executeTargetAttachId: ${executeTargetAttachId}`)
              return 0
            }

            // 如果没有时间段内容，返回0
            return 0
          },
          enumerable: true,
          configurable: true
        })

        return customerObj
      })

      // 移除计算属性中的调试日志，避免频繁触发

      // 根据当前标签页过滤客户
      let filteredCustomers
      if (this.tabBar === 0) {
        // 待拨打：显示未执行的客户
        filteredCustomers = customers.filter(customer => customer.executeState === 0)
      } else {
        // 已拨打：显示已执行的客户
        filteredCustomers = customers.filter(customer => customer.executeState === 1)
      }

      // 移除计算属性中的调试日志，避免频繁触发

      return filteredCustomers
    },

    // 已拨打客户数量
    completedCustomersCount() {
      if (!this.isPhoneCallSop || !this.form || !this.form.weCustomerSops) {
        return 0
      }

      // 统计callStatus为1的客户数量
      return this.form.weCustomerSops.filter(sop => sop.callStatus === 1).length
    }
  },
  created() {
    // 组件创建时的初始化逻辑
  },

  mounted() {
    // 防止浏览器自动检测电话号码并添加链接
    const metaTag = document.createElement('meta')
    metaTag.name = 'format-detection'
    metaTag.content = 'telephone=no'
    document.head.appendChild(metaTag)
  },
  methods: {

    // 检查是否有拨打电话SOP（在dataList中）
    hasPhoneCallSopInDataList() {
      return hasPhoneCallSop(this.dataList)
    },

    // 处理普通客户SOP的拨打电话点击
    handlePhoneCallClick(phone, form) {
      makePhoneCall(phone, form)
    },

    // 直接拨打电话
    async makePhoneCallDirectly(phoneNumber, customer) {
      try {
        // 验证电话号码格式
        const phoneRegex = /^1[3-9]\d{9}$/
        const cleanPhone = phoneNumber.replace(/\D/g, '')
        if (!phoneRegex.test(cleanPhone)) {
          this.$toast('电话号码格式不正确')
          return
        }

        // 使用手机拨打电话
        this.makePhoneCallByMobile(cleanPhone, customer)

      } catch (error) {
        console.error('拨打电话失败:', error)
        this.$toast('拨打失败：' + (error.message || '未知错误'))
      }
    },

    // 手机拨打电话功能
    makePhoneCallByMobile(phoneNumber, customer) {
      try {
        // 验证电话号码格式
        const phoneRegex = /^1[3-9]\d{9}$/
        const cleanPhone = phoneNumber.replace(/\D/g, '')
        if (!phoneRegex.test(cleanPhone)) {
          this.$toast('电话号码格式不正确')
          return
        }

        // 检查是否在移动设备上
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

        if (isMobile) {
          // 移动设备：直接拨打电话
          const telLink = `tel:${cleanPhone}`
          const link = document.createElement('a')
          link.href = telLink
          link.style.display = 'none'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.$toast('正在拨打电话...')
          // 不需要在这里更新状态，因为在handlePhoneCall中已经更新过了
        } else {
          // 非移动设备：提示用户使用手机
          this.$toast('拨打电话功能仅支持手机设备，请在手机上使用')
        }
      } catch (error) {
        console.error('手机拨打失败:', error)
        this.$toast('拨打失败：' + (error.message || '未知错误'))
      }
    },



    // 获取当前客户的电话号码
    getCurrentCustomerPhone() {
      if (!this.isPhoneCallSop || !this.dataList || this.dataList.length === 0) {
        return null
      }

      // 从第一个SOP项目中提取客户电话
      const firstSop = this.dataList[0]
      return this.getCustomerPhoneFromSop(firstSop)
    },

    // 从SOP项目中提取客户电话号码
    getCustomerPhoneFromSop(sopItem) {
      if (!sopItem || !sopItem.sopName) {
        return null
      }

      // 从SOP名称中提取客户信息（格式：SOP名称 - 客户姓名）
      const nameParts = sopItem.sopName.split(' - ')
      if (nameParts.length < 2) {
        return null
      }

      const customerName = nameParts[1]

      // 从后端数据中查找对应客户的电话号码
      if (this.form && this.form.weCustomerSops) {
        for (const sop of this.form.weCustomerSops) {
          // 检查SOP对象本身是否有客户电话（新增的字段）
          if (sop.customerPhone) {
            return sop.customerPhone
          }

          // 检查SOP名称是否匹配
          if (sop.sopName && sop.sopName.includes(customerName)) {
            // 找到匹配的SOP，但没有电话号码字段
          }
        }
      }

      return null
    },

    // 为当前客户拨打电话
    makePhoneCallForCurrentCustomer() {
      const phone = this.getCurrentCustomerPhone()
      if (phone) {
        this.makePhoneCall(phone, { customerName: '当前客户' })
      } else {
        this.$toast('客户未设置电话号码')
      }
    },

    // 为特定SOP项目拨打电话
    makePhoneCallForSop(sopItem) {
      const phone = this.getCustomerPhoneFromSop(sopItem)
      if (phone) {
        const customerName = sopItem.sopName.split(' - ')[1] || '未知客户'
        makePhoneCall(phone, { customerName: customerName })
      } else {
        this.$toast('客户未设置电话号码')
      }
    },

    // 为客户拨打电话
    async makePhoneCallForCustomer(customer) {
      this.addDebugLog('info', '开始拨打电话', customer)
      this.addDebugLog('info', `executeTargetAttachId值: ${customer.executeTargetAttachId}`)
      this.addDebugLog('info', `executeTargetAttachId类型: ${typeof customer.executeTargetAttachId}`)

      if (!customer.customerPhone) {
        this.$toast('客户未设置电话号码')
        return
      }

      // 检查是否已经拨打过
      if (customer.executeState === 1) {
        this.$toast('该客户已拨打过电话')
        return
      }

      // 检查是否在移动设备上
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      if (!isMobile) {
        this.$toast('拨打电话功能仅支持手机设备，请在手机上使用')
        return
      }

      try {
        // 先记录拨打电话
        await this.recordPhoneCall(customer)
        this.$toast('已记录拨打状态')

        // 从后端重新获取最新状态，确保数据一致性
        this.addDebugLog('info', `拨打电话成功，重新获取后端状态，executeTargetAttachId: ${customer.executeTargetAttachId}`)
        await this.getData(this.tabBar, customer.executeTargetAttachId)

        // 添加调试：检查重新获取后的数据
        if (this.form && this.form.weCustomerSops) {
          this.form.weCustomerSops.forEach(sop => {
            if (sop.externalUserid === customer.externalUserid) {
              this.addDebugLog('info', `重新获取后的数据 - 客户: ${sop.customerName}, callStatus: ${sop.callStatus}, executeTargetAttachId: ${customer.executeTargetAttachId}`)
            }
          })
        }

        // 检查是否需要自动切换到已拨打标签页
        this.checkAndSwitchTab()

        // 拨打电话
        this.makePhoneCallDirectly(customer.customerPhone, customer)
      } catch (error) {
        console.error('记录拨打电话失败:', error)
        this.$toast('记录拨打失败：' + (error.message || '未知错误'))
        // 记录失败时不更新状态，保持原有的"待拨打"状态
      }
    },

    // 为客户发起语音通话
    async makeVoiceCallForCustomer(customer) {
      this.addDebugLog('info', '开始语音通话', customer)
      this.addDebugLog('info', `executeTargetAttachId值: ${customer.executeTargetAttachId}`)
      this.addDebugLog('info', `executeTargetAttachId类型: ${typeof customer.executeTargetAttachId}`)

      if (!customer.externalUserid) {
        this.$toast('客户信息不完整，无法发起语音通话')
        return
      }

      // 检查是否已经通话过
      if (customer.executeState === 1) {
        this.$toast('该客户已发起过通话')
        return
      }

      // 检查企业微信环境
      if (!window.wx || !window.wx.invoke) {
        this.$toast('请在企业微信中使用语音通话功能')
        return
      }

      try {
        // 先记录语音通话（复用拨打电话的记录方法）
        await this.recordPhoneCall(customer, 'wechat_voice')
        this.$toast('已记录通话状态')

        // 从后端重新获取最新状态，确保数据一致性
        this.addDebugLog('info', `语音通话成功，重新获取后端状态，executeTargetAttachId: ${customer.executeTargetAttachId}`)
        await this.getData(this.tabBar, customer.executeTargetAttachId)

        // 添加调试：检查重新获取后的数据
        if (this.form && this.form.weCustomerSops) {
          this.form.weCustomerSops.forEach(sop => {
            if (sop.externalUserid === customer.externalUserid) {
              this.addDebugLog('info', `重新获取后的数据 - 客户: ${sop.customerName}, callStatus: ${sop.callStatus}, executeTargetAttachId: ${customer.executeTargetAttachId}`)
            }
          })
        }

        // 检查是否需要自动切换到已拨打标签页
        this.checkAndSwitchTab()

        // 打开语音通话窗口
        this.openVoiceChatWindow(customer)

      } catch (error) {
        console.error('记录语音通话失败:', error)
        this.$toast('记录通话失败：' + (error.message || '未知错误'))
        this.addDebugLog('error', '记录语音通话失败', error)
        // 记录失败时不更新状态，保持原有的"待通话"状态
      }
    },



    // 打开语音通话窗口
    openVoiceChatWindow(customer) {
      try {
        // 使用企业微信API打开聊天窗口
        window.wx.invoke('openEnterpriseChat', {
          userIds: sessionStorage.userId || '', // 当前用户ID
          externalUserIds: customer.externalUserid, // 客户的外部用户ID
          groupName: '', // 单聊时传空字符串
          chatId: '', // 新建会话时传空字符串
          success: (res) => {
            this.addDebugLog('info', '语音通话窗口打开成功', res)
            this.$toast('已打开聊天窗口，可在其中发起语音通话')
          },
          fail: (res) => {
            this.addDebugLog('error', '语音通话窗口打开失败', res)
            if (res.errMsg && res.errMsg.indexOf('function not exist') > -1) {
              this.$toast('企业微信版本过低，请升级后使用')
            } else {
              this.$toast('打开聊天窗口失败：' + (res.errMsg || '未知错误'))
            }
          }
        })
      } catch (error) {
        console.error('语音通话失败:', error)
        this.$toast('语音通话失败：' + (error.message || '未知错误'))
        this.addDebugLog('error', '语音通话失败', error)
      }
    },

    // 获取客户状态样式类
    getCustomerStatusClass(customer) {
      const state = customer.executeState
      return {
        'status-pending': state === 0,
        'status-completed': state === 1
      }
    },

    // 获取客户状态文本
    getCustomerStatusText(customer) {
      const state = customer.executeState
      if (this.tabBar === 0) {
        // 待拨打标签页
        return state === 0 ? '待拨打' : '已拨打'
      } else {
        // 已拨打标签页
        return state === 1 ? '已拨打' : '待拨打'
      }
    },

    // 记录拨打电话（支持手机拨打和语音通话）
    async recordPhoneCall(customer, callMethod = 'mobile') {
      const { recordPhoneCall } = await import('@/api/phoneCall')

      // 根据调用方式设置不同的参数
      const isVoiceCall = callMethod === 'wechat_voice'

      const recordData = {
        sopBaseId: customer.sopBaseId,
        executeTargetId: customer.executeTargetId, // 可以为null
        executeTargetAttachId: customer.executeTargetAttachId, // 使用客户自己的executeTargetAttachId
        externalUserid: customer.externalUserid,
        customerName: customer.customerName,
        customerPhone: isVoiceCall ? (customer.customerPhone || '无') : customer.customerPhone,
        callMethod: callMethod,
        remark: isVoiceCall ? '通过拨打电话SOP发起（企业微信语音通话）' : '通过拨打电话SOP发起（手机拨打）'
      }

      // 验证必要字段
      if (!recordData.sopBaseId) {
        throw new Error('SOP基础ID不能为空')
      }
      if (!recordData.externalUserid) {
        throw new Error('客户ID不能为空')
      }
      // 只有手机拨打才需要验证电话号码
      if (!isVoiceCall && !recordData.customerPhone) {
        throw new Error('客户电话不能为空')
      }
      if (!recordData.executeTargetAttachId) {
        this.addDebugLog('error', 'executeTargetAttachId为空，客户数据', customer)
        throw new Error('SOP执行目标附件ID不能为空，请检查数据完整性')
      }

      // 记录接口调用信息
      this.addDebugLog('api', `准备调用记录接口`, {
        url: '/phone/recordCall',
        method: 'POST',
        requestData: recordData
      })

      // 验证电话号码格式
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!phoneRegex.test(recordData.customerPhone.replace(/\D/g, ''))) {
        throw new Error('客户电话号码格式不正确')
      }

      // 验证客户姓名
      if (!recordData.customerName || recordData.customerName.trim() === '') {
        recordData.customerName = '未知客户'
      }

      // 添加超时处理
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('请求超时，请检查网络连接')), 10000)
      })

      try {
        const response = await Promise.race([recordPhoneCall(recordData), timeoutPromise])

        // 记录接口响应信息
        this.addDebugLog('api', `记录接口响应`, {
          url: '/phone/recordCall',
          status: response.code,
          message: response.msg,
          data: response.data,
          success: response.code === 200
        })

        if (response.code === 200) {
          return response.data
        } else {
          throw new Error(response.msg || '记录拨打电话失败')
        }
      } catch (error) {
        // 记录接口错误信息
        this.addDebugLog('api', `记录接口调用失败`, {
          url: '/phone/recordCall',
          error: error.message,
          requestData: recordData
        })
        throw error
      }
    },




    // 调试相关方法
    addDebugLog(type, message, data = null) {
      // 如果正在清除日志，则不添加新日志（除了清除完成的记录）
      if (this.isClearing && !message.includes('调试日志已手动清除')) {
        return
      }

      const log = {
        type: type, // 'info', 'warn', 'error', 'success'
        message: message,
        data: data ? JSON.stringify(data, null, 2) : null,
        time: new Date().toLocaleTimeString()
      }
      this.debugLogs.push(log)

      // 限制日志数量，避免内存溢出
      if (this.debugLogs.length > 100) {
        this.debugLogs.shift()
      }

      // 自动滚动到底部
      this.$nextTick(() => {
        if (this.$refs.debugContent) {
          this.$refs.debugContent.scrollTop = this.$refs.debugContent.scrollHeight
        }
      })
    },

    copyDebugLogs() {
      try {
        const logsText = this.debugLogs.map(log => {
          let text = `[${log.time}] ${log.type.toUpperCase()}: ${log.message}`
          if (log.data) {
            text += `\n${log.data}`
          }
          return text
        }).join('\n\n')

        // 尝试使用现代API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(logsText).then(() => {
            this.$toast('调试日志已复制到剪贴板')
          }).catch(() => {
            this.fallbackCopyText(logsText)
          })
        } else {
          this.fallbackCopyText(logsText)
        }
      } catch (error) {
        this.$toast('复制失败：' + error.message)
      }
    },

    fallbackCopyText(text) {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()

        const successful = document.execCommand('copy')
        document.body.removeChild(textArea)

        if (successful) {
          this.$toast('调试日志已复制到剪贴板')
        } else {
          this.$toast('复制失败，请手动复制')
        }
      } catch (error) {
        this.$toast('复制失败：' + error.message)
      }
    },

    clearDebugLogs() {
      // 记录清除前的日志数量
      const logCount = this.debugLogs.length

      // 设置清除标记
      this.isClearing = true

      // 使用splice方法确保Vue响应性
      this.debugLogs.splice(0, this.debugLogs.length)

      // 强制更新视图
      this.$nextTick(() => {
        // 确保DOM已更新
        if (this.$refs.debugContent) {
          this.$refs.debugContent.scrollTop = 0
        }

        // 延迟添加清除记录，确保清除操作完成
        setTimeout(() => {
          this.isClearing = false
          this.addDebugLog('success', `调试日志已手动清除，共清除 ${logCount} 条记录`)
        }, 100)
      })

      this.$toast('调试日志已清除')
    },

    forceClearDebugLogs() {
      // 完全清除，不添加任何记录
      this.debugLogs.splice(0, this.debugLogs.length)

      // 强制更新视图
      this.$nextTick(() => {
        if (this.$refs.debugContent) {
          this.$refs.debugContent.scrollTop = 0
        }
      })

      this.$toast('调试日志已完全清除')
    },

    // 手动调试数据状态
    debugDataStatus() {
      this.addDebugLog('info', '=== 手动调试数据状态 ===')

      if (this.isPhoneCallSop) {
        const customers = this.phoneCallCustomers
        this.addDebugLog('info', `拨打电话SOP客户列表，总记录数: ${customers.length}`)

        customers.forEach(customer => {
          this.addDebugLog('info', `客户: ${customer.customerName}, executeTargetAttachId: ${customer.executeTargetAttachId}, executeState: ${customer.executeState}, _sopRef.callStatus: ${customer._sopRef.callStatus}, 拨打状态: ${customer.executeState === 1 ? '已拨打' : '待拨打'}`)
        })

        const waitingCustomers = customers.filter(c => c.executeState === 0)
        const completedCustomers = customers.filter(c => c.executeState === 1)

        this.addDebugLog('info', `过滤结果 - 待拨打: ${waitingCustomers.length}个, 已拨打: ${completedCustomers.length}个, 当前标签页: ${this.tabBar === 0 ? '待拨打' : '已拨打'}`)
      } else {
        this.addDebugLog('info', '当前不是拨打电话SOP页面')
      }

      this.addDebugLog('info', '=== 调试完成 ===')
    },

    // 检查并自动切换标签页
    checkAndSwitchTab() {
      if (!this.isPhoneCallSop) return

      // 延迟检查，确保数据获取完成
      this.$nextTick(() => {
        const waitingCustomers = this.phoneCallCustomers.filter(c => c.executeState === 0)
        const completedCustomers = this.phoneCallCustomers.filter(c => c.executeState === 1)

        this.addDebugLog('info', `标签页切换检查 - 待拨打: ${waitingCustomers.length}个, 已拨打: ${completedCustomers.length}个, 当前标签页: ${this.tabBar === 0 ? '待拨打' : '已拨打'}`)

        // 如果当前在待拨打标签页，但没有待拨打客户，且有已拨打客户，则自动切换
        if (this.tabBar === 0 && waitingCustomers.length === 0 && completedCustomers.length > 0) {
          this.addDebugLog('success', '自动切换到已拨打标签页')
          this.tabBar = 1
          this.$toast('已自动切换到已拨打列表')
        }
      })
    },

    // 获取executeTargetId
    getExecuteTargetId(sop) {
      // 尝试从SOP内容中获取executeTargetId
      if (sop.weCustomerSopContents && sop.weCustomerSopContents.length > 0) {
        const firstContent = sop.weCustomerSopContents[0]
        if (firstContent && firstContent.executeTargetId) {
          return firstContent.executeTargetId
        }
      }

      // 如果没有找到，可以根据其他信息构造或返回null
      // 对于拨打电话SOP，executeTargetId可能不是必需的
      return null
    },




    filType(file) {
      let filecontent = JSON.parse(JSON.stringify(file))
      filecontent = filecontent.split('.')
      let type = filecontent[filecontent.length - 1]
      if (type === 'pdf') {
        return window.sysConfig.DEFAULT_H5_PDF
      } else if (['doc', 'docx'].includes(type)) {
        return window.sysConfig.DEFAULT_H5_WORDE
      } else if (['ppt', 'pptx', 'pps', 'pptsx'].includes(type)) {
        return window.sysConfig.DEFAULT_H5_PPT
      } else {
        return window.sysConfig.DEFAULT_H5_PIC
      }
    },
    getStage() {
      getStageList().then((res) => {
        const stageList = res.data?.map((e) => ({ text: e.stageKey, value: e.stageVal }))
        stageList?.some((e) => e.value == this.form.trackState && (this.trackState = e.text))
      })
    },
    setChange(type) {
      this.tabBar = type
      this.getData(type)
    },
    send(data, id) {
      this.$toast.loading({
        message: '正在发送...',
        duration: 0,
        forbidClick: true,
      })
      let _this = this
      wx.invoke('getContext', {}, async function(res) {
        if (res.err_msg == 'getContext:ok') {
          let mes = {}
          try {
            switch (data.msgType) {
              case 'text':
              default:
                mes.text = {
                  content: data.content, //文本内容
                }
                mes.msgtype = data.msgType
                break
              case 'image':
                let dataMediaId = {
                  url: data.picUrl,
                  type: data.msgType,
                  name: data.materialName,
                }
                try {
                  let resMaterialId = await getMaterialMediaId(dataMediaId)
                  if (!resMaterialId.data) {
                    _this.$toast('获取素材id失败')
                    return
                  }
                  _this.$set(mes, data.msgType, {
                    mediaid: resMaterialId.data.mediaId, //
                  })
                  mes.msgtype = data.msgType
                } catch (error) {
                  _this.$toast.clear()
                  return
                }
                break
              case 'video':
              case 'file':
                let linkUrl =
                  window.document.location.origin +
                  window.sysConfig.BASE_URL +
                  '#/metrialDetail?mediaType=' +
                  data.msgType +
                  '&materialUrl=' +
                  data.linkUrl
                mes.news = {
                  link: linkUrl, //H5消息页面url 必填
                  title: data.title ? data.title : '', //H5消息标题
                  desc: data.description ? data.description : '', //H5消息摘要
                  imgUrl: data.picUrl || _this.filType(data.linkUrl) || window.sysConfig.DEFAULT_H5_PIC,
                }
                mes.msgtype = 'news'
                break
              case 'link':
                mes.news = {
                  link: data.linkUrl, //H5消息页面url 必填
                  title: data.title ? data.title : '', //H5消息标题
                  desc: data.description ? data.description : '', //H5消息摘要
                  imgUrl: window.sysConfig.DEFAULT_H5_PIC, //H5消息封面图片URL
                }
                mes.msgtype = 'news'
                break
              case 'miniprogram':
                mes.miniprogram = {
                  appid: data.appId, //小程序的appid，企业已关联的任一个小程序
                  title: data.title, //小程序消息的title
                  imgUrl: data.picUrl, //小程序消息的封面图。必须带http或者https协议头，否则报错 $apiName$:fail invalid imgUrl
                  page: data.linkUrl, //小程序消息打开后的路径，注意要以.html作为后缀，否则在微信端打开会提示找不到页面
                }
                mes.msgtype = data.msgType
                break
            }
          } catch (err) {
            _this.$dialog({ message: 'err' + JSON.stringify(err) })
          }
          wx.invoke('sendChatMessage', mes, function(resSend) {
            if (resSend.err_msg == 'sendChatMessage:ok') {
              //发送成功 sdk会自动弹出成功提示，无需再加
              _this.setSuccessFn(id)
            }
            if ('sendChatMessage:cancel,sendChatMessage:ok'.indexOf(resSend.err_msg) < 0) {
              //错误处理
              _this.$dialog({ message: '发送失败：' + JSON.stringify(resSend) })
            }
          })
          _this.$toast.clear()
        } else {
          _this.$toast.clear()
          //错误处理
          _this.$dialog({ message: '进入失败：' + JSON.stringify(res) })
        }
      })
    },
    setSuccessFn(targetId) {
      getSuccess(targetId).then(() => {
        this.getData(0)
      })
    },
    async getData(state, customExecuteTargetAttachId = null) {
      this.isLoad = true

      if (this.isPhoneCallSop && !this.externalUserId) {
        // 拨打电话SOP模式下，获取待拨打任务
        const params = {
          executeSubState: state,
          businessType: 7
        }

        // 传递必需的参数
        params.sopBaseId = this.sopBaseId
        // 如果传递了自定义的executeTargetAttachId，使用它；否则使用默认的
        const targetAttachId = customExecuteTargetAttachId || this.executeTargetAttachId
        params.executeTargetAttachId = targetAttachId
        this.addDebugLog('info', `重新获取数据 - sopBaseId: ${this.sopBaseId}, executeTargetAttachId: ${targetAttachId}${customExecuteTargetAttachId ? ' (指定时间段)' : ' (默认时间段)'}`)

        // 记录查询接口调用信息
        this.addDebugLog('api', `准备调用查询接口`, {
          url: '/sop/findCustomerSopContent',
          method: 'GET',
          requestParams: params
        })

        try {
          const res = await getCustomerSopContent(params)

          // 记录查询接口响应信息
          this.addDebugLog('api', `查询接口响应`, {
            url: '/sop/findCustomerSopContent',
            status: res.code,
            message: res.msg,
            dataCount: res.data?.weCustomerSops?.length || 0,
            success: res.code === 200
          })

          if (res.code === 200) {
            this.form = res.data || { customerName: '拨打电话SOP', weCustomerSops: [] }
            // 拨打电话SOP不需要resetData处理，直接使用weCustomerSops
            // phoneCallCustomers计算属性会自动处理数据
          } else {
            this.form = { customerName: '拨打电话SOP', weCustomerSops: [] }
          }
        } catch (error) {
          // 记录查询接口错误信息
          this.addDebugLog('api', `查询接口调用失败`, {
            url: '/sop/findCustomerSopContent',
            error: error.message,
            requestParams: params
          })
          this.addDebugLog('error', 'API调用异常', error)
          this.form = { customerName: '拨打电话SOP', weCustomerSops: [] }
        } finally {
          this.isLoad = false
        }
      } else {
        // 普通客户SOP模式
        getCustomerSopContent({ targetId: this.externalUserId, executeSubState: state }).then((res) => {
          if (res.code === 200) {
            this.form = res.data
            this.getStage()
            let arr = this.form.weCustomerSops
            if (arr && arr.length) {
              this.resetData(arr)
            } else {
              this.dataList = []
            }
          }
          this.isLoad = false
        })
      }
    },
    resetData(array) {
      this.dataList = []
      array.forEach((dd) => {
        let data = this.sorted(dd.weCustomerSopContents, 'pushStartTime')
        let val = Object.keys(data)
        let result = []
        val.forEach((ff) => {
          result.push({
            sopBaseId: dd.sopBaseId,
            sopName: dd.sopName,
            businessType: dd.businessType,
            open: false,
            list: data[ff],
          })
        })
        this.dataList.push(...result)
      })
      this.setStateData()
    },
    setStateData() {
      this.dataList.forEach((dd) => {
        let str = compareTime(dd.list[0].pushStartTime, dd.list[0].pushEndTime)
        if (str === 'before') {
          dd.type = 3 // 未到推送时间
        } else if (str === 'after') {
          dd.type = 2 // 已过推送时间
          dd.time = this.computeTime(dd.list[0].pushEndTime)
        } else {
          dd.time = this.computeTime(dd.list[0].pushEndTime)
          dd.type = 1 // 已到推送时间
        }
      })
    },
    computeTime(t2) {
      let aftert2 = new Date(t2.replace(/-/g, '/')) //转换
      let data = new Date() //获取当前时间
      let times = aftert2.getTime() - data.getTime() //时间差的毫秒数
      // let days = parseInt(times / (24 * 1000 * 3600)) //计算相差的天数
      let leave = times % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
      let h = Math.abs(parseInt(leave / (3600 * 1000))) //计算小时数
      //计算分钟数
      let h_leave = leave % (3600 * 1000)
      let min = Math.abs(parseInt(h_leave / (60 * 1000)))
      //计算秒数
      let min_leave = h_leave % (60 * 1000)
      let sec = Math.abs(parseInt(min_leave / 1000))
      return `${h}时${min}分${sec}左右`
    },
    sorted(array, key) {
      let groups = {}
      array.forEach(function(item) {
        let value = item[key]
        groups[value] = groups[value] || []
        groups[value].push(item)
      })
      return groups
    },
  },
}
</script>

<style lang="less" scoped>
.unit {
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(60, 136, 240, 0.1);
  border: 1px solid #e1edfc;
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
}
.operation {
  display: flex;
  margin-top: 5px;
  align-items: center;
  justify-content: space-between;
}
.swarmsSOP {
  width: 100%;
  // height: 100vh;
  background: #fafafa;
  // position: relative;

  .swarmsSOP_box {
    padding: 10px 10px 10px 10px;

    // 拨打电话SOP的客户列表不需要左右padding
    .phone-call-customers {
      margin: 0 -10px;
    }
  }

  .swarmsSOP_message {
    border-radius: 3px;
    display: flex;
    flex-direction: column;
    font-weight: 500;
    background: #fff;
    min-height: 25px;
    padding: 9px 10px;
    color: #222121;
    width: 100%;

    .swarmsSOP_message_top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .swarmsSOP_message_top_left {
        display: flex;

        .swarmsSOP_message_top_left_userImg {
          width: 50px;
          height: 50px;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
          flex: none;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .swarmsSOP_message_top_left_info {
          padding-top: 2px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 10px;

          img {
            width: 16px;
            height: 16px;
          }
        }
      }

      .swarmsSOP_message_top_right {
        display: flex;
        flex-direction: column;
        gap: 8px;
        flex: none;

        .track-state {
          padding: 6px 15px;
          background-color: #2c8cf0;
          color: #fff;
          border-radius: 5px;
          text-align: center;
        }

        .phone-button {
          padding: 6px 15px;
          background-color: #67c23a;
          color: #fff;
          border-radius: 5px;
          cursor: pointer;
          text-align: center;
          font-size: 14px;

          &:active {
            opacity: 0.8;
          }

          &.primary {
            background-color: #ff6b35;
            font-weight: bold;
            font-size: 16px;
            padding: 8px 20px;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
            animation: pulse 2s infinite;
          }

          &.disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
            animation: none;
            box-shadow: none;

            &:active {
              opacity: 1;
            }
          }
        }

        .phone-button-small {
          padding: 4px 8px;
          background-color: #409eff;
          color: #fff;
          border-radius: 3px;
          cursor: pointer;
          font-size: 12px;
          margin-left: 10px;
          transition: all 0.3s ease;

          &:hover {
            background-color: #337ecc;
          }

          &:active {
            opacity: 0.8;
          }
        }



        @keyframes pulse {
          0% {
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
          }
          50% {
            box-shadow: 0 4px 16px rgba(255, 107, 53, 0.6);
          }
          100% {
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
          }
        }
      }
    }
  }
}

.phone-call-tip {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  padding: 40px 24px;
  border-radius: 20px;
  text-align: center;
  margin: 24px 12px;
  box-shadow: 0 8px 25px rgba(255, 107, 53, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: shimmer 3s ease-in-out infinite;
  }

  .tip-icon {
    font-size: 64px;
    margin-bottom: 20px;
    animation: bounce 2s ease-in-out infinite;
  }

  .tip-text {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .tip-desc {
    font-size: 15px;
    opacity: 0.9;
    line-height: 1.4;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
}

@keyframes shimmer {
  0%, 100% {
    transform: translateX(-50%) translateY(-50%) rotate(0deg);
  }
  50% {
    transform: translateX(-50%) translateY(-50%) rotate(180deg);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.phone-call-customers {
  width: 100%;
  padding: 0;

  .customer-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    padding: 20px 16px;
    margin-bottom: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 107, 53, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(135deg, #ff6b35, #f7931e);
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
      border-color: rgba(255, 107, 53, 0.2);
    }

    .customer-info {
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0; // 防止flex子元素溢出

      .customer-avatar {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16px;
        flex-shrink: 0;
        border: 3px solid rgba(255, 107, 53, 0.1);
        transition: all 0.3s ease;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .default-avatar {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
      }

      .customer-details {
        flex: 1;
        min-width: 0; // 防止文本溢出

        .customer-name {
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 6px;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .customer-phone {
          font-size: 15px;
          color: #7f8c8d;
          margin-bottom: 8px;
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
          letter-spacing: 0.5px;
        }

        .customer-status {
          .status-pending {
            color: #ff6b35;
            font-size: 13px;
            font-weight: 500;
            background: rgba(255, 107, 53, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
          }

          .status-completed {
            color: #27ae60;
            font-size: 13px;
            font-weight: 500;
            background: rgba(39, 174, 96, 0.1);
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
          }
        }
      }
    }

    .customer-actions {
      margin-left: 12px;
      flex-shrink: 0;

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .phone-call-btn, .voice-call-btn {
        padding: 12px 20px;
        color: white;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        outline: none;
        white-space: nowrap;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s;
        }

        &:active {
          transform: translateY(0);
        }
      }

      .phone-call-btn {
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:active {
          box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
        }

        &.disabled {
          background: linear-gradient(135deg, #bdc3c7, #95a5a6);
          cursor: not-allowed;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &::before {
              left: -100%;
            }
          }
        }
      }

      .voice-call-btn {
        background: linear-gradient(135deg, #4CAF50, #45a049);
        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);

          &::before {
            left: 100%;
          }
        }

        &:active {
          box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
        }

        &.disabled {
          background: linear-gradient(135deg, #bdc3c7, #95a5a6);
          cursor: not-allowed;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &:hover {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &::before {
              left: -100%;
            }
          }
        }
      }
    }
  }
}

.swarmsSOP_tabBar {
  margin-top: 10px;
  display: flex;
  border-radius: 10px 10px 0 0;
  background-color: #fff;
  // border: 1px solid #7f7f7f;
  overflow: hidden;
  .swarmsSOP_tabBar_li {
    background-color: #fff;
    color: #7f7f7f;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    height: 40px;
  }
  .swarmsSOP_tabBar_li1 {
    background-color: #7f7f7f;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
    height: 40px;
  }
}
.swarmsSOP_content {
  overflow: hidden;
  border-radius: 10px;
  margin-top: 20px;
  width: 100%;
  background-color: #fff;
  .swarmsSOP_content_top1 {
    background-color: #90f8e3;
    padding: 15px 10px;
    .swarmsSOP_content_top_text {
      color: #1abc9c;
    }
  }
  .swarmsSOP_content_top2 {
    background-color: #fdb2a0;
    padding: 15px 10px;
    .swarmsSOP_content_top_text {
      color: #ed4014;
    }
  }
  .swarmsSOP_content_top3 {
    background-color: #b5b1b1;
    padding: 15px 10px;
    .swarmsSOP_content_top_text {
      color: #7f7f7f;
    }
  }
}
.swarmsSOP_content_title {
  padding: 10px 10px;
  display: flex;
  justify-content: space-between;
}
.swarmsSOP_message_content_box_li_right {
  width: 16px;
  height: 16px;
  img {
    width: 100%;
    height: 100%;
  }
}
.swarmsSOP_content_li {
  background-color: #f6faff;
  padding: 10px;
  .swarmsSOP_content_li_text {
    background-color: #fff;
  }
}

/* 调试面板样式 */
.debug-panel {
  position: fixed;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 0, 0, 0.95);
  color: #fff;
  z-index: 9999;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  font-family: 'Courier New', monospace;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid #333;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px 8px 0 0;
}

.debug-controls {
  display: flex;
  gap: 8px;
}

.debug-btn {
  padding: 4px 8px;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.debug-btn:hover {
  background: #0056b3;
}

.debug-btn.close-btn {
  background: #ff3b30;
}

.debug-btn.close-btn:hover {
  background: #d70015;
}

.debug-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  font-size: 12px;
  line-height: 1.4;
}

.debug-log {
  margin-bottom: 8px;
  padding: 6px;
  border-radius: 4px;
  border-left: 3px solid #007aff;
}

.debug-log.info {
  background: rgba(0, 122, 255, 0.1);
  border-left-color: #007aff;
}

.debug-log.warn {
  background: rgba(255, 149, 0, 0.1);
  border-left-color: #ff9500;
}

.debug-log.error {
  background: rgba(255, 59, 48, 0.1);
  border-left-color: #ff3b30;
}

.debug-log.success {
  background: rgba(52, 199, 89, 0.1);
  border-left-color: #34c759;
}

.debug-log.api {
  background: rgba(88, 86, 214, 0.1);
  border-left-color: #5856d6;
}

.debug-level {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  margin-right: 8px;
  min-width: 40px;
  text-align: center;
}

.debug-log.info .debug-level {
  background: #007aff;
  color: white;
}

.debug-log.warn .debug-level {
  background: #ff9500;
  color: white;
}

.debug-log.error .debug-level {
  background: #ff3b30;
  color: white;
}

.debug-log.success .debug-level {
  background: #34c759;
  color: white;
}

.debug-log.api .debug-level {
  background: #5856d6;
  color: white;
}

.debug-time {
  color: #888;
  font-size: 10px;
  margin-right: 8px;
}

.debug-message {
  color: #fff;
}

.debug-data {
  margin-top: 4px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 10px;
  white-space: pre-wrap;
  word-break: break-all;
  color: #ccc;
  max-height: 200px;
  overflow-y: auto;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: #007aff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.debug-toggle:hover {
  background: #0056b3;
}

</style>
