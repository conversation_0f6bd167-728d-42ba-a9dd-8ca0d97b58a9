(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1629f248"],{9536:function(t,s,r){"use strict";r.r(s);var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"container"},[t.qrcode?s("div",{staticClass:"qrcode",attrs:{id:"qrcode"}},[s("div",{staticClass:"qrcode-tip"},[t._v("长按识别进入群组")]),s("div",{staticClass:"qrcode-content"},[s("img",{attrs:{id:"qrcodeImg",src:t.qrcode}})])]):s("div",{staticClass:"error-content",attrs:{id:"error"}},[s("div",{attrs:{id:"message"}},[t._v(t._s(t.errMsg))])])])},i=[],n=r("b199"),c=r("ed08"),o={name:"",components:{},data(){return{qrcode:"",errMsg:""}},computed:{},watch:{},created(){this.$toast.loading({duration:0,forbidClick:!0}),Object(c["d"])().then(({openId:t,unionId:s,nickName:r,avatar:e})=>{if(t){const i=getUrlParam("fissionId"),c=getUrlParam("recordId");if(!(i&&c&&t))return void(this.errMsg="缺失必要数据, 请联系管理员");Object(n["c"])(i,c,{name:r,unionid:s,userid:t,avatar:e}).then(({data:t})=>{this.qrcode=t,this.$toast.clear()}).catch(()=>{this.errMsg="未获取到可用的二维码",this.$toast.clear()})}else this.errMsg="获取用户信息失败",this.$toast.clear()}).catch(()=>{this.errMsg="获取用户信息失败",this.$toast("授权异常，请刷新重试")})},mounted(){},methods:{}},a=o,d=(r("d892"),r("2877")),u=Object(d["a"])(a,e,i,!1,null,"134c0e68",null);s["default"]=u.exports},b199:function(t,s,r){"use strict";r.d(s,"c",(function(){return o})),r.d(s,"a",(function(){return a})),r.d(s,"b",(function(){return d}));var e=r("b775");const i=window.sysConfig.services.wecom,n=i+"/fission",c=window.sysConfig.services.weChat+"/fission";function o(t,s,r){return Object(e["a"])(n+`/complete/${t}/records/${s}`,r,"post")}function a(t){return Object(e["a"])({url:c+"/findFissionPoster",params:t})}function d(t){return Object(e["a"])({url:c+"/findWeFissionProgress",params:t})}},d892:function(t,s,r){"use strict";r("e5dc")},e5dc:function(t,s,r){}}]);