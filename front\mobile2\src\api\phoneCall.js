import request from '@/utils/request'
const wecom = window.sysConfig.services.wecom
const service = wecom + '/phone'

/**
 * 记录拨打电话
 * @param {Object} data 拨打记录数据
 * @returns {Promise}
 */
export function recordPhoneCall(data) {
  return request({
    url: service + '/recordCall',
    method: 'post',
    data
  })
}



/**
 * 获取拨打电话统计信息
 * @param {string} sopBaseId SOP基础ID（可选）
 * @returns {Promise}
 */
export function getCallStatistics(sopBaseId) {
  return request({
    url: service + '/statistics',
    params: { sopBaseId }
  })
}

/**
 * 获取特定SOP和时间段的拨打统计信息
 * @param {string} sopBaseId SOP基础ID
 * @param {string} executeTargetAttachId 执行目标附件ID（可选）
 * @returns {Promise}
 */
export function getSopCallStatistics(sopBaseId, executeTargetAttachId) {
  return request({
    url: service + '/sopStatistics',
    params: { sopBaseId, executeTargetAttachId }
  })
}



/**
 * 查询拨打记录
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getCallRecords(params) {
  return request({
    url: service + '/records',
    params
  })
}
