(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7077cd1e","chunk-097ac29a"],{1419:function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"no-data"},[e("svg-icon",{staticClass:"no-data-icon",attrs:{name:"no-data"}}),e("span",[t._v(t._s(t.description))])],1)},a=[],r={props:{description:{type:String,default:"暂无数据"}}},n=r,l=(i("8b72"),i("2877")),c=Object(l["a"])(n,s,a,!1,null,"6b545506",null);e["default"]=c.exports},"270e":function(t,e,i){},"6f29":function(t,e,i){"use strict";i.r(e);var s=function(){var t=this,e=t._self._c;return e("div",[e("div",[e("van-search",{attrs:{"show-action":"",placeholder:"请输入关键词，在当前分类分组下搜索"},on:{search:function(e){return t.search(1)}},scopedSlots:t._u([{key:"action",fn:function(){return[e("span",{on:{click:function(e){return t.search(1)}}},[t._v("搜索")]),e("span",{staticClass:"ml5",on:{click:t.reset}},[t._v("重置")])]},proxy:!0}]),model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),e("div",{staticClass:"tabs"},[e("van-tabs",{on:{click:t.getCodeCategoryListFn},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},t._l(t.list,(function(i,s){return e("van-tab",{key:s,attrs:{title:i.name}},[e("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"stretch",width:"100%",height:"calc(100vh - 120px)"}},[e("div",{staticClass:"item-list"},t._l(t.groupList,(function(i,s){return e("div",{key:i.id,staticClass:"item",class:{active:t.groupIndex==s},on:{click:function(e){return t.switchGroup(s,i)}}},[e("div",{staticClass:"name"},[t._v(t._s(i.name))])])})),0),e("div",{staticStyle:{width:"70%",overflow:"auto"}},[e("List",{ref:"list"+s,refInFor:!0,attrs:{sideId:i.id,mediaType:i.type+""}})],1)])])})),1)],1)])},a=[],r=(i("14d9"),i("d800")),n=function(){var t=this,e=t._self._c;return e("div",[e("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:function(e){return t.getList(1)}},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":t.finishedText,error:t.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(e){t.error=e},load:function(e){return t.getList()}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[t.loading||t.list&&t.list.length?t._e():e("NoData"),t._l(t.list,(function(i,s){return e("div",{key:s,staticClass:"itemList"},[e("div",{staticClass:"content bfc-o",on:{click:function(e){return t.showPopup(i)}}},["18"!==i.mediaType?e("div",{staticClass:"title"},[t._v(t._s(i.materialName))]):t._e(),"4"==i.mediaType?e("p",{staticClass:"text"},[t._v(t._s(i.content))]):t._e(),"0"==i.mediaType&&i.materialUrl?e("van-image",{attrs:{width:"50",height:"50",src:i.materialUrl}}):t._e(),"0"!=i.mediaType||i.materialUrl?t._e():e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}})],1),9==i.mediaType?e("div",{staticClass:"centerStyle"},[i.coverUrl?e("van-image",{attrs:{width:"50",height:"50",src:i.coverUrl}}):e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"imgText"}})],1),e("div",{staticClass:"contentStyle"},[t._v(t._s(i.content))])],1):t._e(),11==i.mediaType?e("div",{staticClass:"centerStyle"},[i.coverUrl?e("van-image",{attrs:{width:"50",height:"50",src:i.coverUrl}}):e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}})],1)],1):t._e(),12==i.mediaType?e("div",{staticClass:"centerStyle"},[i.coverUrl?e("van-image",{attrs:{width:"50",height:"50",src:i.coverUrl}}):e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"article"}})],1),e("div",{staticClass:"contentStyle"},[t._v(t._s(i.digest))])],1):t._e(),2==i.mediaType?e("div",{staticClass:"centerStyle"},[e("van-image",{attrs:{width:"50",height:"50",src:i.coverUrl}}),e("div",{staticClass:"contentStyle"},[t._v(t._s(i.digest))])],1):t._e(),5==i.mediaType?e("div",{staticClass:"centerStyle"},[i.materialUrl?e("van-image",{attrs:{width:"50",height:"50",src:i.materialUrl}}):e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}})],1),e("div",{staticClass:"contentStyle"},[t._v(t._s(i.digest))])],1):t._e(),"3"===i.mediaType?e("div",{staticStyle:{display:"flex"}},[i.materialUrl?e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:i.materialUrl?t.filType(i.materialUrl):""}})],1):t._e(),e("div",{staticClass:"contentStyle"},[t._v(t._s(i.digest))])]):t._e(),"18"===i.mediaType?e("div",{staticStyle:{display:"flex"}},[e("div",{staticClass:"icon-style"},[e("svg-icon",{staticClass:"icon-style",attrs:{name:"collect"}})],1),e("div",{staticClass:"contentTitle"},[t._v(t._s(i.materialName))])]):t._e()],1),e("div",{staticClass:"info"},[e("div",{staticClass:"flex fr"},["0"!==i.mediaType&&"4"!==i.mediaType&&"11"!==i.mediaType&&"3"!==i.mediaType&&"18"!==i.mediaType?e("div",{staticClass:"preview",on:{click:function(e){return t.preview(i)}}},[t._v(" 预览 ")]):t._e(),"3"==i.mediaType?e("a",{staticClass:"preview",attrs:{target:"_blank",href:i.materialUrl}},[t._v("预览")]):t._e(),e("div",{staticClass:"action",on:{click:function(e){return t.send(i)}}},[t._v("发送")])])])])}))],2)],1),e("van-popup",{attrs:{closeable:"",round:""},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},["4"==t.mediaType?e("div",{staticClass:"popText"},[e("div",{staticClass:"popTitle"},[t._v(t._s(t.clickObj?t.clickObj.materialName:""))]),e("div",{staticClass:"popContent"},[t._v(" "+t._s(t.clickObj?t.clickObj.content:"")+" ")])]):t._e(),"0"==t.mediaType?e("div",{staticClass:"popPic"},[e("van-image",{attrs:{width:"200",src:t.clickObj?t.clickObj.materialUrl:""}})],1):t._e()])],1)},l=[],c=i("1419"),o={components:{NoData:c["default"]},props:{sideId:{type:String,default:""},mediaType:{type:String,default:null}},data(){return{userName:"",refreshing:!1,loading:!1,finished:!1,finishedText:"暂无数据",keyword:void 0,error:!1,pageSize:10,list:[],collectList:[],query:{pageNum:1,pageSize:10,materialName:"",mediaType:""},clickObj:null,categoryId:null,show:!1}},watch:{userId(){this.sideId||this.getList(1)}},computed:{userId(){return this.$store.state.userId}},created(){this.getList(),this.sysId=sessionStorage.getItem("sysId")},mounted(){},methods:{filType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(".");let i=e[e.length-1];return"pdf"===i?"pdf":"doc"===i||"docx"===i?"word":"ppt"===i||"pptx"===i||"pps"===i||"pptsx"===i?"ppt":""},filPicType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(".");let i=e[e.length-1];return"pdf"===i?window.sysConfig.DEFAULT_H5_PDF:["doc","docx"].includes(i)?window.sysConfig.DEFAULT_H5_WORDE:["ppt","pptx","pps","pptsx"].includes(i)?window.sysConfig.DEFAULT_H5_PPT:""},showPopup(t){["4","0"].includes(t.mediaType)&&(this.show=!0,this.clickObj={...t})},getList(t){this.loading=!0,this.finished=!1;let e=this.pageSize;this.query.mediaType=this.mediaType,this.query.materialName=this.keyword,this.query.pageSize=e,t&&(this.query.pageNum=t),Object(r["d"])(this.query).then(({rows:t,total:e})=>{1==this.query.pageNum&&(this.list=[]),this.list.push(...t),this.loading=!1,this.refreshing=!1,this.list.length>=+e?(0==this.list.length?(this.query.pageNum=1,this.finishedText=""):this.finishedText="没有更多了",this.finished=!0):this.query.pageNum++}).catch(()=>{this.error=!0,this.loading=!1})},search(t,e,i){this.keyword=e,i?this.query.categoryId=i:delete this.query.categoryId,this.getList(t)},preview(t){"0"!==t.id&&"4"!==t.id&&this.$router.push({name:"metrialDetail",query:{materiaId:t.id,isBack:!0}})},addSend(t){this.userName=sessionStorage.getItem("userName");let e={contentId:t.id,sendBy:this.userName,sendById:this.sysId,resourceType:1};Object(r["b"])(e).then(t=>{console.log(t)})},send(t){this.$toast.loading({message:"正在发送...",duration:0,forbidClick:!0});let e=void 0,i=this;wx.invoke("getContext",{},(async function(s){if("getContext:ok"==s.err_msg){e=s.entry;let l={};try{if(!["single_chat_tools","group_chat_tools","normal"].includes(e))return void i.$toast("入口错误："+e);let s={0:"image",2:"news",3:"news",4:"text",5:"news",9:"news",11:"miniprogram",12:"news",18:"news",19:"news"},n=window.document.location.origin+window.sysConfig.BASE_URL+"#/metrialDetail?materiaId="+t.id+"&userId="+sessionStorage.getItem("sysId");switch(t.mediaType){case"4":default:l.text={content:t.content?t.content:""};break;case"0":let e={url:t.materialUrl,type:s[t.mediaType],name:t.materialName};try{let a=await Object(r["f"])(e);if(!a.data)return void i.$toast("获取素材id失败");l[s[t.mediaType]]={mediaid:a.data.mediaId}}catch(a){return}break;case"9":l.news={link:t.materialUrl?t.materialUrl:" ",title:t.materialName?t.materialName:" ",desc:t.content?t.content:" ",imgUrl:t.coverUrl||window.sysConfig.DEFAULT_H5_PIC};break;case"2":case"12":case"19":l.news={link:n,title:t.materialName?t.materialName:" ",desc:t.digest,imgUrl:t.coverUrl||window.sysConfig.DEFAULT_H5_PIC};break;case"3":l.news={link:n,title:t.materialName?t.materialName:" ",desc:t.digest?t.digest:" ",imgUrl:i.filPicType(t.materialUrl)};break;case"5":l.news={link:n,title:t.materialName?t.materialName:" ",desc:t.digest?t.digest:" ",imgUrl:t.materialUrl||window.sysConfig.DEFAULT_H5_PIC};break;case"18":l.news={link:t.materialUrl?t.materialUrl:" ",title:t.materialName?t.materialName:" ",desc:t.digest?t.digest:" ",imgUrl:window.sysConfig.DEFAULT_H5_COLLECT};break;case"11":l.miniprogram={appid:t.digest?t.digest:" ",title:t.materialName?t.materialName:" ",imgUrl:t.coverUrl?t.coverUrl:" ",page:t.materialUrl?t.materialUrl:" "};break}l.msgtype=s[t.mediaType]}catch(n){i.$dialog({message:"err"+JSON.stringify(n)})}wx.invoke("sendChatMessage",l,(function(e){if("sendChatMessage:ok"==e.err_msg&&i.addSend(t),"sendChatMessage:cancel,sendChatMessage:ok".indexOf(e.err_msg)<0){let t=JSON.stringify(e.err_msg);'"sendChatMessage:fail, not allow to cross corp"'===t?i.$dialog({message:"发送失败：小程序配置有误"}):i.$dialog({message:"发送失败："+JSON.stringify(e)})}})),i.$toast.clear()}else i.$toast.clear(),i.$dialog({message:"进入失败："+JSON.stringify(s)})}))}}},d=o,u=(i("d7f9"),i("2877")),m=Object(u["a"])(d,n,l,!1,null,"306d919e",null),p=m.exports,h={components:{List:p},props:{},data(){return{groupIndex:0,keyword:"",active:0,list:[],loading:!1,finished:!1,show:!1,groupList:[],query:{},type:""}},watch:{userId(){this.getList()}},computed:{userId(){return this.$store.state.userId}},beforeCreate(){},created(){this.getList()},mounted(){},methods:{getList(){Object(r["h"])().then(t=>{this.list=t.data||[],this.getCodeCategoryListFn()})},search(t){this.$refs["list"+this.active]&&this.$refs["list"+this.active][0].search(t,this.keyword,this.query.categoryId)},reset(){this.keyword="",this.$nextTick(()=>this.search(1))},switchGroup(t,e){this.groupIndex=t,this.query.categoryId=e.id,this.search(1)},getCodeCategoryListFn(t=0){this.type=this.list[t].type;let e="";e="18"===this.type?"15":this.type,Object(r["c"])({mediaType:e}).then(t=>{this.groupList=[{name:"全部"}],t.data&&this.groupList.push(...t.data),this.groupIndex=0})}}},g=h,y=(i("737a"),Object(u["a"])(g,s,a,!1,null,"cb4ee0ec",null));e["default"]=y.exports},"737a":function(t,e,i){"use strict";i("8e6f")},"8b72":function(t,e,i){"use strict";i("270e")},"8e6f":function(t,e,i){},c01a:function(t,e,i){},d7f9:function(t,e,i){"use strict";i("c01a")},d800:function(t,e,i){"use strict";i.d(e,"h",(function(){return l})),i.d(e,"f",(function(){return c})),i.d(e,"c",(function(){return o})),i.d(e,"d",(function(){return d})),i.d(e,"b",(function(){return u})),i.d(e,"g",(function(){return m})),i.d(e,"i",(function(){return p})),i.d(e,"e",(function(){return h})),i.d(e,"a",(function(){return g}));var s=i("b775");const a=window.sysConfig.services.wecom,r=a+"/material",n=window.sysConfig.services.weChat;function l(){return Object(s["a"])({url:r+"/media/type"})}function c(t){return Object(s["a"])({url:r+"/temporaryMaterialMediaId",params:t})}function o(t){return Object(s["a"])({url:a+"/category/list",params:t})}function d(t){return Object(s["a"])({url:r+"/list",params:t})}function u(t){return Object(s["a"])({url:a+"/material/action/addSend",method:"post",data:t})}function m(t){return Object(s["a"])({url:n+"/track/material/auth/get",params:t})}function p(t){return Object(s["a"])({url:a+"/talk/list",params:t})}function h(t){return Object(s["a"])({url:n+"/material/get/"+t})}function g(t){return Object(s["a"])({url:a+"/material/action/addAllSend",method:"post",data:t})}}}]);