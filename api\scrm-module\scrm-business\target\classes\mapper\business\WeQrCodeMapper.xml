<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeQrCodeMapper">



    <resultMap type="org.scrm.domain.qr.WeQrCode" id="WeQrCodeResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="autoAdd" column="auto_add" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="configId" column="config_id" jdbcType="VARCHAR"/>
        <result property="qrCode" column="qr_code" jdbcType="VARCHAR"/>
        <result property="qrWelcomeOpen" column="qr_welcome_open" jdbcType="INTEGER"/>
        <result property="qrPriorityUserWelcome" column="qr_priority_user_welcome" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap type="org.scrm.domain.qr.vo.WeQrCodeDetailVo" id="WeQrCodeDetailResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="qrGroupName" column="group_name" jdbcType="INTEGER"/>
        <result property="autoAdd" column="auto_add" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
        <result property="ruleMode" column="rule_mode" jdbcType="INTEGER"/>
        <result property="openSpareUser" column="open_spare_user" jdbcType="INTEGER"/>
        <result property="isExclusive" column="is_exclusive" jdbcType="INTEGER"/>
        <result property="state" column="state" jdbcType="VARCHAR"/>
        <result property="configId" column="config_id" jdbcType="VARCHAR"/>
        <result property="qrCode" column="qr_code" jdbcType="VARCHAR"/>
        <result property="qrWelcomeOpen" column="qr_welcome_open" jdbcType="INTEGER"/>
        <result property="qrPriorityUserWelcome" column="qr_priority_user_welcome" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remarkType" column="remark_type"/>
        <result property="logoUrl" column="logo_url"/>
        <result property="backupQrUrl" column="backup_qr_url"/>
        <result property="startPeriodAnnex" column="start_period_annex"/>
        <collection property="qrTags" ofType="org.scrm.domain.tag.vo.WeTagVo">
            <result property="tagId" column="tag_id" jdbcType="INTEGER"/>
            <result property="tagName" column="tag_name" jdbcType="VARCHAR"/>
        </collection>
        <collection property="qrAttachments" ofType="org.scrm.domain.qr.WeQrAttachments">
            <result property="msgType" column="msg_type" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
            <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
            <result property="mediaId" column="media_id" jdbcType="VARCHAR"/>
            <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
            <result property="appId" column="app_id" jdbcType="VARCHAR"/>
            <result property="realType" column="real_type" jdbcType="INTEGER"/>
            <result property="materialId" column="material_id" jdbcType="BIGINT"/>
        </collection>
    </resultMap>

    <sql id="selectWeQrCodeVo">
        select id, name, group_id, auto_add, type, rule_type, state, scan_num, config_id, qr_code, rule_mode,open_spare_user,is_exclusive, qr_welcome_open, qr_priority_user_welcome, create_by, create_time, update_by, update_time, del_flag from we_qr_code
    </sql>

    <sql id="selectWeQrCodeDetailVo">
        select
            wqc.id,
            wqc.name,
            wqc.group_id,
            if(wqc.group_id = 1,'默认分组',wc.name) as group_name,
            wqc.auto_add,
            wqc.type,
            wqc.rule_type,
            wqc.rule_mode,
            wqc.open_spare_user,
            wqc.is_exclusive,
            wqc.state,
            wqc.scan_num,
            wqc.config_id,
            wqc.qr_code,
            wqc.rule_mode,
            wqc.open_spare_user,
            wqc.is_exclusive,
            wqc.qr_welcome_open,
            wqc.qr_priority_user_welcome,
            wqc.update_time,
            wqtr.tag_id,
            wt.name as tag_name,
            wqa.msg_type,
            wqa.content,
            wqa.title,
            wqa.description,
            wqa.pic_url,
            wqa.link_url,
            wqa.media_id,
            wqa.file_url,
            wqa.app_id,
            wqa.real_type,
            wqa.material_id,
            wqc.remark_type,
            wqc.logo_url,
            wqc.backup_qr_url,
            wqc.start_period_annex
        from
            we_qr_code wqc
        left join we_qr_tag_rel wqtr on wqtr.qr_id = wqc.id and wqtr.business_type = 1  and wqtr.del_flag = 0
        left join we_qr_scope wqs on wqs.qr_id = wqc.id and wqs.del_flag = 0
        left join we_qr_attachments wqa on wqa.qr_id = wqc.id and wqa.business_type = 1 and wqa.del_flag = 0
        left join we_tag wt on wt.tag_id = wqtr.tag_id and wt.del_flag = 0
        left join we_category wc on wc.id = wqc.group_id and wc.del_flag = 0
        where 1=1
    </sql>

    <select id="getQrDetailByQrId" resultMap="WeQrCodeDetailResult">
        <include refid="selectWeQrCodeDetailVo"/>
        <if test="qrId != null">
            and wqc.id = #{qrId}
        </if>
    </select>

    <select id="getQrDetailByQrIds" resultMap="WeQrCodeDetailResult">
        <include refid="selectWeQrCodeDetailVo"/>
        <if test="qrIds != null and qrIds.size > 0">
            and wqc.id in
            <foreach item="qrId" collection="qrIds" index="index" open="(" separator="," close=")">
                #{qrId}
            </foreach>
        </if>
        and wqc.del_flag = 0
    </select>

    <select id="getQrCodeList" resultType="java.lang.Long">
        select
            distinct wqc.id
        from we_qr_code wqc
            left join we_qr_tag_rel wqtr on wqtr.qr_id = wqc.id and wqtr.business_type = 1 and wqtr.del_flag = 0
            left join we_qr_scope wqs on wqs.qr_id = wqc.id and wqs.del_flag = 0
            left join we_qr_attachments wqa on wqa.qr_id = wqc.id and wqa.business_type = 1  and  wqs.del_flag = 0
            left join sys_user su on su.we_user_id = wqs.user_id and su.del_flag = 0
            where 1=1
        <if test="groupId != null and groupId != ''">
            and wqc.group_id = #{groupId}
        </if>
        <if test="qrName != null and qrName != ''">
            and wqc.name like concat('%', #{qrName}, '%')
        </if>
        <if test="qrUserIds != null and qrUserIds != ''">
            and su.we_user_id in
            <foreach item="item" index="index" collection="qrUserIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(wqc.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
        </if>
        <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
            AND date_format(wqc.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
        </if>
        and wqc.del_flag = 0
    </select>

    <resultMap id="QrScopeResult" type="org.scrm.domain.qr.vo.WeQrScopeVo">
        <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="scopeId" column="scope_id" jdbcType="VARCHAR"/>
        <result property="beginTime" column="begin_time" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
        <result property="workCycle" column="work_cycle" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <collection property="weQrUserList" ofType="org.scrm.domain.qr.vo.WeQrScopeUserVo">
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        </collection>
        <collection property="weQrPartyList" ofType="org.scrm.domain.qr.vo.WeQrScopePartyVo">
            <result property="party" column="party" jdbcType="VARCHAR"/>
            <result property="partyName" column="party_name" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <select id="getWeQrScopeByTime" resultMap="QrScopeResult">
        select
        wqs.qr_id,
        wqs.scope_id,
        wqs.scope_type,
        wqs.`type`,
        wqs.begin_time,
        wqs.end_time,
        wqs.work_cycle,
        wqs.user_id,
        wqs.party,
        wqs.status
        from we_qr_scope wqs
        where  find_in_set(dayofweek(curdate())-1,wqs.work_cycle)
        and date_format(concat(date_format(curdate(),'%Y-%m-%d '),wqs.begin_time), '%H:%i:%s') &lt;= date_format(#{formatTime},'%H:%i:%s')
        and date_format(concat(date_format(curdate(),'%Y-%m-%d '),wqs.end_time), '%H:%i:%s') &gt;= date_format(#{formatTime},'%H:%i:%s')
        <if test="qrId != null">
            and wqs.qr_id = #{qrId}
        </if>
        and wqs.del_flag = 0
    </select>

    <select id="getQrDetailByState" resultMap="WeQrCodeDetailResult">
        <include refid="selectWeQrCodeDetailVo"/>
        <if test="state != null and state != ''">
            and wqc.state = #{state} limit 1
        </if>
    </select>

    <select id="getWeQrCodeScanSheetCount" resultType="org.scrm.domain.qr.vo.WeQrCodeScanLineCountVo">
        SELECT
        sd.DATE AS dateTime,
        (
            SELECT
            count( DISTINCT external_userid )
            FROM
            we_customer
            WHERE DATE ( add_time )= sd.DATE
            <if test="state !=null and state !=''">
                AND  state = #{state}
            </if>
            AND del_flag=0
        ) AS today,
        (
        SELECT
        count( DISTINCT external_userid )
        FROM
        we_customer
        WHERE DATE ( add_time ) &lt;= sd.DATE
        <if test="state !=null and state !=''">
            AND  state = #{state}
        </if>
        AND del_flag=0
        ) AS total,
        (
        SELECT
        IFNULL( SUM( pv_num ), 0 )
        FROM
        we_common_link_stat
        WHERE DATE ( create_time )= sd.DATE
        <if test="qrId != null">
            AND short_id = #{qrId}
        </if>
        AND del_flag = 0
        ) AS todayLinkVisitsTotal,

        (
        SELECT
        IFNULL( SUM( pv_num ), 0 )
        FROM
        we_common_link_stat
        WHERE DATE ( create_time )  &lt;= sd.DATE
        <if test="qrId != null">
            AND short_id = #{qrId}
        </if>
        AND del_flag = 0
        ) AS linkVisitsTotal,

        (
        SELECT
        IFNULL( SUM( uv_num ), 0 )
        FROM
        we_common_link_stat
        WHERE
        DATE ( create_time )= sd.DATE
        <if test="qrId != null">
            AND short_id = #{qrId}
        </if>
        AND del_flag = 0
        ) AS todayLinkVisitsPeopleTotal,

        (
        SELECT
        IFNULL( SUM( uv_num ), 0 )
        FROM
        we_common_link_stat
        WHERE
        DATE ( create_time ) &lt;= sd.DATE
        <if test="qrId != null">
            AND short_id = #{qrId}
        </if>
        AND del_flag = 0
        ) AS linkVisitsPeopleTotal

        FROM
        sys_dim_date sd
        <where>
            <if test="beginTime != null and beginTime != '' and endTime !=null and endTime !=''">
                DATE_FORMAT( sd.DATE, '%Y-%m-%d' ) BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
        ORDER BY sd.DATE ASC
    </select>

    <select id="getWeQrCodeScanCount" resultType="org.scrm.domain.qr.vo.WeQrCodeScanCountVo">
        SELECT
        (
            SELECT
            count( DISTINCT external_userid )
            FROM
            we_customer
            WHERE DATE ( add_time )= CURDATE()
            <if test="state !=null and state !=''">
                AND  state = #{state}
            </if>
            AND del_flag=0
        ) AS today,
        (
            SELECT
              count( DISTINCT external_userid )
            FROM
            we_customer
            WHERE del_flag=0
            <if test="state !=null and state !=''">
                AND  state = #{state}
            </if>
        ) AS total,
        (
            SELECT
            IFNULL( SUM( pv_num ), 0 )
            FROM
            we_common_link_stat
            WHERE DATE ( create_time )=  CURDATE()
            <if test="qrId != null">
                AND short_id = #{qrId}
            </if>
            AND del_flag = 0
        ) AS todayLinkVisitsTotal,
        (
            SELECT
            IFNULL( SUM( pv_num ), 0 )
            FROM
            we_common_link_stat
            WHERE DATE ( create_time )  &lt;= CURDATE()
            <if test="qrId != null">
                AND short_id = #{qrId}
            </if>
            AND del_flag = 0
        ) AS linkVisitsTotal,
        (
            SELECT
            IFNULL( SUM( uv_num ), 0 )
            FROM
            we_common_link_stat
            WHERE
            DATE ( create_time )=  CURDATE()
            <if test="qrId != null">
                AND short_id = #{qrId}
            </if>
            AND del_flag = 0
        ) AS todayLinkVisitsPeopleTotal,

        (
            SELECT
            IFNULL( SUM( uv_num ), 0 )
            FROM
            we_common_link_stat
            WHERE
            DATE ( create_time ) &lt;= CURDATE()
            <if test="qrId != null">
                AND short_id = #{qrId}
            </if>
            AND del_flag = 0
        ) AS linkVisitsPeopleTotal

    </select>


</mapper>
