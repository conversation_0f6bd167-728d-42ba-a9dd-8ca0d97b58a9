<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMarketSubMapper">

    <select id="findMarketTab"  resultType="org.scrm.domain.WeMarketCountVo$MarketTab">
        SELECT
            COUNT(DISTINCT unionid) AS viewTotalNumber,
            IFNULL(SUM(CASE WHEN awards_state != 0 THEN 1 ELSE 0 END),0) AS joinNumber,
            IFNULL(SUM(CASE WHEN awards_state = 1 THEN 1 ELSE 0 END),0) AS drawNumber,
            IFNULL(SUM(CASE WHEN DATE_FORMAT(view_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END),0) AS viewTdNumber,
            IFNULL(SUM(CASE WHEN awards_state != 0 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END),0) AS joinTdNumber,
            IFNULL(SUM(CASE WHEN awards_state = 1 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END),0) AS drawTdNumber,
            IFNULL(SUM(CASE WHEN DATE_FORMAT(view_time, '%Y-%m-%d') =DATE_ADD(CURDATE(), INTERVAL -1 DAY) THEN 1 ELSE 0 END),0) AS viewYdNumber,
            IFNULL(SUM(CASE WHEN awards_state != 0 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = DATE_ADD(CURDATE(), INTERVAL -1 DAY) THEN 1 ELSE 0 END),0) AS joinYdNumber,
            IFNULL(SUM(CASE WHEN awards_state = 1 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = DATE_ADD(CURDATE(), INTERVAL -1 DAY) THEN 1 ELSE 0 END),0) AS drawYdNumber,
            IFNULL(ROUND(
            SUM(CASE WHEN awards_state != 0 THEN 1 ELSE 0 END) / COUNT(DISTINCT unionid),
            4
            ), 0) AS aRate,
            IFNULL(ROUND(
            SUM(CASE WHEN awards_state = 1 THEN 1 ELSE 0 END) / SUM(CASE WHEN awards_state != 0 THEN 1 ELSE 0 END),
            4
            ), 0) AS wRate,
            IFNULL(
                ROUND(
                (COUNT(DISTINCT unionid)-SUM(CASE WHEN DATE_FORMAT(view_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END))
                /(SUM(CASE WHEN awards_state != 0 THEN 1 ELSE 0 END)
                -SUM(CASE WHEN awards_state != 0 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END))
                ,4
                ),0) as tDaRate,
            IFNULL(
                ROUND(
                (SUM(CASE WHEN awards_state = 1 THEN 1 ELSE 0 END)-SUM(CASE WHEN awards_state = 1 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END))
                /
                (SUM(CASE WHEN awards_state != 0 THEN 1 ELSE 0 END)
                -SUM(CASE WHEN awards_state != 0 AND DATE_FORMAT(draw_time, '%Y-%m-%d') = CURDATE() THEN 1 ELSE 0 END))
                ,4) ,0) as tdWRate
        FROM (
                 SELECT unionid, awards_state, view_time, draw_time
                 FROM we_market_record
                 <where>
                     <if  test="marketIds != null and marketIds.size() > 0">
                         market_id in
                         <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                             #{marketId}
                         </foreach>
                     </if>
                 </where>
             ) AS filtered_records
    </select>

    <select id="findMarketTrend" resultType="org.scrm.domain.WeMarketCountVo$MarketTrend">
        SELECT
        date as xTime,
        (SELECT count(*) FROM we_market_record where DATE_FORMAT(view_time,'%Y-%m-%d')=date
        <if  test="marketIds != null and marketIds.size() > 0">
           and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>
          ) as viewTotalNumber,
        (SELECT count(*) FROM we_market_record where DATE_FORMAT(view_time,'%Y-%m-%d')=date and awards_state !=0
        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>
        ) as joinNumber,
        (SELECT count(*) FROM we_market_record where DATE_FORMAT(view_time,'%Y-%m-%d')=date and awards_state =1
        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>
        ) as drawNumber
        FROM
        sys_dim_date
        <where>
            <if test="beginTime != null and beginTime !='' and endTime !=null and endTime != ''">
                DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{beginTime} and #{endTime}
            </if>
        </where>
        ORDER BY date ASC
    </select>

    <select id="findLottery" resultType="org.scrm.domain.WeMarketCountVo$Lottery">
        SELECT
            wms.awards,
            IFNULL((SELECT COUNT(*) FROM we_market_record wmr  WHERE wmr.mark_sub_id=wms.id),0) AS drawNumber
        FROM
            we_market_sub wms
        <where>
            <if test="marketId != null">
                wms.market_id= #{marketId}
            </if>
        </where>
        GROUP BY wms.awards
        ORDER BY wms.prize_sort ASC
    </select>

    <select id="findMarketViewTop" resultType="org.scrm.domain.WeMarketCountVo$MarketViewTop">
        SELECT
            wm.market_name as marketName,
            IFNULL((SELECT COUNT(DISTINCT wmr.unionid) FROM we_market_record wmr WHERE wmr.market_id = wm.id), 0) AS viewTotalNumber
        FROM
            we_market wm
        <where>
            <if  test="marketIds != null and marketIds.size() > 0">
                and id in
                <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
        </where>
        ORDER BY
           viewTotalNumber DESC
            LIMIT 5
    </select>

    <select id="findMrketJoinTop" resultType="org.scrm.domain.WeMarketCountVo$MrketJoinTop">
        SELECT
            wm.market_name as marketName,
            IFNULL(
                    ROUND( (SELECT COUNT(DISTINCT wmr.unionid) FROM we_market_record wmr WHERE wmr.awards_state != 0 AND wmr.market_id = wm.id)
		/ (SELECT COUNT(DISTINCT wmr.unionid) FROM we_market_record wmr WHERE wmr.market_id = wm.id),4)
                ,0)   as aRate
        FROM
            we_market wm
        <where>
            <if  test="marketIds != null and marketIds.size() > 0">
                and id in
                <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
        </where>
        ORDER BY
            aRate DESC
            LIMIT 5
    </select>

    <select id="findMarketTable" resultType="org.scrm.domain.MarketTable">
        SELECT
        date as xTime,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where DATE_FORMAT(view_time,'%Y-%m-%d')&lt;=date
            <if  test="marketIds != null and marketIds.size() > 0">
                and market_id in
                <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                    #{marketId}
                </foreach>
            </if>
            ) as viewTotalNumber,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where wmr.awards_state !=0 AND DATE_FORMAT(view_time,'%Y-%m-%d')&lt;=date

        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>

        ) as joinNumber,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where wmr.awards_state =1 AND DATE_FORMAT(view_time,'%Y-%m-%d')&lt;=date


        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>
        ) as drawNumber,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where DATE_FORMAT(view_time,'%Y-%m-%d')=date

        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>

        ) as viewTdNumber,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where wmr.awards_state !=0 AND DATE_FORMAT(view_time,'%Y-%m-%d')=date

        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>

        ) as joinTdNumber,
        (SELECT count(DISTINCT unionid) from we_market_record wmr where wmr.awards_state =1 AND DATE_FORMAT(view_time,'%Y-%m-%d')=date


        <if  test="marketIds != null and marketIds.size() > 0">
            and market_id in
            <foreach item="marketId" collection="marketIds" open="(" separator="," close=")">
                #{marketId}
            </foreach>
        </if>

        ) as drawTdNumber
        FROM
        sys_dim_date
        <where>
            <if test="beginTime != null and beginTime !='' and endTime !=null and endTime != ''">
                DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{beginTime} and #{endTime}
            </if>
        </where>
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>
    </select>

    <select id="countMarketTable" resultType="long">
        SELECT count(*)
        FROM
        sys_dim_date
        <where>
            <if test="beginTime != null and beginTime !='' and endTime !=null and endTime != ''">
                DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{beginTime} and #{endTime}
            </if>
        </where>
    </select>




</mapper>
