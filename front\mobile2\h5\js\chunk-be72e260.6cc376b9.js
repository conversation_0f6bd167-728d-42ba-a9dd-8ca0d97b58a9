(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-be72e260"],{"23ba":function(t,e,a){"use strict";a.d(e,"c",(function(){return n})),a.d(e,"e",(function(){return l})),a.d(e,"g",(function(){return o})),a.d(e,"h",(function(){return c})),a.d(e,"a",(function(){return d})),a.d(e,"f",(function(){return u})),a.d(e,"b",(function(){return h})),a.d(e,"d",(function(){return g}));var s=a("b775");const i=window.sysConfig.services.wecom,r=i+"/groupchat";function n(t){return Object(s["a"])({url:r+"/get/"+t})}function l(t){return Object(s["a"])({url:r+"/member/page/list",params:t})}function o(t){return Object(s["a"])({url:r+"/makeGroupTag",method:"post",data:t})}function c(){return Object(s["a"])({url:r+"/synch"})}function d({pageNum:t,pageSize:e,chatId:a}){return Object(s["a"])({url:r+"/findGroupTrajectory/"+a,params:{pageNum:t,pageSize:e}})}function u(t){return Object(s["a"])({url:i+"/tag/list",params:t})}function h(t){return Object(s["a"])({url:i+"/customer/findWeCustomerListByApp",params:t})}function g(t){return Object(s["a"])({url:i+"/groupchat/page/listByApp",params:t})}},"244b":function(t,e,a){"use strict";a("d556")},"45d1":function(t,e,a){},"474e":function(t,e,a){"use strict";a("45d1")},4866:function(t,e,a){},"6bd4":function(t,e,a){"use strict";a.r(e);a("e9f5"),a("a732");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"portrait",style:{"margin-bottom":t.isQueryChatId?"50px":"0px"}},[e("div",{staticClass:"details"},[e("div",{staticClass:"detail mb20"},[e("div",{staticClass:"left"},[e("div",{staticClass:"img"},[e("img",{attrs:{src:t.form.avatar,alt:""}})]),e("div",{staticClass:"right"},[e("div",{staticClass:"key"},[t._v(" "+t._s(t.form.groupName)+" ")]),e("div",{staticClass:"data"},[t._v("群主："+t._s(t.form.groupLeaderName))])])])])]),e("div",{staticClass:"card"},[e("div",{staticClass:"title"},[t._v("群公告")]),e("div",[t._v(" "+t._s(t.form.notice||"无")+" ")])]),e("div",{staticClass:"realationship card"},[e("div",{staticClass:"detail"},[e("div",{staticClass:"title"},[t._v("群成员")]),e("div",{staticClass:"data",on:{click:function(e){return t.goRoute("/groupMembers")}}},[t._v("详情"),e("van-icon",{attrs:{name:"arrow"}})],1)]),e("div",{staticClass:"detail"},[e("div",{staticClass:"boxnumber"},[e("div",{staticClass:"number"},[t._v(t._s(t.form.memberNum))]),e("p",{staticClass:"key"},[t._v("群总人数")])]),e("div",{staticClass:"boxnumber"},[e("div",{staticClass:"number"},[t._v(t._s(t.form.customerNum))]),e("p",{staticClass:"key"},[t._v("客户总数")])]),e("div",{staticClass:"boxnumber"},[e("div",{staticClass:"number"},[t._v(t._s(t.form.toDayMemberNum))]),e("p",{staticClass:"key"},[t._v("今日进群")])]),e("div",{staticClass:"boxnumber"},[e("div",{staticClass:"number"},[t._v(t._s(t.form.toDayExitMemberNum))]),e("p",{staticClass:"key"},[t._v("今日退群")])])])]),e("div",{staticClass:"userlabel card"},[e("div",{staticClass:"detail"},[e("div",{staticClass:"title"},[t._v("客群标签")]),e("div",{staticClass:"data",attrs:{"is-link":""},on:{click:function(e){return t.labelEdit()}}},[t._v("编辑"),e("van-icon",{attrs:{name:"arrow"}})],1)]),t.form.tags&&t.form.tags.length?e("div",{staticClass:"labelstyle mt15"},t._l(t.form.tags,(function(a,s){return e("div",{key:s,staticClass:"label"},[t._v(" "+t._s(a.name)+" ")])})),0):e("van-empty",{attrs:{"image-size":"50",description:"暂无数据"}})],1),e("div",{staticClass:"addwaiting card"},[e("div",{staticClass:"detail"},[e("div",{staticClass:"title"},[t._v("客群轨迹")]),e("div",{staticClass:"data",attrs:{"is-link":""},on:{click:t.sync}},[t._v("同步")])]),e("StepList",{ref:"stepList",attrs:{load:t.findTrajectory}})],1),e("div",{staticClass:"cancat-btn"},[t.isQueryChatId?e("van-button",{attrs:{type:"info",size:"normal",block:"",round:""},on:{click:t.concat}},[t._v(" 进入群聊 ")]):t._e()],1),e("van-action-sheet",{model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[e("van-nav-bar",{attrs:{title:"客户标签"}}),e("div",{staticClass:"content"},[t._l(t.alllabel,(function(a,s){return e("div",{key:s},[e("div",{staticClass:"mb15 mt5 tag-group-name"},[t._v(t._s(a.groupName))]),e("div",{staticClass:"labelstyle"},t._l(a.weTags,(function(a,s){return e("div",{key:s,staticClass:"label",class:t.addTag.some(t=>t.tagId==a.tagId)&&"active",on:{click:function(e){return t.clickLabel(a)}}},[t._v(" "+t._s(a.name)+" ")])})),0)])})),t.alllabel&&t.alllabel.length?t._e():e("van-empty",{attrs:{"image-size":"50",description:"暂无数据"}})],2),e("div",{staticClass:"save-btn"},[e("van-button",{attrs:{type:"info",size:"normal",block:"",round:""},on:{click:t.saveCustomerTag}},[t._v(" 保存 ")])],1)],1)],1)},i=[],r=(a("14d9"),a("f665"),a("7d54"),a("ab43"),a("23ba")),n=a("7f84"),l={components:{StepList:n["a"]},data(){return{show:!1,form:{groupName:"",groupLeaderName:"",notice:"",memberNum:0,customerNum:0,toDayMemberNum:0,toDayExitMemberNum:0,groupName:0,groupName:""},alllabel:[],addTag:[],loading:!1,finished:!1,loadingStep:!1,isQueryChatId:!1}},computed:{userId(){return this.$store.state.userId}},created(){let t=this.$route.query;this.chatId=t&&t.id,this.isQueryChatId=!!this.chatId,this.$toast.loading({message:"loading...",duration:0,forbidClick:!0}),this.isQueryChatId?(this.start(),this.$toast.clear()):this.init()},methods:{start(){this.getDetail(),this.refreshTrajectory()},init(){wx.invoke("getContext",{},t=>{if("getContext:ok"==t.err_msg){let e=t.entry;if(!["group_chat_tools","contact_profile"].includes(e))return void this.$toast("入口错误："+e);wx.invoke("getCurExternalChat",{},t=>{"getCurExternalChat:ok"==t.err_msg?(this.chatId=t.chatId,this.start()):this.$dialog({message:"进入失败："+JSON.stringify(t)}),this.$toast.clear()})}else this.$toast.clear(),this.$dialog({message:"进入失败："+JSON.stringify(t)})})},getDetail(){Object(r["c"])(this.chatId).then(({data:t})=>{t.tagIds&&t.tags&&(t.tagIds=t.tagIds.split(","),t.tagNames=t.tags.split(","),t.tags=t.tagIds.map((e,a)=>({id:e,name:t.tagNames[a]}))),this.form=t}).catch(t=>{console.log(t)})},getAllTags(){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0});let t={groupTagType:2};return Object(r["f"])(t).then(({rows:t})=>{this.alllabel=this.listTagOneArray=t,this.listTagOneArray=[],t.forEach(t=>{t.weTags.forEach(t=>{this.listTagOneArray.push(t)})}),this.$toast.clear()}).catch(t=>{console.log(t)})},findTrajectory(t){if(!this.chatId)return new Promise(()=>{});let e={chatId:this.chatId};return Object.assign(e,t),Object(r["a"])(e)},refreshTrajectory(){this.$nextTick(()=>{this.$refs.stepList.getList(1)})},goRoute(t){this.$router.push({path:t,query:{id:this.chatId}})},async labelEdit(){await this.getAllTags(),this.addTag=[];let t=this.form.tags,e=[];t&&t.forEach(t=>{let a=this.listTagOneArray.find(e=>e.tagId===t.id);a?this.addTag.push(a):e.push(t.name)}),this.show=!0},clickLabel(t){let e=this.addTag.findIndex(e=>t.tagId==e.tagId);-1==e?this.addTag.push({groupId:t.groupId,name:t.name,tagId:t.tagId}):this.addTag.splice(e,1)},saveCustomerTag(){Object(r["g"])({chatId:this.chatId,tagIds:this.addTag.map(t=>t.tagId)}).then(t=>{200==t.code&&(this.show=!1,this.getDetail(),this.$toast.success("保存成功"))}).catch(t=>{console.log(t)})},sync(){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0}),Object(r["h"])(this.userId).then(t=>{this.$toast.success("同步成功"),this.refreshTrajectory()}).finally(()=>{this.$toast.clear()})},concat(){wx.invoke("openExistedChatWithMsg",{chatId:this.chatId},t=>{"openExistedChatWithMsg:ok"==t.err_msg||this.$dialog({message:"进入失败："+JSON.stringify(t)})})}}},o=l,c=(a("244b"),a("2877")),d=Object(c["a"])(o,s,i,!1,null,"0b1858f3",null);e["default"]=d.exports},"7f84":function(t,e,a){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",[e("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:function(e){return t.getList(1)}},model:{value:t.refreshing,callback:function(e){t.refreshing=e},expression:"refreshing"}},[e("van-list",{attrs:{finished:t.finished,"finished-text":t.finishedText,error:t.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(e){t.error=e},load:function(e){return t.getList()}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,(function(a,s){return e("van-cell",{key:s},[e("p",{staticClass:"f12",staticStyle:{position:"relative"}},[t._v(" "+t._s(t.dateFormat(a[0].createTime,"yyyy-MM-dd w"))+" ")]),e("van-steps",{attrs:{direction:"vertical","inactive-color":t.color,"active-color":t.color,active:a.length}},t._l(a,(function(a,s){return e("van-step",{key:s,staticClass:"msg"},[e("span",{staticClass:"f12 po"},[t._v(t._s(t.dateFormat(a.createTime,"hh:mm")))]),e("span",{staticClass:"fs14"},[t._v(t._s(a.title))]),e("p",{staticClass:"fs14 con"},[t._v(t._s(a.content))]),a.weMaterialVo?e("MaterialCard",{attrs:{weMaterialVo:a.weMaterialVo}}):t._e()],1)})),1)],1)})),1)],1)],1)},i=[],r=(a("14d9"),a("e9f5"),a("7d54"),a("ac0e")),n=a("ed08"),l=function(){var t=this,e=t._self._c;return e("div",{key:t.index,staticClass:"itemList"},[e("div",{staticClass:"content bfc-o"},[e("div",{staticClass:"title"},[t._v(t._s(t.weMaterialVo.materialName))]),12==t.weMaterialVo.mediaType?e("div",{staticClass:"centerStyle"},[t.weMaterialVo.coverUrl?e("van-image",{attrs:{width:"40",height:"40",src:t.weMaterialVo.coverUrl}}):e("svg-icon",{staticClass:"icon-style",attrs:{name:"article"}}),e("div",{staticClass:"contentStyle"},[t._v(t._s(t.weMaterialVo.digest))])],1):t._e(),2==t.weMaterialVo.mediaType?e("div",{staticClass:"centerStyle"},[e("van-image",{attrs:{width:"40",height:"40",src:t.weMaterialVo.coverUrl}}),e("div",{staticClass:"contentStyle"},[t._v(t._s(t.weMaterialVo.digest))])],1):t._e(),5==t.weMaterialVo.mediaType?e("div",{staticClass:"centerStyle"},[t.weMaterialVo.materialUrl?e("van-image",{attrs:{width:"40",height:"40",src:t.weMaterialVo.materialUrl}}):e("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}}),e("div",{staticClass:"contentStyle"},[t._v(t._s(t.weMaterialVo.digest))])],1):t._e(),"3"===t.weMaterialVo.mediaType?e("div",{staticStyle:{display:"flex"}},[t.weMaterialVo.materialUrl?e("svg-icon",{staticClass:"icon-style",attrs:{name:t.weMaterialVo.materialUrl?t.filType(t.weMaterialVo.materialUrl):""}}):t._e(),e("span",{staticClass:"contentStyle"},[t._v(t._s(t.weMaterialVo.digest))])],1):t._e()]),e("div",{staticClass:"info"},[e("div",{staticClass:"flex fr"},["0"!==t.weMaterialVo.mediaType&&"4"!==t.weMaterialVo.mediaType&&"11"!==t.weMaterialVo.mediaType&&"3"!==t.weMaterialVo.mediaType?e("div",{staticClass:"preview",on:{click:function(e){return t.preview(t.weMaterialVo)}}},[t._v(" 预览 ")]):t._e(),"3"==t.weMaterialVo.mediaType?e("a",{staticClass:"preview",attrs:{target:"_blank",href:t.weMaterialVo.materialUrl}},[t._v(" 预览 ")]):t._e()])])])},o=[],c={data(){return{}},methods:{preview(t){"0"!==t.id&&"4"!==t.id&&this.$router.push({name:"metrialDetail",query:{materiaId:t.id,isBack:!0}})},filType(t){let e=JSON.parse(JSON.stringify(t));e=e.split(".");let a=e[e.length-1];return"pdf"===a?"pdf":"doc"===a||"docx"===a?"word":"ppt"===a||"pptx"===a||"pps"===a||"pptsx"===a?"ppt":""}},props:{weMaterialVo:{type:Array,default:null}}},d=c,u=(a("474e"),a("2877")),h=Object(u["a"])(d,l,o,!1,null,"5545e649",null),g=h.exports,m={components:{MaterialCard:g},props:{load:Function},data(){return{query:{pageNum:1,pageSize:10},content:"",finishedText:"",error:!1,type:0,list:[],loading:!1,finished:!1,refreshing:!1,dateFormat:n["b"],color:r["color"]}},watch:{},methods:{getList(t){this.loading=!0,this.finished=!1,t&&(this.query.pageNum=t),1==this.query.pageNum&&(this.list=[]),this.refreshing&&(this.list=[],this.refreshing=!1),this.load(this.query).then(({rows:t,total:e})=>{t=t||[];let a=[];t.forEach(t=>{let e=Object(n["b"])(t.createTime,"yyyyMMdd");a.includes(e)||a.push(e)}),a.sort((t,e)=>e-t);for(let s=0;s<a.length;s++){let e=[];for(let i=0;i<t.length;i++)a[s]==Object(n["b"])(t[i].createTime,"yyyyMMdd")&&e.push(t[i]);this.list.push(e)}this.loading=!1,this.refreshing=!1,e&&0!=e?t.length<this.query.pageSize?(this.finishedText="没有更多了",this.finished=!0):this.query.pageNum++:(this.finishedText="暂无数据",this.finished=!0)}).catch(t=>{this.loading=!1,this.finished=!0,this.error=!0,alert(t)})}}},f=m,p=(a("b537"),Object(u["a"])(f,s,i,!1,null,"32ad7ab4",null));e["a"]=p.exports},a732:function(t,e,a){"use strict";var s=a("23e7"),i=a("c65b"),r=a("2266"),n=a("59ed"),l=a("825a"),o=a("46c4"),c=a("2a62"),d=a("f99f"),u=d("some",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:u},{some:function(t){l(this);try{n(t)}catch(s){c(this,"throw",s)}if(u)return i(u,this,t);var e=o(this),a=0;return r(e,(function(e,s){if(t(e,a++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},ac0e:function(t,e,a){t.exports={color:"#07c060"}},b537:function(t,e,a){"use strict";a("4866")},d556:function(t,e,a){}}]);