<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSubstituteCustomerOrderCatalogueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="org.scrm.domain.substitute.customer.order.entity.WeSubstituteCustomerOrderCatalogue">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="sort" property="sort"/>
        <result column="is_fixed" property="fixed"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="create_by_id" property="createById"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, sort, is_fixed, create_time, update_time, create_by, update_by, update_by_id, create_by_id, del_flag
    </sql>

</mapper>
