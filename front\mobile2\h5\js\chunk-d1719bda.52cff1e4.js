(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d1719bda"],{"0766":function(t,a,s){},"365c":function(t,a,s){"use strict";s.d(a,"b",(function(){return c})),s.d(a,"c",(function(){return r})),s.d(a,"a",(function(){return d})),s.d(a,"d",(function(){return u}));var i=s("b775");const n=window.sysConfig.services.wecom,e=window.sysConfig.services.weChat,o=e+"/live";function c(t){return Object(i["a"])({url:n+"/operation/getCustomerAnalysisForApp",params:t})}function r(t){return Object(i["a"])({url:n+"/operation/getGroupAnalysisByApp",params:t})}function d(t){return Object(i["a"])({url:n+"/trajectory/findCompanyDynamics",params:t})}function u(t){return Object(i["a"])({url:o+"/getLivingCode",params:t})}},"3a5e":function(t,a,s){"use strict";var i=function(){var t=this,a=t._self._c;return a("van-overlay",{attrs:{show:t.isLoad,"z-index":"999"},on:{click:function(a){t.show=!1}}},[a("div",{staticClass:"popup"},[a("div",{staticClass:"pop-box"},[a("div",{staticClass:"pop-box-bg"}),a("div",{staticClass:"pop-box-content"},[a("svg-icon",{staticClass:"lodash-icon",attrs:{name:"lodash"}}),a("span",[t._v("数据查询中，请稍后...")])],1)])])])},n=[],e={props:{isLoad:{type:Boolean,default:!1}}},o=e,c=(s("97db"),s("2877")),r=Object(c["a"])(o,i,n,!1,null,"4cb44c8c",null);a["a"]=r.exports},9127:function(t,a,s){"use strict";s("0766")},"97db":function(t,a,s){"use strict";s("a507")},a507:function(t,a,s){},b34b:function(t,a,s){"use strict";s.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"dynamics"},[a("div",{staticClass:"dynamics-tabs"},[a("div",{class:0===t.activeIndex?"active":"",on:{click:function(a){return t.tabClick(0)}}},[t._v(" 全部 ")]),a("div",{class:1===t.activeIndex?"active":"",on:{click:function(a){return t.tabClick(1)}}},[t._v(" 客户动态 ")]),a("div",{class:5===t.activeIndex?"active":"",on:{click:function(a){return t.tabClick(5)}}},[t._v(" 客群动态 ")])]),a("div",{staticClass:"dynamics-content"},[a("van-list",{attrs:{loading:t.loading,finished:t.finished,"finished-text":"暂无更多数据","loading-text":"上划加载更多"},on:{load:t.onLoad}},t._l(t.dynamicsOutList,(function(s,i){return a("div",{key:i,staticClass:"dynamics-item"},[a("div",{staticClass:"dynamics-left"},[a("div",{staticClass:"top"},[a("span",{class:1===s.operatorType?"customer":"staff"},[t._v(t._s(1===s.operatorType?"客户":"员工"))]),a("span",[t._v(t._s(s.operatorName))])]),a("div",{staticClass:"bottom"},[a("span",{class:"删除员工"===s.action||"解散群聊"===s.action||"退出群聊"===s.action?"unnomal":"nomal"},[t._v(t._s(s.action)+"  ")]),a("span",[t._v(t._s(s.operatoredObjectName))])])]),a("div",{staticClass:"dynamics-right"},[a("span",[t._v(t._s(1===s.trajectoryType?"客户动态":5===s.trajectoryType?"客群动态":""))]),a("span",[t._v(t._s(s.createTime))])])])})),0)],1),a("Loading",{attrs:{isLoad:t.isLoad}})],1)},n=[],e=(s("e9f5"),s("7d54"),s("3a5e")),o=s("365c"),c={components:{Loading:e["a"]},data(){return{activeIndex:0,loading:!1,finished:!1,dynamicsFrom:{trajectoryType:"",dataScope:!1,pageSize:10,pageNum:1},dynamicsOutList:[],total:0,isLoad:!1}},methods:{tabClick(t){this.activeIndex=t,this.isLoad=!0,this.dynamicsFrom.trajectoryType=0===t?"":t,this.getDynamicsData(1)},onLoad(){this.total>=this.dynamicsOutList.length?(this.dynamicsFrom.pageNum++,this.getDynamicsData()):this.finished=!0},getDynamicsData(t){t&&(this.dynamicsFrom.pageNum=t),this.loading=!0,this.finished=!1,Object(o["a"])(this.dynamicsFrom).then(t=>{200===t.code&&(this.dynamicsFrom.pageNum>1?this.dynamicsOutList=[...this.dynamicsOutList,...t.rows]:this.dynamicsOutList=t.rows,this.total=Number(t.total),this.dynamicsOutList.forEach(t=>{t.operatoredObjectType=1===t.operatoredObjectType?"客户":2===t.operatoredObjectType?"员工":"客群"}),this.total<=this.dynamicsOutList.length&&(this.finished=!0)),this.loading=!1,this.isLoad=!1})}},mounted(){this.getDynamicsData()},created(){this.dynamicsFrom.dataScope=this.$route.query.dataScope}},r=c,d=(s("9127"),s("2877")),u=Object(d["a"])(r,i,n,!1,null,"ff06622e",null);a["default"]=u.exports}}]);