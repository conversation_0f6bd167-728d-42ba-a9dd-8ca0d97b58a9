<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMomentsInteracteMapper">

    <resultMap type="org.scrm.domain.moments.entity.WeMomentsInteracte" id="WeMomentsInteracteResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="momentsTaskId" column="moments_task_id" jdbcType="INTEGER"/>
        <result property="interacteUserId" column="interacte_user_id" jdbcType="VARCHAR"/>
        <result property="weUserId" column="we_user_id" jdbcType="VARCHAR"/>
        <result property="interacteType" column="interacte_type" jdbcType="INTEGER"/>
        <result property="momentId" column="moment_id" jdbcType="VARCHAR"/>
        <result property="interacteUserType" column="interacte_user_type" jdbcType="INTEGER"/>
        <result property="interacteTime" column="interacte_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectWeMomentsInteracteVo">
        select id,
               moments_task_id,
               we_user_id,
               interacte_user_id,
               interacte_type,
               moment_id,
               interacte_user_type,
               interacte_time,
               create_time,
               create_by,
               create_by_id,
               update_time,
               update_by,
               update_by_id,
               del_flag
        from we_moments_interacte
    </sql>

</mapper>
