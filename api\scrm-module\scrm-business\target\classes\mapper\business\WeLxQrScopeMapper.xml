<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLxQrScopeMapper">



    <resultMap type="org.scrm.domain.WeLxQrScope" id="WeLxQrScopeResult">
                <result property="id" column="id" jdbcType="INTEGER"/>
                <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
                <result property="scopeType" column="scope_type" jdbcType="INTEGER"/>
                <result property="party" column="party" jdbcType="VARCHAR"/>
                <result property="userId" column="user_id" jdbcType="VARCHAR"/>
                <result property="position" column="position" jdbcType="VARCHAR"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            </resultMap>

    <sql id="selectWeLxQrScopeVo">
        select id, qr_id, scope_type, party, user_id, position, create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag from we_lx_qr_scope
    </sql>

</mapper>
