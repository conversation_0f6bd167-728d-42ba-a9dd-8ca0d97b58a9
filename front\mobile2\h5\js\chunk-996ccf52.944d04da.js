(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-996ccf52"],{"46ad":function(t,i,e){"use strict";e.d(i,"c",(function(){return r})),e.d(i,"a",(function(){return n})),e.d(i,"b",(function(){return c}));var a=e("b775");const s=window.sysConfig.services.wecom,l=s+"/moments";function r(t){return Object(a["a"])({url:l+"/mobile/list",params:t})}function n(t){return Object(a["a"])({url:l+"/mobile/get/"+t})}function c(t){return Object(a["a"])({url:l+"/group/send/finish",method:"post",data:t})}},"4cf5":function(t,i,e){"use strict";var a=function(){var t=this,i=t._self._c;return t.materialList&&t.materialList[0]?i("div",{staticClass:"show-file"},["0"===t.materialList[0].mediaType?i("div",{staticClass:"file-pic"},t._l(t.materialList,(function(t,e){return i("van-image",{key:e,staticClass:"file-pic-item",attrs:{width:"32%",height:"32%",radius:"8px",fit:"cover",src:t.materialUrl}})})),1):t._e(),"9"===t.materialList[0].mediaType?i("div",{staticClass:"file-other",on:{click:function(i){return t.preview(i,t.materialList[0].id)}}},[t.materialList[0].coverUrl?i("van-image",{attrs:{width:"40px",height:"40px",radius:"8px",fit:"cover",src:t.materialList[0].coverUrl}}):i("div",{staticClass:"icon-style"},[i("svg-icon",{staticClass:"icon-style",attrs:{name:"imgText"}})],1),i("div",{staticClass:"file-other-content"},[i("div",{staticClass:"other-title"},[t._v(t._s(t.materialList[0].materialName))]),i("div",{staticClass:"other-content"},[t._v(" "+t._s(t.materialList[0].content)+" ")])])],1):t._e(),"12"===t.materialList[0].mediaType?i("div",{staticClass:"file-other",on:{click:function(i){return t.preview(i,t.materialList[0].id)}}},[t.materialList[0].coverUrl?i("van-image",{attrs:{width:"40px",height:"40px",radius:"8px",fit:"cover",src:t.materialList[0].coverUrl}}):i("div",{staticClass:"icon-style"},[i("svg-icon",{staticClass:"icon-style",attrs:{name:"article"}})],1),i("div",{staticClass:"file-other-content"},[i("div",{staticClass:"other-title"},[t._v(t._s(t.materialList[0].materialName))]),i("div",{staticClass:"other-content"},[t._v(" "+t._s(t.materialList[0].digest)+" ")])])],1):t._e(),"2"===t.materialList[0].mediaType?i("div",{staticClass:"file-other",on:{click:function(i){return t.preview(i,t.materialList[0].id)}}},[i("van-image",{attrs:{width:"40px",height:"40px",radius:"8px",fit:"cover",src:t.materialList[0].coverUrl}}),i("div",{staticClass:"file-other-content"},[i("div",{staticClass:"other-title"},[t._v(t._s(t.materialList[0].materialName))]),i("div",{staticClass:"other-content"},[t._v(" "+t._s(t.materialList[0].digest)+" ")])])],1):t._e(),"5"===t.materialList[0].mediaType?i("div",{staticClass:"file-other",on:{click:function(i){return t.preview(i,t.materialList[0].id)}}},[t.materialList[0].materialUrl?i("van-image",{attrs:{width:"40px",height:"40px",radius:"8px",fit:"cover",src:t.materialList[0].materialUrl}}):i("div",{staticClass:"icon-style"},[i("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}})],1),i("div",{staticClass:"file-other-content"},[i("div",{staticClass:"other-title"},[t._v(t._s(t.materialList[0].materialName))]),i("div",{staticClass:"other-content"},[t._v(" "+t._s(t.materialList[0].digest)+" ")])])],1):t._e(),"3"===t.materialList[0].mediaType?i("div",{staticClass:"file-other",on:{click:function(i){return t.goLink(i,t.materialList[0].materialUrl)}}},[t.materialList[0].materialUrl?i("div",{staticClass:"icon-style"},[i("svg-icon",{staticClass:"icon-style",attrs:{name:t.materialList[0].materialUrl?t.filType(t.materialList[0].materialUrl):""}})],1):t._e(),i("div",{staticClass:"file-other-content"},[i("div",{staticClass:"other-title"},[t._v(t._s(t.materialList[0].materialName))]),i("div",{staticClass:"other-content"},[t._v(" "+t._s(t.materialList[0].digest)+" ")])])]):t._e()]):t._e()},s=[],l=(e("14d9"),{props:{materialList:{type:Array,default:()=>[]}},methods:{filType(t){let i=JSON.parse(JSON.stringify(t));i=i.split(".");let e=i[i.length-1];return"pdf"===e?"pdf":["doc","docx"].includes(e)?"word":["ppt","pptx","pps","pptsx"].includes(e)?"ppt":""},preview(t,i){t.stopPropagation(),"0"!==i&&"4"!==i&&this.$router.push({name:"metrialDetail",query:{materiaId:i,isBack:!0}})},goLink(t,i){t.stopPropagation(),window.open(i)}}}),r=l,n=(e("a071"),e("2877")),c=Object(n["a"])(r,a,s,!1,null,"66de18e8",null);i["a"]=c.exports},6848:function(t,i,e){},"7b0e":function(t,i,e){"use strict";e.r(i);var a=function(){var t=this,i=t._self._c;return i("div",[i("van-tabs",{attrs:{"title-active-color":"#1869FF","title-inactive-color":"#1D2129",sticky:""},on:{click:t.onClick},model:{value:t.active,callback:function(i){t.active=i},expression:"active"}},t._l(t.tabList,(function(e,a){return i("van-tab",{key:a,attrs:{title:e.title}},[i("van-list",{attrs:{loading:t.loading,finished:t.finished,"loading-text":"上划加载更多","finished-text":"没有更多了"},on:{load:t.onLoad}},t._l(t.list,(function(e,a){return i("van-cell",{key:a,attrs:{title:e.title}},[i("div",{staticClass:"card-item",on:{click:function(i){return t.goDetail(e.weMomentsTaskId)}}},[i("div",{staticClass:"task"},[i("div",{staticClass:"task-name"},[t._v(t._s(e.name))]),i("div",{staticClass:"task-button",class:e.executeStatus?"task-button-blue":"task-button-red"},[t._v(" "+t._s(e.executeStatus?"已执行":"未执行")+" ")])]),i("div",{staticClass:"task2"},[i("div",{staticClass:"task-create"},[i("div",{staticClass:"task-create-name"},[t._v(t._s(e.createBy))]),t._v(" 创建 ")]),i("div",[t._v(t._s(e.executeEndTime)+"执行")])]),3===t.query.status?i("div",{staticClass:"task-time"},[t._v(t._s(e.executeTime)+"--"+t._s(e.executeEndTime))]):t._e(),i("van-divider"),i("div",{staticClass:"guide"},[i("span",[t._v(t._s(e.content))])]),e.materialList&&e.materialList.length?i("ShowFile",{attrs:{materialList:e.materialList}}):t._e()],1)])})),1)],1)})),1)],1)},s=[],l=(e("14d9"),e("46ad")),r=e("4cf5"),n={components:{ShowFile:r["a"]},data(){return{active:0,tabList:[{title:"进行中"},{title:"已结束"}],list:[],finished:!1,loading:!1,query:{pageNum:1,pageSize:10,status:2},total:0}},mounted(){this.getList(!0)},methods:{onClick(t,i){this.query.status=0===t?2:3,this.finished=!1,this.total=0,this.list=[],this.query.pageNum=1,this.getList(!0)},onLoad(){this.total>this.list.length?(this.query.pageNum++,this.getList()):this.finished=!0},getList(t){this.loading=!0,Object(l["c"])(this.query).then(i=>{200===i.code&&(this.total=+i.total,t?this.list=i.rows:this.list.push(...i.rows),this.loading=!1,this.finished=!1)}).catch()},goDetail(t){this.$router.push({name:"friendsDetail",query:{id:t}})},dealTime(t){let i=+new Date,e=+new Date(t);return e>i?e-i:0}}},c=n,o=(e("f5d5"),e("2877")),d=Object(o["a"])(c,a,s,!1,null,"151fd32e",null);i["default"]=d.exports},a071:function(t,i,e){"use strict";e("dd98")},dd98:function(t,i,e){},f5d5:function(t,i,e){"use strict";e("6848")}}]);