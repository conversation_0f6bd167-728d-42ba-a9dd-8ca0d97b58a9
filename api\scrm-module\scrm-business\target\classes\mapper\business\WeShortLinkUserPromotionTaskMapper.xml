<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeShortLinkUserPromotionTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.WeShortLinkUserPromotionTask">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="template_type" property="templateType"/>
        <result column="template_id" property="templateId"/>
        <result column="send_status" property="sendStatus"/>
        <result column="msg_id" property="msgId"/>
        <result column="all_client_num" property="allClientNum"/>
        <result column="real_client_num" property="realClientNum"/>
        <result column="all_group_num" property="allGroupNum"/>
        <result column="real_group_num" property="realGroupNum"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="moment_id" property="momentId"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        user_id,
        template_type,
        template_id,
        send_status,
        msg_id,
        all_client_num,
        real_client_num,
        all_group_num,
        real_group_num,
        create_by,
        create_by_id,
        create_time,
        update_by,
        update_by_id,
        update_time,
        del_flag,
        moment_id
    </sql>

</mapper>
