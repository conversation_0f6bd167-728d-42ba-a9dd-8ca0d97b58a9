<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeQrAttachmentsMapper">



    <resultMap type="org.scrm.domain.qr.WeQrAttachments" id="WeQrAttachmentsResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
        <result property="businessType" column="business_type" jdbcType="INTEGER"/>
        <result property="msgType" column="msg_type" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="mediaId" column="media_id" jdbcType="VARCHAR"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
        <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="selectWeQrAttachmentsVo">
        select id, qr_id, business_type ,msg_type, content, media_id, title, description, file_url, link_url, pic_url, app_id, create_by, create_time, update_by, update_time, del_flag from we_qr_attachments
    </sql>

</mapper>
