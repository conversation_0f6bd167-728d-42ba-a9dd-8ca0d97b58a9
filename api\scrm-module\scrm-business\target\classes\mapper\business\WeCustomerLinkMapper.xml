<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCustomerLinkMapper">

    <select id="findLinkWeCustomer" resultType="org.scrm.domain.WeCustomerLinkCount">
        SELECT
          wcl.*
        FROM
        we_customer_link_count wcl
        WHERE wcl.del_flag=0
        <if test="weCustomerLinkCount.linkId != null and weCustomerLinkCount.linkId !=''">
            and  wcl.link_id=#{weCustomerLinkCount.linkId}
        </if>
        <if test="weCustomerLinkCount.beginTime !=null and weCustomerLinkCount.beginTime != '' and weCustomerLinkCount.endTime !='' and weCustomerLinkCount.endTime != null">
            AND  date_format(wcl.add_time,'%Y-%m-%d') BETWEEN #{weCustomerLinkCount.beginTime} AND #{weCustomerLinkCount.endTime}
        </if>
        <if test="weCustomerLinkCount.weUserId !=null and weCustomerLinkCount.weUserId !=''">
            AND wcl.we_user_id=#{weCustomerLinkCount.weUserId}
        </if>
    </select>


    <select id="findWeCustomerLinks" resultType="org.scrm.domain.WeCustomerLink">
        select * from we_customer_link  ${ew.customSqlSegment}
    </select>
</mapper>
