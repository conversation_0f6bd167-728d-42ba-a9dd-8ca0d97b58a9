<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeOperationCenterMapper">




    <select id="getCustomerAnalysisForApp"
            resultType="org.scrm.domain.operation.vo.WeCustomerAnalysisVo">
        select
            (select count(1) from we_customer where del_flag = 0

            <if test="weUserIds != null and weUserIds.size > 0">
                and add_user_id in
                <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                  #{weUserId}
                </foreach>
            </if>
            ) total_cnt,
            (select count(1) from we_customer where date_format(add_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
            <if test="weUserIds != null and weUserIds.size > 0">
                and add_user_id in
                <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                    #{weUserId}
                </foreach>
            </if>
            ) as td_cnt,
            (select count(1) from we_customer where date_format(track_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
            <if test="weUserIds != null and weUserIds.size > 0">
                and add_user_id in
                <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                    #{weUserId}
                </foreach>
            </if>
            ) as td_follow_up_customer,
            (select count(distinct external_userid) from we_customer where date_format(add_time,'%y%m%d') = date_format(curdate(),'%y%m%d')  and del_flag = 0
            <if test="weUserIds != null and weUserIds.size > 0">
                and add_user_id in
                <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                    #{weUserId}
                </foreach>
            </if>
            ) as td_net_cnt,
            (select count(1) from we_customer where date_format(loss_time,'%y%m%d') = date_format(curdate(),'%y%m%d')  and track_state = 5
            <if test="weUserIds != null and weUserIds.size > 0">
                and add_user_id in
                <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                    #{weUserId}
                </foreach>
            </if>
            ) as td_lost_cnt,
            (select sum(new_apply_cnt) from we_user_behavior_data where date_format(stat_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
                <if test="weUserIds != null and weUserIds.size > 0">
                    and user_id in
                    <foreach collection="weUserIds" item="weUserId" open="(" separator="," close=")">
                        #{weUserId}
                    </foreach>
                </if>
            ) as yd_new_apply_cnt
    </select>



    <select id="getCustomerRealCnt" resultType="org.scrm.domain.operation.vo.WeCustomerRealCntVo">
        SELECT
            date as xTime,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')&lt;=date
             AND del_flag=0
             <if test="query.userIds != null and query.userIds.size() > 0" >
              and add_user_id in
                 <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                     #{userId}
                 </foreach>
             </if>
            ) as totalCnt,

          ((SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')&lt;=date
            AND del_flag=0
            <if test="query.userIds != null and query.userIds.size() > 0" >
                and add_user_id in
                <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            ) - (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')&lt;=date
            AND del_flag=0
            <if test="query.userIds != null and query.userIds.size() > 0" >
                and add_user_id in
                <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            )) as repeatCnt,

            (SELECT count(external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date
                 AND del_flag=0
                <if test="query.userIds != null and query.userIds.size() > 0" >
                    and add_user_id in
                    <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
            ) as addCnt,
            (SELECT count(external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and track_state=5
                <if test="query.userIds != null and query.userIds.size() > 0" >
                    and add_user_id in
                    <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
             ) as lostCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(loss_time,'%Y-%m-%d')=date and del_flag=1
            <if test="query.userIds != null and query.userIds.size() > 0" >
                and add_user_id in
                <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            ) as userDelCnt,
            (SELECT count(DISTINCT external_userid) from we_customer where DATE_FORMAT(add_time,'%Y-%m-%d')=date and del_flag=0 and track_state!=5
            <if test="query.userIds != null and query.userIds.size() > 0" >
                and add_user_id in
                <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
           ) as netCnt,
            (SELECT IFNULL(sum(new_apply_cnt),0) from we_user_behavior_data WHERE DATE_FORMAT(stat_time,'%Y-%m-%d')=date

                <if test="query.userIds != null and query.userIds.size() > 0" >
                    and user_id in
                    <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                </if>
           ) as applyCnt
        FROM
        sys_dim_date
       <where>
          <if test="query.beginTime != null and query.beginTime !='' and query.endTime !=null and query.endTime != ''">
              DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{query.beginTime} and #{query.endTime}
          </if>
       </where>
        ORDER BY date ASC
    </select>

    <select id="getCustomerLostCnt" resultType="org.scrm.domain.operation.vo.WeCustomerRealCntVo">
        select
        x_time,
        lost_cnt
        from (
        select date_list.`date` as x_time ,
        IFNULL(real_data.lost_num, 0) as lost_cnt
        from
        (
        select `date`
        from sys_dim_date
        where `date` &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
        and `date` &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        ) as date_list
        left join
        (select DATE_FORMAT(wc.update_time, '%Y-%m-%d') day,
        sum(case when wc.track_state = 5 then 1 else 0 end) as lost_num
        from we_customer wc
        <where>
            <if test="userIds != null and userIds.size() > 0" >
                and wc.add_user_id in
                <foreach item="userId" collection="userIds" index="index" open="(" separator="," close=")">
                    #{userId}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds.size() > 0" >
                and exists(select 1 from sys_user su where wc.add_user_id = su.we_user_id and su.dept_id in
                <foreach item="deptId" collection="deptIds" index="index" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                )
            </if>
            and DATE_FORMAT(wc.update_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
            and DATE_FORMAT(wc.update_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        </where>
        group by day) as real_data on date_list.`date` = real_data.day order by day asc
        ) as result order by x_time asc
    </select>

    <select id="getNewCustomerLostCnt" resultType="org.scrm.domain.operation.vo.WeCustomerRealCntVo">
        SELECT
            DATE_FORMAT( add_time, '%Y-%m-%d' ) AS x_time,
            count(*) AS lost_cnt
        FROM
            we_customer
        WHERE
            track_state = 5
          <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
              AND DATE_FORMAT( add_time, '%Y-%m-%d' ) BETWEEN #{beginTime}
              AND #{endTime}
          </if>
    </select>
    <select id="getCustomerRank" resultType="org.scrm.domain.operation.vo.WeUserCustomerRankVo">
        select (select GROUP_CONCAT(DISTINCT su.user_name) from sys_user su where wc.add_user_id = su.we_user_id) user_name,
               count(1) as total_cnt
        from we_customer wc where del_flag = 0 and track_state !=5
        and DATE_FORMAT(wc.add_time,'%Y-%m-%d')  BETWEEN  #{beginTime} and  #{endTime}
        group by wc.add_user_id order by total_cnt desc limit 10
    </select>

    <select id="getGroupAnalysis" resultType="org.scrm.domain.operation.vo.WeGroupAnalysisVo">
        select
                (select count(1) from we_group where del_flag = 0
                    <if test="query.userIds != null and query.userIds.size() > 0" >
                        and owner in
                        <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                                               ) total_cnt,
                (select count(1) from we_group where date_format(create_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')
                    <if test="query.userIds != null and query.userIds.size() > 0" >
                        and owner in
                        <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                                               ) as td_group_add_cnt,
                (select count(1) from we_group where date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')
                    <if test="query.userIds != null and query.userIds.size() > 0" >
                        and owner in
                        <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                                               ) as yd_group_add_cnt,
                (select count(1) from we_group where date_format(update_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')  and del_flag = 1
                    <if test="query.userIds != null and query.userIds.size() > 0" >
                        and owner in
                        <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                            #{userId}
                        </foreach>
                    </if>
                                               ) as td_group_dissolve_cnt,
                (select count(1) from we_group where date_format(update_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')  and del_flag = 1
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>
                                               ) as yd_group_dissolve_cnt,
                (select count(user_id) from we_group_member where quit_time IS NULL AND del_flag = 0
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>

                                                            ) as member_total_cnt,
                (select count(user_id) from we_group_member where quit_time IS NULL AND  type = 1 and del_flag = 0
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>

                                                            ) as member_user_cnt,
                (select count(user_id) from we_group_member where date_format(join_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d') AND  quit_time IS NULL AND del_flag = 0
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>
                                                            ) as td_member_add_cnt,
                (select count(user_id) from we_group_member where date_format(join_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') AND  quit_time IS NULL AND del_flag = 0
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>

                 ) as yd_member_add_cnt,
                (select count(user_id) from we_group_member where date_format(quit_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d') and quit_time IS NOT NULL
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>
                                                            ) as td_member_quit_cnt,
                (select count(user_id) from we_group_member where date_format(quit_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') and quit_time IS NOT NULL
                        <if test="query.userIds != null and query.userIds.size() > 0" >
                            and owner in
                            <foreach item="userId" collection="query.userIds" index="index" open="(" separator="," close=")">
                                #{userId}
                            </foreach>
                        </if>
                                                            ) as yd_member_quit_cnt
    </select>


    <select id="getGroupAnalysisByApp" resultType="org.scrm.domain.operation.vo.WeGroupAnalysisVo">
        <choose>

         <when test="chatIds!= null and chatIds.size()>0" >
              select
            (select count(1) from we_group where del_flag = 0
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) total_cnt,
            (select count(1) from we_group where date_format(create_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) as td_group_add_cnt,
            (select count(1) from we_group where date_format(update_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')  and del_flag = 1
                <if test="owner != null and owner != '' " >
                    and owner = #{owner}
                </if>
            ) as tdGroupDissolveCnt,
            (select count(1) from we_group_member where del_flag = 0
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) as member_total_cnt,
            (select count(1) from we_group_member where date_format(join_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) as td_member_add_cnt,
            (select count(1) from we_group_member where date_format(quit_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d')
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) as td_member_quit_cnt
        </when>
            <otherwise>
                select
                    0 as  total_cnt,
                    0 as td_group_add_cnt,
                    0 as td_group_dissolve_cnt,
                    0 as member_total_cnt,
                    0 as td_member_add_cnt,
                    0 td_member_quit_cnt
            </otherwise>

        </choose>
   </select>





    <select id="getGroupMemberTotalCnt"
            resultType="org.scrm.domain.operation.vo.WeGroupTotalCntVo">
        SELECT
            date as xTime,
            (SELECT count(*) from we_group_member wgm
            LEFT JOIN we_group wg ON wgm.chat_id=wg.chat_id
            where DATE_FORMAT(wgm.join_time,'%Y-%m-%d')&lt;=date and wgm.quit_scene IS NULL and wgm.del_flag=0
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and wg.owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and wgm.chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            ) as totalCnt
        FROM
        sys_dim_date
        <where>
            <if test="beginTime !=null and beginTime != '' and endTime !='' and endTime != null">
                date_format(date,'%Y-%m-%d') BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
        ORDER BY date ASC
    </select>

    <select id="getGroupRealCnt" resultType="org.scrm.domain.operation.vo.WeGroupRealCntVo">
        SELECT
         date as xTime,
        (SELECT count(*) from we_group where DATE_FORMAT(add_time,'%Y-%m-%d')&lt;=date and del_flag=0
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        ) as totalCnt,

        (SELECT count(*) from we_group where DATE_FORMAT(add_time,'%Y-%m-%d')=date and del_flag=0
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        ) as addCnt,
        (SELECT count(*) from we_group where DATE_FORMAT(add_time,'%Y-%m-%d')=date  and del_flag=1
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        ) as dissolveCnt
        FROM
        sys_dim_date
        <where>
            <if test="beginTime !=null and beginTime != '' and endTime !='' and endTime != null">
                date_format(date,'%Y-%m-%d') BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
        ORDER BY date ASC

    </select>

    <select id="getGroupMemberRealCnt"
            resultType="org.scrm.domain.operation.vo.WeGroupMemberRealCntVo">
        select
        x_time,
        @total := @total + add_cnt AS total_cnt,
        add_cnt,
        quit_cnt
        from (
        select date_list.`date` as x_time ,
        IFNULL(real_data.add_num, 0) as add_cnt,
        IFNULL(quit_data.quit_num, 0) as quit_cnt
        from
        (
            select `date`
            from sys_dim_date
            where `date` &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
            and `date` &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        ) as date_list
        left join
        (select DATE_FORMAT(wgm.join_time, '%Y-%m-%d') day,
        count(1) as add_num
        from we_group_member wgm  where 1=1
        and wgm.del_flag = 0
        <if test="chatIds != null and chatIds.size() > 0" >
            and wgm.chat_id in
            <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>
        <if test="ownerIds != null and ownerIds.size() > 0" >
            and exists (select 1 from we_group wg where wg.chat_id = wgm.chat_id and wg.del_flag = 0 and wg.owner in
            <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                #{ownerId}
            </foreach>
            )
        </if>
        and DATE_FORMAT(wgm.join_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
        and DATE_FORMAT(wgm.join_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        group by day
        ,(SELECT @total := (select count(1) from we_group_member wgmt
        where
        wgmt.del_flag = 0
        and date_format(wgmt.join_time,'%Y-%m-%d') &lt; DATE_FORMAT(#{beginTime},'%Y-%m-%d')
        <if test="chatIds != null and chatIds.size() > 0" >
            and wgmt.chat_id in
            <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>
        <if test="ownerIds != null and ownerIds.size() > 0" >
            and exists (select 1 from we_group wg where wg.chat_id = wgmt.chat_id and wg.del_flag = 0 and wg.owner in
            <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                #{ownerId}
            </foreach>
            )
        </if>
        )))
        as real_data on date_list.`date` = real_data.day
        left join
        (select DATE_FORMAT(wgm.quit_time, '%Y-%m-%d') day,
        count(1) as quit_num
        from we_group_member wgm  where 1=1
        <if test="chatIds != null and chatIds.size() > 0" >
            and wgm.chat_id in
            <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>
        <if test="ownerIds != null and ownerIds.size() > 0" >
            and exists (select 1 from we_group wg where wg.chat_id = wgm.chat_id and wg.del_flag = 0 and wg.owner in
            <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                #{ownerId}
            </foreach>
            )
        </if>
        and DATE_FORMAT(wgm.quit_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{beginTime},'%Y-%m-%d')
        and DATE_FORMAT(wgm.quit_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
        group by day) as quit_data on date_list.`date` = quit_data.day
        ) as result order by x_time asc
    </select>
    <select id="getCustomerSessionAnalysis"
            resultType="org.scrm.domain.operation.vo.WeSessionCustomerAnalysisVo">
        select
            (select sum(chat_cnt) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')) as yd_chat_cnt,
            (select sum(chat_cnt) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d')) as byd_chat_cnt,
            (select sum(message_cnt) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as yd_message_cnt,
            (select sum(message_cnt) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d') ) as byd_message_cnt,
            (select avg(reply_percentage) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')) as yd_reply_percentage,
            (select avg(reply_percentage) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d') ) as byd_reply_percentage,
            (select avg(avg_reply_time) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as yd_avg_reply_time,
            (select avg(avg_reply_time) from we_user_behavior_data where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d') ) as byd_avg_reply_time;
    </select>

    <select id="getCustomerSessionTotalCnt"
            resultType="org.scrm.domain.operation.vo.WeSessionCustomerTotalCntVo">
        select
        date_format(stat_time,'%Y-%m-%d') as x_time,
        sum(wubd.chat_cnt) as chat_cnt,
        sum(wubd.message_cnt) as message_cnt,
        avg(wubd.reply_percentage) as reply_percentage,
        avg(wubd.avg_reply_time) as avg_reply_time
        from we_user_behavior_data wubd
        <where>
            <if test="userIds !=null and userIds !=''">
                and wubd.user_id in
                <foreach collection="userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptIds !=null and deptIds !=''">
                and exists (select 1 from sys_user wu where wu.we_user_id = wubd.user_id and wu.dept_id in
                <foreach collection="deptIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            and date_format(stat_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            and date_format(stat_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </where>
        group by wubd.stat_time
        order by x_time
    </select>

    <select id="getUserChatRank" resultType="org.scrm.domain.operation.vo.WeSessionUserChatRankVo">
        select (select su.user_name from sys_user su where wubd.user_id = su.we_user_id limit 1) user_name,
               sum(wubd.chat_cnt) as chat_cnt
        from we_user_behavior_data wubd
        <where>
            and date_format(wubd.stat_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            and date_format(wubd.stat_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </where>
        group by user_id order by chat_cnt desc limit 5
    </select>
    <select id="getUserAvgReplyTimeRank"
            resultType="org.scrm.domain.operation.vo.WeSessionUserAvgReplyTimeRankVo">
        select (select su.user_name from sys_user su where wubd.user_id = su.we_user_id limit 1) user_name,
               avg(wubd.avg_reply_time) as avg_reply_time
        from we_user_behavior_data wubd
        <where>
            and date_format(wubd.stat_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            and date_format(wubd.stat_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </where>
        group by user_id order by avg_reply_time desc limit 5
    </select>
    <select id="getGroupSessionAnalysis"
            resultType="org.scrm.domain.operation.vo.WeSessionGroupAnalysisVo">
        select
            (select sum(chat_total) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')) as yd_chat_total,
            (select sum(chat_total) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d') ) as byd_chat_total,
            (select sum(msg_total) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as yd_msg_total,
            (select sum(msg_total) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d') ) as byd_msg_total,
            (select sum(member_has_msg) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as yd_member_has_msg,
            (select sum(member_has_msg) from we_group_statistic where date_format(stat_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 2 day),'%Y-%m-%d')) as byd_member_has_msg;
    </select>
    <select id="getGroupSessionTotalCnt"
            resultType="org.scrm.domain.operation.vo.WeSessionGroupTotalCntVo">
        select
        date_format(wgs.stat_time,'%Y-%m-%d') as x_time,
        sum(wgs.chat_total) as chat_total,
        sum(wgs.msg_total) as msg_total,
        avg(wgs.member_has_msg) as member_has_msg
        from we_group_user_statistic wgs
        <where>
            <if test="chatIds != null and chatIds.size() > 0" >
                and wgs.chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and wgs.user_id in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            and date_format(wgs.stat_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            and date_format(wgs.stat_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </where>
        group by wgs.stat_time
        order by x_time asc
    </select>

    <select id="getSessionArchiveAnalysis"
            resultType="org.scrm.domain.operation.vo.WeSessionArchiveAnalysisVo">
        select
            (select count(1) from sys_user su where su.is_open_chat = 1 and su.del_flag = 0 ) as open_user_cnt,
            (SELECT COUNT(1) FROM sys_user su WHERE su.del_flag=0 AND (su.is_open_chat=0 OR su.is_open_chat IS NULL)) AS not_open_user_cnt,
            (select count(1) from we_customer wc where wc.is_open_chat = 1 and wc.del_flag = 0 ) as open_customer_cnt,
            (SELECT COUNT(1) FROM we_customer wc WHERE wc.del_flag=0 AND (wc.is_open_chat=0 OR wc.is_open_chat IS NULL)) AS not_open_customer_cnt
    </select>

    <select id="getSessionArchiveDetails"
            resultType="org.scrm.domain.operation.vo.WeSessionArchiveDetailVo">
        select
            wc.external_userid as custoemr_id,
            wc.customer_name,
            wc.avatar as customer_avatar,
            wc.add_user_id as user_id,
            (select su.user_name from sys_user su where su.we_user_id = wc.add_user_id and su.del_flag = 0 limit 1) as user_name,
            wc.open_chat_time
        from we_customer wc
        where is_open_chat = 1 and del_flag = 0
        <if test="beginTime != null">
            and date_format(wc.open_chat_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
        </if>
        <if test="endTime != null">
            and date_format(wc.open_chat_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
        </if>
    </select>

    <select id="findWeCustomerRemindAnalysis" resultType="org.scrm.domain.operation.vo.WeCustomerRemindAnalysisVo">
        select
        (select count(1) from we_customer where del_flag = 0 and date_format(add_time,'%y%m%d') &lt;= date_format(date_sub(curdate(),interval 1 day),'%y%m%d') )  as ydTotalCnt,
        (select count(1) from we_customer where date_format(add_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d') ) as ydCnt,
        (select count(1) from we_customer where del_flag = 0 and track_state=1 AND date_format(add_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d') ) ydFollowUpCustomer,
        (select count(distinct external_userid) from we_customer where date_format(add_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')  and del_flag = 0 ) as ydNetCnt,
        (select count(1) from we_customer where date_format(add_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')  and del_flag = 1 ) as ydLostCnt,
        (select sum(new_apply_cnt) from we_user_behavior_data where date_format(stat_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d') ) as ydNewApplyCnt
    </select>

    <select id="findWeGroupRemindAnalysis" resultType="org.scrm.domain.operation.vo.WeGroupRemindAnalysisVo">
        select
        (select count(1) from we_group where del_flag = 0 and date_format(add_time,'%Y-%m-%d') &lt;= date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')) as ydGroupTotalCnt,
        (select count(1) from we_group where date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as ydGroupAddCnt,
        (select count(1) from we_group where date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')  and del_flag = 1 ) as ydGroupDissolveCnt,
        (select count(1) from we_group_member where type=1 and date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d')) AS ydMemberTotalCnt,
        (select count(1) from we_group_member where date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') ) as ydMemberAddCnt,
        (select count(1) from we_group_member where date_format(create_time,'%Y-%m-%d') = date_format(date_sub(curdate(),interval 1 day),'%Y-%m-%d') and del_flag = 1 ) as ydMemberQuitCnt
    </select>



    <select id="selectGroupMemberBrokenLine"  resultType="org.scrm.domain.operation.vo.WeGroupMemberRealCntVo">
        SELECT
        date as xTime,
        (SELECT count(*) from we_group_member wgm
         LEFT JOIN we_group wg ON wgm.chat_id=wg.chat_id
          where DATE_FORMAT(wgm.join_time,'%Y-%m-%d')&lt;=date and wgm.quit_scene IS NULL and wgm.del_flag=0
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and wg.owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and wg.chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        ) as totalCnt,

        (SELECT count(*) from we_group_member wgm
            LEFT JOIN we_group wg ON wgm.chat_id=wg.chat_id
            where DATE_FORMAT(wgm.join_time,'%Y-%m-%d')=date and wgm.quit_scene IS NULL and wgm.del_flag=0
            <if test="ownerIds != null and ownerIds.size() > 0" >
                and wg.owner in
                <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="chatIds != null and chatIds.size() > 0" >
                and wg.chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        ) as addCnt,



        (        SELECT count(*) from we_group_member wgm
        LEFT JOIN we_group wg ON wgm.chat_id=wg.chat_id
        where DATE_FORMAT(wgm.quit_time,'%Y-%m-%d')=date
        <if test="ownerIds != null and ownerIds.size() > 0" >
            and wg.owner in
            <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                #{ownerId}
            </foreach>
        </if>
        <if test="chatIds != null and chatIds.size() > 0" >
            and wg.chat_id in
            <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>

        ) as quitCnt,

        (        SELECT count(*) from we_group_member wgm
        LEFT JOIN we_group wg ON wgm.chat_id=wg.chat_id
        where DATE_FORMAT(wgm.join_time,'%Y-%m-%d')=date and wgm.quit_scene IS NOT NULL or wgm.del_flag=1
        <if test="ownerIds != null and ownerIds.size() > 0" >
            and wg.owner in
            <foreach item="ownerId" collection="ownerIds" index="index" open="(" separator="," close=")">
                #{ownerId}
            </foreach>
        </if>
        <if test="chatIds != null and chatIds.size() > 0" >
            and wg.chat_id in
            <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                #{chatId}
            </foreach>
        </if>
        ) as dissolveCnt
        FROM
        sys_dim_date
        <where>
            <if test="beginTime !=null and beginTime != '' and endTime !='' and endTime != null">
                date_format(date,'%Y-%m-%d') BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
        ORDER BY date ASC
    </select>


</mapper>