(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6dfc0e48"],{"073e":function(e,t,a){},1013:function(e,t,a){"use strict";a("a8ce1")},"11a6":function(e,t,a){"use strict";a("073e")},"20fb":function(e,t,a){"use strict";var s=function(){var e=this,t=e._self._c;return e._list&&e._list.length||e.emptyText?t("div",{staticClass:"tag-ellipsis"},[e._list&&e._list.length?e._e():t("div",[e._v(e._s(e.emptyText))]),t("div",{staticClass:"tag-all"},[e._l(e._list.slice(0,e.showMoreTag?void 0:+e.limit),(function(a,s){return t("van-tag",e._b({key:s,attrs:{round:""}},"van-tag",e.elTagProps,!1),[e._v(" "+e._s(a[e.defaultProps]||a)+" ")])})),e._list.length>+e.limit?t("van-tag",e._b({key:"a",attrs:{round:"",type:"primary"},on:{click:function(t){e.showMoreTag=!e.showMoreTag}}},"van-tag",e.elTagProps,!1),[e._v(" "+e._s(e.showMoreTag?"收起":`...展开全部${e._list.length}个`)+" ")]):e._e()],2)]):e._e()},i=[],l=(a("e9f5"),a("910d"),{name:"TagEllipsis",components:{},props:{list:{type:Array,default:()=>[]},limit:{type:[String,Number],default:2},defaultProps:{type:String,default:"name"},emptyText:{type:String,default:""}},data(){return{showMoreTag:!1}},computed:{_list(){return this.list.filter(e=>"string"===typeof e?e:e[this.defaultProps])}},watch:{},created(){this.elTagProps=Object.assign({},this.$attrs),delete this.elTagProps.style},mounted(){},methods:{}}),r=l,o=(a("1013"),a("2877")),n=Object(o["a"])(r,s,i,!1,null,"55d52006",null);t["a"]=n.exports},"2ec2":function(e,t,a){},"45d1":function(e,t,a){},"474e":function(e,t,a){"use strict";a("45d1")},4866:function(e,t,a){},"5ae7":function(e,t,a){"use strict";a("2ec2")},"663a":function(e,t,a){"use strict";a("d950")},"7f84":function(e,t,a){"use strict";var s=function(){var e=this,t=e._self._c;return t("div",[t("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:function(t){return e.getList(1)}},model:{value:e.refreshing,callback:function(t){e.refreshing=t},expression:"refreshing"}},[t("van-list",{attrs:{finished:e.finished,"finished-text":e.finishedText,error:e.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(t){e.error=t},load:function(t){return e.getList()}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},e._l(e.list,(function(a,s){return t("van-cell",{key:s},[t("p",{staticClass:"f12",staticStyle:{position:"relative"}},[e._v(" "+e._s(e.dateFormat(a[0].createTime,"yyyy-MM-dd w"))+" ")]),t("van-steps",{attrs:{direction:"vertical","inactive-color":e.color,"active-color":e.color,active:a.length}},e._l(a,(function(a,s){return t("van-step",{key:s,staticClass:"msg"},[t("span",{staticClass:"f12 po"},[e._v(e._s(e.dateFormat(a.createTime,"hh:mm")))]),t("span",{staticClass:"fs14"},[e._v(e._s(a.title))]),t("p",{staticClass:"fs14 con"},[e._v(e._s(a.content))]),a.weMaterialVo?t("MaterialCard",{attrs:{weMaterialVo:a.weMaterialVo}}):e._e()],1)})),1)],1)})),1)],1)],1)},i=[],l=(a("14d9"),a("e9f5"),a("7d54"),a("ac0e")),r=a("ed08"),o=function(){var e=this,t=e._self._c;return t("div",{key:e.index,staticClass:"itemList"},[t("div",{staticClass:"content bfc-o"},[t("div",{staticClass:"title"},[e._v(e._s(e.weMaterialVo.materialName))]),12==e.weMaterialVo.mediaType?t("div",{staticClass:"centerStyle"},[e.weMaterialVo.coverUrl?t("van-image",{attrs:{width:"40",height:"40",src:e.weMaterialVo.coverUrl}}):t("svg-icon",{staticClass:"icon-style",attrs:{name:"article"}}),t("div",{staticClass:"contentStyle"},[e._v(e._s(e.weMaterialVo.digest))])],1):e._e(),2==e.weMaterialVo.mediaType?t("div",{staticClass:"centerStyle"},[t("van-image",{attrs:{width:"40",height:"40",src:e.weMaterialVo.coverUrl}}),t("div",{staticClass:"contentStyle"},[e._v(e._s(e.weMaterialVo.digest))])],1):e._e(),5==e.weMaterialVo.mediaType?t("div",{staticClass:"centerStyle"},[e.weMaterialVo.materialUrl?t("van-image",{attrs:{width:"40",height:"40",src:e.weMaterialVo.materialUrl}}):t("svg-icon",{staticClass:"icon-style",attrs:{name:"pic"}}),t("div",{staticClass:"contentStyle"},[e._v(e._s(e.weMaterialVo.digest))])],1):e._e(),"3"===e.weMaterialVo.mediaType?t("div",{staticStyle:{display:"flex"}},[e.weMaterialVo.materialUrl?t("svg-icon",{staticClass:"icon-style",attrs:{name:e.weMaterialVo.materialUrl?e.filType(e.weMaterialVo.materialUrl):""}}):e._e(),t("span",{staticClass:"contentStyle"},[e._v(e._s(e.weMaterialVo.digest))])],1):e._e()]),t("div",{staticClass:"info"},[t("div",{staticClass:"flex fr"},["0"!==e.weMaterialVo.mediaType&&"4"!==e.weMaterialVo.mediaType&&"11"!==e.weMaterialVo.mediaType&&"3"!==e.weMaterialVo.mediaType?t("div",{staticClass:"preview",on:{click:function(t){return e.preview(e.weMaterialVo)}}},[e._v(" 预览 ")]):e._e(),"3"==e.weMaterialVo.mediaType?t("a",{staticClass:"preview",attrs:{target:"_blank",href:e.weMaterialVo.materialUrl}},[e._v(" 预览 ")]):e._e()])])])},n=[],c={data(){return{}},methods:{preview(e){"0"!==e.id&&"4"!==e.id&&this.$router.push({name:"metrialDetail",query:{materiaId:e.id,isBack:!0}})},filType(e){let t=JSON.parse(JSON.stringify(e));t=t.split(".");let a=t[t.length-1];return"pdf"===a?"pdf":"doc"===a||"docx"===a?"word":"ppt"===a||"pptx"===a||"pps"===a||"pptsx"===a?"ppt":""}},props:{weMaterialVo:{type:Array,default:null}}},d=c,u=(a("474e"),a("2877")),h=Object(u["a"])(d,o,n,!1,null,"5545e649",null),m=h.exports,f={components:{MaterialCard:m},props:{load:Function},data(){return{query:{pageNum:1,pageSize:10},content:"",finishedText:"",error:!1,type:0,list:[],loading:!1,finished:!1,refreshing:!1,dateFormat:r["b"],color:l["color"]}},watch:{},methods:{getList(e){this.loading=!0,this.finished=!1,e&&(this.query.pageNum=e),1==this.query.pageNum&&(this.list=[]),this.refreshing&&(this.list=[],this.refreshing=!1),this.load(this.query).then(({rows:e,total:t})=>{e=e||[];let a=[];e.forEach(e=>{let t=Object(r["b"])(e.createTime,"yyyyMMdd");a.includes(t)||a.push(t)}),a.sort((e,t)=>t-e);for(let s=0;s<a.length;s++){let t=[];for(let i=0;i<e.length;i++)a[s]==Object(r["b"])(e[i].createTime,"yyyyMMdd")&&t.push(e[i]);this.list.push(t)}this.loading=!1,this.refreshing=!1,t&&0!=t?e.length<this.query.pageSize?(this.finishedText="没有更多了",this.finished=!0):this.query.pageNum++:(this.finishedText="暂无数据",this.finished=!0)}).catch(e=>{this.loading=!1,this.finished=!0,this.error=!0,alert(e)})}}},p=f,v=(a("b537"),Object(u["a"])(p,s,i,!1,null,"32ad7ab4",null));t["a"]=v.exports},8350:function(e,t,a){"use strict";a("97b0")},9585:function(e,t,a){"use strict";a.d(t,"d",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"a",(function(){return u})),a.d(t,"b",(function(){return h}));var s=a("b775");const{get:i,post:l,put:r,del:o}=s["b"],n="/svipGroup",c=e=>i(n+"/findSvipGroupTpls",{status:"1",...e}),d=e=>i(n+"/findSvipGroupTplByIdForApp",{externalUserid:e.externalUserId,...e}),u=e=>i(n+"/checkSvipGroup",{externalUserid:e.externalUserId,...e}),h=e=>l(n+"/completeSvipGroup",e)},"97b0":function(e,t,a){},a732:function(e,t,a){"use strict";var s=a("23e7"),i=a("c65b"),l=a("2266"),r=a("59ed"),o=a("825a"),n=a("46c4"),c=a("2a62"),d=a("f99f"),u=d("some",TypeError);s({target:"Iterator",proto:!0,real:!0,forced:u},{some:function(e){o(this);try{r(e)}catch(s){c(this,"throw",s)}if(u)return i(u,this,e);var t=n(this),a=0;return l(t,(function(t,s){if(e(t,a++))return s()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},a89b:function(e,t,a){"use strict";a.r(t);a("e9f5"),a("a732");var s=function(){var e,t,a,s,i,l,r,o=this,n=o._self._c;return n("div",{staticClass:"portrait"},[n("div",{staticClass:"details card"},[n("div",{staticClass:"detail mb20"},[n("div",{staticClass:"left"},[n("div",{staticClass:"img"},[n("img",{attrs:{src:o.form.avatar,alt:""}})]),n("div",{staticClass:"right"},[n("div",{staticClass:"key"},[o._v(" "+o._s(o.form.name||"")+" ")]),n("div",{staticClass:"flex aic"},[n("van-icon",{class:["gender",{1:"man",2:"woman"}[o.form.gender]],attrs:{name:"manager"}}),n("span",{style:{color:1===o.form.customerType?"#4bde03":"#f9a90b","font-size":"12px"}},[o._v(" "+o._s({1:"@微信",2:"@企业微信"}[o.form.customerType])+" ")])],1)])]),n("div",{attrs:{type:"success"}},[o._v(o._s(o.str))])]),n("div",[n("div",{staticClass:"content-style"},[n("span",[o._v("添加时间：")]),n("span",[o._v(o._s(o.form.addTime||"-"))])]),n("div",{staticClass:"content-style"},[n("span",[o._v("添加方式：")]),n("span",[o._v(o._s(o.form.addMethodStr||"-"))])])]),n("div",{staticClass:"realationship"},[n("div",{staticClass:"detail"},[n("div",{staticClass:"boxnumber"},[null!==(e=o.form.socialConn)&&void 0!==e&&e.addEmployeNum?n("div",{staticClass:"number",on:{click:function(e){return o.goRoute("/community",1)}}},[n("span",[o._v(o._s(null===(t=o.form.socialConn)||void 0===t?void 0:t.addEmployeNum))])]):o._e(),n("p",{staticClass:"key"},[o._v("跟进员工")])]),n("div",{staticClass:"boxnumber"},[null!==(a=o.form.socialConn)&&void 0!==a&&a.addGroupNum?n("div",{staticClass:"number",on:{click:function(e){return o.goRoute("/community",2)}}},[n("span",[o._v(o._s(null===(s=o.form.socialConn)||void 0===s?void 0:s.addGroupNum))])]):o._e(),n("p",{staticClass:"key"},[o._v("所在群聊")])]),n("div",{staticClass:"boxnumber"},[null!==(i=o.form.socialConn)&&void 0!==i&&i.commonGroupNum?n("div",{staticClass:"number",on:{click:function(e){return o.goRoute("/community",3)}}},[n("span",[o._v(o._s(null===(l=o.form.socialConn)||void 0===l?void 0:l.commonGroupNum))])]):o._e(),n("p",{staticClass:"key"},[o._v("共同群聊")])])])])]),n("div",{staticClass:"userlabel card"},[n("div",{staticClass:"detail"},[n("div",{staticClass:"title"},[o._v("企业备注")]),n("div",{staticClass:"data",attrs:{"is-link":""},on:{click:function(e){o.remarkFormshow=!0}}},[o._v(" 编辑 "),n("van-icon",{attrs:{name:"arrow"}})],1)]),n("remarkForm",{attrs:{data:o.form,detail:!0}}),n("van-action-sheet",{attrs:{title:"企业备注"},model:{value:o.remarkFormshow,callback:function(e){o.remarkFormshow=e},expression:"remarkFormshow"}},[o.remarkFormshow?n("remarkForm",{attrs:{data:o.form,detail:!1},on:{cancel:function(e){o.remarkFormshow=!1},success:function(e){o.remarkFormshow=!1,o.getCustomerInfo()}}}):o._e()],1)],1),n("div",{staticClass:"userlabel card"},[n("div",{staticClass:"detail"},[n("div",{staticClass:"title"},[o._v("企业标签")]),n("div",{staticClass:"data",attrs:{"is-link":""},on:{click:function(e){return o.labelEdit()}}},[o._v(" 编辑 "),n("van-icon",{attrs:{name:"arrow"}})],1)]),o.form.tags&&o.form.tags.length?n("div",{staticClass:"labelstyle mt15"},[n("TagEllipsis",{attrs:{list:o.form.tags,limit:10}})],1):n("Empty",{staticStyle:{padding:"0"}})],1),n("div",{staticClass:"userlabel card"},[n("div",{staticClass:"detail"},[n("div",{staticClass:"title"},[o._v("个人标签")]),n("div",{staticClass:"data",attrs:{"is-link":""},on:{click:function(e){return o.labelEdit("person")}}},[o._v(" 编辑 "),n("van-icon",{attrs:{name:"arrow"}})],1)]),o.form.personTags&&o.form.personTags.length?n("div",{staticClass:"labelstyle mt15"},o._l(o.form.personTags,(function(e,t){return n("div",{key:t,staticClass:"label"},[o._v(" "+o._s(e.name)+" ")])})),0):n("Empty",{staticStyle:{padding:"0"}})],1),n("div",{staticClass:"addwaiting card",staticStyle:{"min-heigth":"200px"}},[n("van-tabs",{attrs:{color:o.color},model:{value:o.active,callback:function(e){o.active=e},expression:"active"}},[n("van-tab",{attrs:{title:"资料"}},[n("div",{staticClass:"table"},[n("DefineFieldCom",{key:12,attrs:{detail:!0,addressOptions:o.addressOptions,list:o.fieldList,baseData:o.form}}),n("div",{staticClass:"more ac mt15",on:{click:function(e){o.detailshow=!0,o.getAreaList()}}},[o._v(" 更多详细资料 ")])],1)]),n("van-tab",{attrs:{title:"轨迹"}},[n("div",{staticClass:"addwaiting card",staticStyle:{"padding-left":"0px"}},[n("div",{staticClass:"detail"},[n("van-tabs",{attrs:{color:o.color},on:{change:function(e){return o.refreshTrajectory()}},model:{value:o.query.trajectoryType,callback:function(e){o.$set(o.query,"trajectoryType",e)},expression:"query.trajectoryType"}},[n("van-tab",{attrs:{name:0,title:"全部"}}),n("van-tab",{attrs:{name:1,title:"客户动态"}}),n("van-tab",{attrs:{name:2,title:"员工动态"}}),n("van-tab",{attrs:{name:3,title:"互动动态"}})],1),n("div",{staticClass:"data",attrs:{"is-link":""},on:{click:o.sync}},[o._v("同步")])],1),1===o.active?n("StepList",{ref:"stepList",attrs:{load:o.findTrajectory}}):o._e()],1)]),n("van-tab",{attrs:{title:"跟进"}},[2===o.active?n("StepList",{ref:"stepList",attrs:{load:o.findTrajectory}}):o._e()],1)],1)],1),o.isMyCustomer?n("div",{staticClass:"cancat-btn"},[null!==(r=o.$route.query)&&void 0!==r&&r.id?n("van-button",{staticStyle:{width:"90%"},attrs:{type:"info",round:""},on:{click:o.concat}},[o._v(" 联系客户 ")]):[n("van-button",{attrs:{type:"info",round:""},on:{click:function(e){o.detailshow=!0,o.getAreaList()}}},[o._v(" 编辑资料 ")]),n("van-button",{attrs:{type:"info",round:""},on:{click:function(e){o.usershow=!0}}},[o._v(" 添加跟进 ")]),n("van-button",{attrs:{type:"info",round:""},on:{click:o.goVipGroup}},[o._v(" 专属客群 ")])]],2):o._e(),n("van-action-sheet",{attrs:{title:"详细资料"},model:{value:o.detailshow,callback:function(e){o.detailshow=e},expression:"detailshow"}},[o.detailshow?n("DefineFieldCom",{key:21,attrs:{addressOptions:o.addressOptions,list:o.fieldList,baseData:o.form},on:{success:o.saveUserInformation}}):o._e()],1),n("van-action-sheet",{attrs:{title:"跟进记录"},model:{value:o.usershow,callback:function(e){o.usershow=e},expression:"usershow"}},[n("van-form",{on:{submit:o.onSubmit}},[n("van-field",{attrs:{value:o.trackState,label:"商机阶段",required:"",rules:[{required:!0,message:"必填项"}]},scopedSlots:o._u([{key:"input",fn:function(){return[n("van-dropdown-menu",{attrs:{"active-color":"#07c060"}},[n("van-dropdown-item",{attrs:{options:o.stageList},model:{value:o.trackState,callback:function(e){o.trackState=e},expression:"trackState"}})],1)]},proxy:!0}])}),n("van-field",{staticClass:"conagency",attrs:{name:"跟进内容",label:"跟进内容",placeholder:"请输入跟进内容",type:"textarea",required:"",rules:[{required:!0,message:"必填项"}]},model:{value:o.conagency,callback:function(e){o.conagency=e},expression:"conagency"}}),n("div",{staticClass:"save-btn"},[n("van-button",{attrs:{round:"",block:"",size:"normal",type:"info","native-type":"submit"}},[o._v("保存")])],1)],1)],1),n("van-action-sheet",{attrs:{title:"person"===o.editLabelType?"个人标签":"客户标签"},model:{value:o.show,callback:function(e){o.show=e},expression:"show"}},[n("div",{staticClass:"search-container"},[n("div",{staticClass:"custom-search-wrapper"},[n("van-icon",{staticClass:"search-icon",attrs:{name:"search"}}),n("input",{directives:[{name:"model",rawName:"v-model",value:o.searchKeyword,expression:"searchKeyword"}],staticClass:"search-input",attrs:{placeholder:"搜索标签名"},domProps:{value:o.searchKeyword},on:{input:[function(e){e.target.composing||(o.searchKeyword=e.target.value)},o.onSearchInput]}}),o.searchKeyword?n("van-icon",{staticClass:"clear-icon",attrs:{name:"clear"},on:{click:o.onSearchClear}}):o._e()],1)]),n("div",{staticClass:"content"},["person"===o.editLabelType?[n("div",{staticClass:"labelstyle"},o._l(o.filteredPersonTags,(function(e,t){return n("div",{key:t,staticClass:"label",class:o.addTag.some(t=>t.tagId==e.tagId)&&"active",on:{click:function(t){return o.clickLabel(e)}}},[o._v(" "+o._s(e.name)+" ")])})),0),n("div",{staticClass:"add-person-tag ac",on:{click:function(e){o.showAddTag=!0}}},[n("van-icon",{staticClass:"mr10",attrs:{name:"add-o"}}),o._v(" 添加个人标签 ")],1)]:o._l(o.filteredCompanyTags,(function(e,t){return n("div",{key:t},[n("div",{staticClass:"mb15 mt5 tag-group-name"},[o._v(o._s(e.groupName))]),n("div",{staticClass:"labelstyle"},o._l(e.weTags,(function(e,t){return n("div",{key:t,staticClass:"label",class:o.addTag.some(t=>t.tagId==e.tagId)&&"active",on:{click:function(t){return o.clickLabel(e)}}},[o._v(" "+o._s(e.name)+" ")])})),0)])})),("person"!==o.editLabelType||o.filteredPersonTags&&o.filteredPersonTags.length)&&("person"===o.editLabelType||o.filteredCompanyTags&&o.filteredCompanyTags.length)?o._e():n("Empty")],2),n("div",{staticClass:"save-btn",on:{click:function(e){return o.saveCustomerTag()}}},[n("van-button",{attrs:{type:"info",size:"normal",block:"",round:""}},[o._v(" 保存 ")])],1)]),n("van-dialog",{attrs:{title:"添加标签","show-cancel-button":"",width:"300px","confirm-button-color":o.color,"before-close":o.submitNewPersonTag},model:{value:o.showAddTag,callback:function(e){o.showAddTag=e},expression:"showAddTag"}},[n("van-form",{ref:"addTagForm",attrs:{"validate-trigger":"onChange"}},[n("van-field",{staticClass:"conagency",attrs:{maxlength:"15",placeholder:"请输入标签（不超过15字）",rules:[{required:!0,message:""}]},model:{value:o.newPersonTag,callback:function(e){o.newPersonTag=e},expression:"newPersonTag"}})],1)],1)],1)},i=[],l=(a("14d9"),a("910d"),a("f665"),a("7d54"),a("ab43"),a("c2eb")),r=a("9585"),o=a("7f84"),n=a("ac0e"),c=a("ed08"),d=a("2934"),u=function(){var e=this,t=e._self._c;return t("div",[t("van-form",{attrs:{"input-align":"right"},on:{submit:e.saveUserInformation}},[t("div",{staticClass:"content",staticStyle:{"max-height":"60vh",overflow:"auto"}},[e._l(e.fieldList,(function(a,s){return[!e.detail||s<3?t("div",{key:s},[1==a.isDefault?[t("div",["customerFullName"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:e.detail,maxlength:"30","show-word-limit":""},model:{value:e.form.customerFullName,callback:function(t){e.$set(e.form,"customerFullName",t)},expression:"form.customerFullName"}}):"remarkMobiles"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:e.detail,maxlength:"11"},model:{value:e.form.remarkMobiles,callback:function(t){e.$set(e.form,"remarkMobiles",t)},expression:"form.remarkMobiles"}}):"age"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:"",maxlength:"3"},model:{value:e.form.age,callback:function(t){e.$set(e.form,"age",t)},expression:"form.age"}}):"birthday"==a.labelVal?t("van-field",{attrs:{readonly:"",label:a.labelName,placeholder:e.detail?"":"请选择",disabled:e.detail},on:{click:function(t){e.birthdayShow=!0}},model:{value:e.form.birthday,callback:function(t){e.$set(e.form,"birthday",t)},expression:"form.birthday"}}):"email"==a.labelVal?t("van-field",{attrs:{label:a.labelName,disabled:e.detail,placeholder:e.detail?"":"请输入",maxlength:"30","show-word-limit":""},model:{value:e.form.email,callback:function(t){e.$set(e.form,"email",t)},expression:"form.email"}}):"area"==a.labelVal?t("van-field",{attrs:{readonly:"",label:a.labelName,placeholder:e.detail?"":"请选择",disabled:e.detail},on:{click:function(t){e.addressShow=!0}},model:{value:e.form.area,callback:function(t){e.$set(e.form,"area",t)},expression:"form.area"}}):"address"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:e.detail,maxlength:"60","show-word-limit":""},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}}):"qq"==a.labelVal?t("van-field",{attrs:{disabled:e.detail,label:a.labelName,placeholder:e.detail?"":"请输入",maxlength:"12"},model:{value:e.form.qq,callback:function(t){e.$set(e.form,"qq",t)},expression:"form.qq"}}):"position"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:e.detail,maxlength:"30","show-word-limit":""},model:{value:e.form.position,callback:function(t){e.$set(e.form,"position",t)},expression:"form.position"}}):"remarkCorpName"==a.labelVal?t("van-field",{attrs:{label:a.labelName,placeholder:e.detail?"":"请输入",disabled:e.detail,maxlength:"30","show-word-limit":""},model:{value:e.form.remarkCorpName,callback:function(t){e.$set(e.form,"remarkCorpName",t)},expression:"form.remarkCorpName"}}):"corpArea"==a.labelVal?t("van-field",{attrs:{readonly:"",label:a.labelName,placeholder:e.detail?"":"请选择",disabled:e.detail},on:{click:function(t){e.corpAreaShow=!0}},model:{value:e.form.corpArea,callback:function(t){e.$set(e.form,"corpArea",t)},expression:"form.corpArea"}}):"otherDescr"==a.labelVal?t("van-field",{staticClass:"conagency",attrs:{label:a.labelName,placeholder:e.detail?"":"请输入","input-align":"left",disabled:e.detail,type:"textarea",maxlength:"200","show-word-limit":""},model:{value:e.form.otherDescr,callback:function(t){e.$set(e.form,"otherDescr",t)},expression:"form.otherDescr"}}):e._e()],1)]:[1==a.type?[t("van-field",{attrs:{disabled:e.detail,label:a.labelName,placeholder:e.detail?"":"请输入"},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}})]:2==a.type?[t("van-field",{attrs:{disabled:e.detail,readonly:"",label:a.labelName,placeholder:e.detail?"":"请选择"},on:{click:function(t){return e.setSelectOpen(s)}},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}})]:[1==a.typeSub?[t("VantFieldSelectPicker",{attrs:{disabled:e.detail,label:a.labelName,placeholder:"请选择",columns:a.otherContent,option:{label:"labelName",value:"labelName"}},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}})]:[t("VantFieldCheckbox",{attrs:{disabled:e.detail,label:a.labelName,placeholder:"请选择",columns:a.otherContent,"label-width":"100",option:{label:"labelName",value:"labelName"}},model:{value:a.value,callback:function(t){e.$set(a,"value",t)},expression:"item.value"}})]]]],2):e._e()]}))],2),e.detail?e._e():t("div",{staticClass:"save-btn-info"},[t("van-button",{attrs:{round:"",block:"",size:"normal",type:"info","native-type":"submit"}},[e._v("保存")])],1)]),t("van-popup",{style:{height:"60%"},attrs:{position:"bottom",round:""},model:{value:e.dateSelectShow,callback:function(t){e.dateSelectShow=t},expression:"dateSelectShow"}},[t("van-datetime-picker",{attrs:{type:"date",title:"选择年月日","min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.confirmSelect,cancel:function(t){e.dateSelectShow=!1}},model:{value:e.dateSelect,callback:function(t){e.dateSelect=t},expression:"dateSelect"}})],1),t("van-popup",{style:{height:"60%"},attrs:{position:"bottom",round:""},model:{value:e.birthdayShow,callback:function(t){e.birthdayShow=t},expression:"birthdayShow"}},[t("van-datetime-picker",{attrs:{type:"date",title:"选择年月日","min-date":e.minDate,"max-date":e.maxDate},on:{confirm:e.confirm,cancel:function(t){e.birthdayShow=!1}},model:{value:e.birthday,callback:function(t){e.birthday=t},expression:"birthday"}})],1),t("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.addressShow,callback:function(t){e.addressShow=t},expression:"addressShow"}},[t("van-cascader",{attrs:{title:"请选择所在地区",options:e.addressOptions,"field-names":{text:"name",value:"id"}},on:{close:function(t){e.addressShow=!1},finish:e.selectedArea},model:{value:e.form.areaId,callback:function(t){e.$set(e.form,"areaId",t)},expression:"form.areaId"}})],1),t("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.corpAreaShow,callback:function(t){e.corpAreaShow=t},expression:"corpAreaShow"}},[t("van-cascader",{attrs:{title:"请选择所在地区",options:e.provinceCityTrees,"field-names":{text:"name",value:"id"}},on:{close:function(t){e.corpAreaShow=!1},finish:({selectedOptions:t})=>{e.corpAreaShow=!1,e.form.corpArea=t.map(e=>e.name).join("/")}},model:{value:e.form._corpArea,callback:function(t){e.$set(e.form,"_corpArea",t)},expression:"form._corpArea"}})],1)],1)},h=[],m=function(){var e=this,t=e._self._c;return t("div",{staticClass:"dh-field"},[t("div",{staticClass:"van-hairline--bottom"},[t("van-field",e._b({staticClass:"dh-cell",attrs:{label:e.label,readonly:"","input-align":"right","error-message-align":"right",placeholder:e.disabled?"":"请输入",disabled:e.disabled},on:{click:function(t){return e.showPopu(e.$attrs.disabled)}},model:{value:e.resultLabel,callback:function(t){e.resultLabel=t},expression:"resultLabel"}},"van-field",e.$attrs,!1)),t("van-popup",{attrs:{position:"bottom"},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[e.isSearch?t("van-field",{attrs:{"input-align":"left",placeholder:"搜索"},on:{input:e.search},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}}):e._e(),t("van-picker",e._b({attrs:{columns:e.columnsData,"show-toolbar":"","value-key":this.option.label},on:{cancel:e.cancel,confirm:e.onConfirm,change:e.change}},"van-picker",e.$attrs,!1))],1)],1)])},f=[],p={name:"VanFieldSelectPicker",model:{prop:"selectValue"},props:{disabled:{type:Boolean,default:!1},label:{type:String,default:""},columns:{type:Array,default:function(){return[]}},selectValue:{type:[String,Number],default:""},option:{type:Object,default:function(){return{label:"label",value:"value"}}},isSearch:{type:Boolean,default:!1},offOption:{type:Boolean,default:!1}},computed:{resultLabel:{get(){const e=this.columns.filter(e=>{const t=this.offOption?e:e[this.option.value];return t===this.resultValue});let t="";return e.length&&(t=this.offOption?e[0]:e[0][this.option.label]),t},set(){}}},data(){return{show:!1,searchVal:"",resultValue:this.selectValue,columnsData:[]}},methods:{search(e){this.columnsData=e?this.columnsData.filter(t=>{const a=this.offOption?t:t[this.option.label];return a.indexOf(e)>-1}):JSON.parse(JSON.stringify(this.columns))},onConfirm(e,t){const a=this.offOption?e:e[this.option.value];this.resultValue=a,this.show=!this.show,this.$emit("confirm",e,t,this.resultValue)},change(e,t){this.$emit("change",e,t,this.resultValue)},cancel(e,t){this.show=!this.show,this.$emit("cancel",e,t,this.resultValue)},showPopu(e){if(!this.disabled){if(this.columnsData=JSON.parse(JSON.stringify(this.columns)),this.resultValue=this.selectValue,void 0!==e&&!1!==e)return!1;this.show=!this.show}}},watch:{selectValue:function(e){this.resultValue=e},resultValue(e){this.searchVal="",this.columnsData=JSON.parse(JSON.stringify(this.columns)),this.$emit("input",e)}}},v=p,g=(a("8350"),a("2877")),b=Object(g["a"])(v,m,f,!1,null,"1d370804",null),y=b.exports,w=function(){var e=this,t=e._self._c;return t("div",{staticClass:"dh-field"},[t("div",{staticClass:"van-hairline--bottom"},[t("van-field",e._b({attrs:{label:e.label,readonly:"","error-message-align":"right","input-align":"right",disabled:e.disabled,placeholder:e.disabled?"":"请输入"},on:{click:function(t){return e.showPopu(e.$attrs.disabled)}},model:{value:e.resultLabel,callback:function(t){e.resultLabel=t},expression:"resultLabel"}},"van-field",e.$attrs,!1)),t("van-popup",{attrs:{position:"bottom"},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[t("div",{staticClass:"van-picker__toolbar"},[t("button",{staticClass:"van-picker__cancel",attrs:{type:"button"},on:{click:e.cancel}},[e._v("取消")]),t("div",{staticClass:"van-ellipsis van-picker__title"},[e._v(e._s(e.$attrs.label))]),t("button",{staticClass:"van-picker__confirm",attrs:{type:"button"},on:{click:e.onConfirm}},[e._v("确认")])]),t("div",{staticStyle:{"max-height":"264px","overflow-y":"auto"}},[e.isSearch?t("van-field",{attrs:{"input-align":"left",placeholder:"搜索"},on:{input:e.search},model:{value:e.searchVal,callback:function(t){e.searchVal=t},expression:"searchVal"}}):e._e(),t("van-cell",{attrs:{title:"全选"},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{attrs:{name:"all"},on:{click:e.toggleAll},model:{value:e.checkedAll,callback:function(t){e.checkedAll=t},expression:"checkedAll"}})]},proxy:!0}])}),t("van-checkbox-group",{ref:"checkboxGroup",on:{change:e.change},model:{value:e.checkboxValue,callback:function(t){e.checkboxValue=t},expression:"checkboxValue"}},[t("van-cell-group",e._l(e.columnsData,(function(a,s){return t("van-cell",{key:a[e.option.value],attrs:{title:a[e.option.label],clickable:""},on:{click:function(t){return e.toggle(s)}},scopedSlots:e._u([{key:"right-icon",fn:function(){return[t("van-checkbox",{ref:"checkboxes",refInFor:!0,attrs:{name:a[e.option.value]}})]},proxy:!0}],null,!0)})})),1)],1)],1)])],1)])},k=[],C={name:"VanFieldCheckbox",model:{prop:"selectValue"},props:{disabled:{type:Boolean,default:!1},label:{type:String,default:""},columns:{type:Array,default:function(){return[]}},selectValue:{type:Array,default:function(){return[]}},option:{type:Object,default:function(){return{label:"label",value:"value"}}},isSearch:{type:Boolean,default:!1}},computed:{resultLabel:{get(){const e=this.columns.filter(e=>this.resultValue.indexOf(e[this.option.value])>-1),t=e.map(e=>e[this.option.label]);return t.join(",")},set(){}}},data(){return{show:!1,searchVal:"",columnsData:JSON.parse(JSON.stringify(this.columns)),checkboxValue:JSON.parse(JSON.stringify(this.selectValue)),checkedAll:!1,resultValue:JSON.parse(JSON.stringify(this.selectValue))}},methods:{search(e){this.columnsData=e?this.columnsData.filter(t=>t[this.option.label].indexOf(e)>-1):JSON.parse(JSON.stringify(this.columns))},getData(e){const t=this.columnsData.filter(t=>e.indexOf(t[this.option.value])>-1);return t},onConfirm(){this.resultValue=this.checkboxValue,this.show=!this.show,this.$emit("confirm",this.resultValue,this.getData(this.resultValue))},change(e){this.$emit("change",e,this.getData(this.resultValue))},cancel(){this.show=!this.show,this.$emit("cancel",this.resultValue)},toggle(e){this.$refs.checkboxes[e].toggle()},toggleAll(e){this.$refs.checkboxGroup.toggleAll(this.checkedAll)},showPopu(e){if(!this.disabled){if(this.columnsData=JSON.parse(JSON.stringify(this.columns)),this.checkboxValue=JSON.parse(JSON.stringify(this.selectValue)),this.resultValue=JSON.parse(JSON.stringify(this.selectValue)),void 0!==e&&!1!==e)return!1;this.show=!this.show}}},watch:{selectValue:function(e){this.resultValue=e},resultValue(e){this.searchVal="",this.columnsData=JSON.parse(JSON.stringify(this.columns)),this.$emit("input",e)},columnsData:{handler(e){e.length&&e.length===this.checkboxValue.length?this.checkedAll=!0:this.checkedAll=!1},immediate:!0},checkboxValue:{handler(e){e.length&&e.length===this.columnsData.length?this.checkedAll=!0:this.checkedAll=!1},immediate:!0}}},x=C,_=(a("f9fc"),Object(g["a"])(x,w,k,!1,null,"cd5cf492",null)),S=_.exports,T={name:"define-field",components:{VantFieldSelectPicker:y,VantFieldCheckbox:S},data(){return{birthdayShow:!1,addressShow:!1,corpAreaShow:!1,minDate:new Date(1949,9,1),maxDate:new Date,birthday:new Date(2022,0,17),form:{},fieldList:[],dateSelectShow:!1,dateSelect:new Date,dateIndex:null}},props:{detail:{type:Boolean,default:!1},list:{type:Array,default:()=>[]},baseData:{type:Object,default:{}},addressOptions:{type:Array,default:()=>[]}},computed:{provinceCityTrees(){let e=JSON.parse(JSON.stringify(this.addressOptions));return e.forEach(e=>{e.children.forEach(e=>{delete e.children})}),e}},watch:{list:{immediate:!0,handler(e){e&&this.handlerData(e)}},baseData:{immediate:!0,handler(e){e&&(this.form=JSON.parse(JSON.stringify(e)),this.list.length&&this.handlerData(this.list))}}},methods:{saveUserInformation(){let e=[];this.fieldList.forEach((t,a)=>{if(0==t.isDefault){let a={customerId:this.form.customerId,fieldTemplateId:t.id,id:t.defineId,fieldCustomerInfoVal:[]};if(3!==t.type){let e={labelVal:t.labelVal,infoVal:t.value,id:t.defineId};a.fieldCustomerInfoVal[0]=e}else if(1==t.typeSub)a.fieldCustomerInfoVal[0]=this.getValue(t.otherContent,t.value,t.defineId);else if(t.value&&t.value.length){let e=[];t.value.forEach(a=>{e.push(this.getValue(t.otherContent,a,t.defineId))}),a.fieldCustomerInfoVal=e}e.push(a)}}),this.form.weCustomerInfoExpands=e,this.$emit("success",this.form)},getValue(e,t,a){let s={};return e.forEach(e=>{t==e.labelName&&(s={labelVal:e.labelVal,infoVal:t,id:a})}),s},confirm(e){this.birthdayShow=!1,this.form.birthday=Object(c["b"])(e,"yyyy-MM-dd")},selectedArea({selectedOptions:e}){this.addressShow=!1,this.form.provinceId=e[0].id,this.form.cityId=e[1].id,this.form.areaId=e[2].id,this.form.area=e.map(e=>e.name).join("/")},handlerData(e){let t=JSON.parse(JSON.stringify(e));t.forEach((e,a)=>{if(0==e.isDefault){let s=this.getEditValue(e),i={...e,...s};t[a]=i}}),this.fieldList=t},getEditValue(e){Object.keys(this.form).length||(this.form=JSON.parse(JSON.stringify(this.baseData)));let t={value:3!==e.type||1==e.typeSub?"":[],defineId:""};return this.form.weCustomerInfoExpands&&this.form.weCustomerInfoExpands.forEach((a,s)=>{a.fieldTemplateId===e.id&&a.fieldCustomerInfoVal&&a.fieldCustomerInfoVal.length&&(t.defineId=a.id,3!==e.type?t.value=a.fieldCustomerInfoVal[0].infoVal:(t.value=a.fieldCustomerInfoVal.map(e=>e.infoVal),1==e.typeSub&&(t.value=t.value[0])))}),t},setSelectOpen(e){this.dateIndex=e,this.dateSelectShow=!0,this.fieldList[e].value&&(this.dateSelect=new Date(this.fieldList[e].value))},confirmSelect(e){this.dateSelectShow=!1,this.fieldList[this.dateIndex].value=Object(c["b"])(e,"yyyy-MM-dd")}},created(){}},I=T,V=(a("5ae7"),Object(g["a"])(I,u,h,!1,null,"6ae8eb70",null)),O=V.exports,N=function(){var e=this,t=e._self._c;return t("div",[t("van-form",{attrs:{"input-align":"right"},on:{submit:e.save}},[t("div",{staticClass:"content",staticStyle:{"max-height":"60vh",overflow:"auto"}},[t("van-field",{attrs:{label:"备注名",placeholder:e.detail?"-":"请输入",disabled:e.detail,maxlength:"10","show-word-limit":!e.detail},model:{value:e.form.remarkCustomerName,callback:function(t){e.$set(e.form,"remarkCustomerName",t)},expression:"form.remarkCustomerName"}}),t("van-field",{attrs:{label:"手机号",placeholder:e.detail?"-":"请输入",disabled:e.detail,maxlength:"11","show-word-limit":!e.detail},model:{value:e.form.remarkPhone,callback:function(t){e.$set(e.form,"remarkPhone",t)},expression:"form.remarkPhone"}}),t("van-field",{attrs:{label:"所属企业",placeholder:e.detail?"-":"请输入",disabled:e.detail,maxlength:"30","show-word-limit":!e.detail},model:{value:e.form.remarkCorpName,callback:function(t){e.$set(e.form,"remarkCorpName",t)},expression:"form.remarkCorpName"}}),t("van-field",{staticStyle:{display:"block","padding-bottom":"0"},attrs:{label:"客户描述",placeholder:e.detail?"-":"请输入",disabled:e.detail,maxlength:"200","show-word-limit":!e.detail,type:"textarea","input-align":"left"},model:{value:e.form.remarkCustomerDescr,callback:function(t){e.$set(e.form,"remarkCustomerDescr",t)},expression:"form.remarkCustomerDescr"}})],1),e.detail?e._e():t("div",{staticClass:"save-btn-info"},[t("van-button",{attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:function(t){return e.$emit("cancel")}}},[e._v("取消")]),t("van-button",{attrs:{round:"",block:"",size:"normal",type:"info","native-type":"submit"}},[e._v("保存")])],1)])],1)},$=[],j={props:{data:{type:Object,default:()=>({})},detail:{type:Boolean,default:!1}},components:{},data(){return{form:{}}},computed:{},watch:{data:{handler(e){this.form=e},immediate:!0}},created(){},mounted(){},methods:{save(){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0}),Object(l["j"])(this.form).then(e=>{this.$toast.success("保存成功"),this.$emit("success",this.form)}).catch(e=>{console.log(e)})}}},D=j,A=(a("663a"),Object(g["a"])(D,N,$,!1,null,"75ee3ffb",null)),M=A.exports,L=a("20fb"),U={components:{StepList:o["a"],DefineFieldCom:O,TagEllipsis:L["a"],remarkForm:M},data(){return{fieldList:[],color:n["color"],show:!1,usershow:!1,trackState:"",conagency:"",actions:[{name:"选项一"},{name:"选项二"},{name:"选项三"}],active:0,todonewsshow:!1,externalUserId:"",form:{name:"",remarkMobiles:"",age:"",birthday:"",email:"",address:"",qq:"",position:"",remarkCorpName:"",otherDescr:"",tags:[],personTags:[],socialConn:{addEmployeNum:"",addGroupNum:"",commonGroupNum:""}},alllabel:[],groupId:"",name:"",tagId:"",addTag:[],staff:[],groupChat:[],commonGroup:[],query:{trajectoryType:null},loading:!1,dictTrackState:Object.freeze({1:{name:"待跟进",color:n["color"]},2:{name:"跟进中",color:n["color"]},3:{name:"已成交",color:"#07c160"},4:{name:"无意向",color:"#ff976a"},5:{name:"已流失",color:"#ee0a24"}}),editLabelType:"",showAddTag:!1,newPersonTag:"",isMyCustomer:!1,detailshow:!1,minDate:new Date(1949,9,1),maxDate:new Date,birthday:new Date(2022,0,17),addressOptions:[],stageList:[],str:"",tagUp:!0,vipGroup:void 0,remarkData:!0,remarkFormshow:!1,searchKeyword:""}},computed:{userId(){var e;return(null===(e=this.$route.query)||void 0===e?void 0:e.userId)||(this.$store.state.userId=sessionStorage.userId)},filteredPersonTags(){return this.searchKeyword&&"person"===this.editLabelType?this.alllabel.filter(e=>e.name&&e.name.toLowerCase().includes(this.searchKeyword.toLowerCase())):this.alllabel},filteredCompanyTags(){return this.searchKeyword&&"person"!==this.editLabelType?this.alllabel.map(e=>({...e,weTags:e.weTags.filter(e=>e.name&&e.name.toLowerCase().includes(this.searchKeyword.toLowerCase()))})).filter(e=>e.weTags.length>0):this.alllabel}},watch:{searchKeyword(e,t){}},created(){let e=this.$route.query;this.externalUserId=(null===e||void 0===e?void 0:e.externalUserId)||(null===e||void 0===e?void 0:e.id),this.isMyCustomer=this.userId==sessionStorage.userId,this.$toast.loading(),this.externalUserId?(this.start(),this.$toast.clear()):this.init()},mounted(){},methods:{getStage(){Object(d["e"])().then(e=>{var t,a,s;this.stageList=null===(t=e.data)||void 0===t?void 0:t.map(e=>({text:e.stageKey,value:e.stageVal})),null===(a=this.stageList)||void 0===a||a.some(e=>e.value==this.form.trackState&&(this.str=e.text)),this.trackState||(this.trackState=null===(s=this.stageList)||void 0===s||null===(s=s[0])||void 0===s?void 0:s.value)})},start(){this.getCustomerInfo(),this.findAddaddEmployes(),this.findAddGroupNum(),this.getField(),this.getStage(),this.getVipGroup()},getField(){Object(l["h"])({weUserId:this.userId,externalUserId:this.externalUserId}).then(e=>{this.fieldList=e.data})},init(){wx.invoke("getContext",{},e=>{if("getContext:ok"==e.err_msg){let t=e.entry;if(!["single_chat_tools","group_chat_tools","contact_profile"].includes(t))return void this.$toast("入口错误："+t);if("group_chat_tools"===t)return void this.$router.replace("/portraitGroup");wx.invoke("getCurExternalContact",{},e=>{"getCurExternalContact:ok"==e.err_msg?(this.externalUserId=e.userId,this.start()):this.$dialog({message:"外部联系人失败："+JSON.stringify(e)}),this.$toast.clear()})}else this.$toast.clear(),this.$dialog({message:"进入失败："+JSON.stringify(e)})})},getCustomerInfo(){let e={externalUserid:this.externalUserId,userId:this.userId};Object(l["g"])(e).then(({data:t})=>{t.tagIds&&t.tagNames&&(t.tagIds=t.tagIds.split(","),t.tagNames=t.tagNames.split(","),t.tags=t.tagIds.map((e,a)=>({tagId:e,name:t.tagNames[a]}))),t.personTagIds&&t.personTagNames&&(t.personTagIds=t.personTagIds.split(","),t.personTagNames=t.personTagNames.split(","),t.personTags=t.personTagIds.map((e,a)=>({tagId:e,name:t.personTagNames[a]}))),this.form=Object.assign(t,e),this.conagency=this.form.trackContent,this.form.trackState&&(this.trackState=this.form.trackState),this.getStage()}).catch(e=>{console.log(e)})},findAddaddEmployes(){Object(l["d"])(this.externalUserId).then(({data:e})=>{this.staff=e}).catch(e=>{console.log(e)})},findAddGroupNum(){Object(l["c"])({externalUserid:this.externalUserId,userId:this.userId}).then(({data:e})=>{this.groupChat=e,this.commonGroup=this.groupChat.filter(e=>1==e.groupMemberNum)}).catch(e=>{console.log(e)})},findTrajectory(e){if(!this.externalUserId)return new Promise(()=>{});let t={weUserId:this.userId,externalUserid:this.externalUserId};return Object.assign(t,this.query,e),0==t.trajectoryType&&(t.trajectoryType=null),2===this.active&&(t.trajectoryType=4),Object(l["e"])(t)},refreshTrajectory(){this.$nextTick(()=>{this.$refs.stepList.getList(1)})},getAreaList(){Object(d["b"])().then(e=>{this.addressOptions=e})},getAllTags(e){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0});let t={groupTagType:1};return"person"===e&&(t={groupTagType:3,userId:this.userId}),Object(l["f"])(t).then(({data:e})=>{this.alllabel=this.listTagOneArray=e,"person"!==this.editLabelType&&(this.listTagOneArray=[],e.forEach(e=>{e.weTags.forEach(e=>{this.listTagOneArray.push(e)})})),this.$toast.clear()}).catch(e=>{console.log(e)})},saveUserInformation(e){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0}),Object(l["k"])(e).then(e=>{this.$toast("操作成功"),this.getCustomerInfo(),this.detailshow=!1}).catch(e=>{console.log(e)})},onSubmit(){let e={weUserId:this.userId,externalUserid:this.externalUserId,trackContent:this.conagency,trackState:this.trackState};Object(l["a"])(e).then(e=>{this.getCustomerInfo(),this.refreshTrajectory(),200==e.code&&(this.$toast.success("保存成功"),this.usershow=!1)}).catch(e=>{console.log(e)})},goRoute(e,t){this.$router.push({path:e,query:{customerId:this.externalUserId,type:t}})},async labelEdit(e){this.editLabelType=e,this.searchKeyword="",await this.getAllTags(e),this.addTag=[];let t=this.form["person"===e?"personTags":"tags"],a=[];t&&t.forEach(e=>{let t=this.listTagOneArray.find(t=>t.tagId===e.tagId);t?this.addTag.push(t):a.push(e.name)}),this.show=!0},clickLabel(e){let t=this.addTag.findIndex(t=>e.tagId==t.tagId);-1==t?this.addTag.push({groupId:e.groupId,name:e.name,tagId:e.tagId}):this.addTag.splice(t,1)},saveCustomerTag(){let e=[],t=this.form["person"===this.editLabelType?"personTags":"tags"];t&&t.length&&t.forEach(t=>{this.addTag.some(e=>e.tagId==t.tagId)||e.push(t)}),Object(l["l"])({externalUserid:this.externalUserId,userId:this.userId,isCompanyTag:"person"!==this.editLabelType,addTag:this.addTag.map(e=>({tagId:e.tagId,name:e.name})),removeTag:e}).then(e=>{200==e.code&&(this.show=!1,this.getCustomerInfo(),this.$toast.success("保存成功"))}).catch(e=>{console.log(e)})},submitNewPersonTag(e,t){"confirm"===e?this.$refs.addTagForm.validate().then(()=>{let e={weTags:[{name:this.newPersonTag,owner:this.userId}]};Object(l["b"])(e).then(e=>{this.$toast.success("添加成功"),this.getAllTags("person")}).catch(e=>{console.log(e)}),t()}).catch(e=>{t(!1),console.log(e)}):t()},sync(){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0}),Object(l["i"])(this.userId).then(e=>{this.$toast.success("同步成功"),this.refreshTrajectory()}).finally(()=>{this.$toast.clear()})},concat(e){wx.openEnterpriseChat({userIds:sessionStorage.userId,externalUserIds:this.externalUserId,groupName:"",chatId:"",success:e=>{e.chatId},fail:e=>{e.errMsg.indexOf("function not exist")>-1&&alert("版本过低请升级")}})},getVipGroup(){Object(r["a"])({weUserId:this.userId,externalUserId:this.externalUserId}).then(e=>{this.vipGroup=e.data})},goVipGroup(){var e;null!==(e=this.vipGroup)&&void 0!==e&&e.chatId?this.vipGroup.exitUserChat?wx.invoke("openExistedChatWithMsg",{chatId:this.vipGroup.chatId},(function(e){"openExistedChatWithMsg:ok"!==e.err_msg&&this.$toast(e.err_msg)})):this.$toast("当前客户已创建专属服务群，但您无管理权限，请联系管理员核实。"):this.$router.push({path:"/templateGroup",query:{userId:this.userId,externalUserId:this.externalUserId}})},onSearchInput(){console.log("搜索输入:",this.searchKeyword)},onSearchClear(){console.log("清除按钮被点击"),this.searchKeyword="",console.log("搜索框已清除，当前关键词:",this.searchKeyword),this.$forceUpdate()}}},E=U,J=(a("11a6"),Object(g["a"])(E,s,i,!1,null,"7c4f578e",null));t["default"]=J.exports},a8ce1:function(e,t,a){},ac0e:function(e,t,a){e.exports={color:"#07c060"}},b537:function(e,t,a){"use strict";a("4866")},c2eb:function(e,t,a){"use strict";a.d(t,"g",(function(){return r})),a.d(t,"k",(function(){return o})),a.d(t,"f",(function(){return n})),a.d(t,"l",(function(){return c})),a.d(t,"d",(function(){return d})),a.d(t,"c",(function(){return u})),a.d(t,"e",(function(){return h})),a.d(t,"a",(function(){return m})),a.d(t,"b",(function(){return f})),a.d(t,"i",(function(){return p})),a.d(t,"h",(function(){return v})),a.d(t,"j",(function(){return g}));var s=a("b775");const i=window.sysConfig.services.wecom,l=i+"/portrait";function r(e){return Object(s["a"])({url:l+"/findWeCustomerInfo",params:e})}function o(e){return Object(s["a"])({url:l+"/updateWeCustomerInfo",method:"post",data:e})}function n(e){return Object(s["a"])({url:l+"/findAllTags",params:e})}function c(e){return Object(s["a"])({url:l+"/updateWeCustomerPorTraitTag",method:"post",data:e})}function d(e){return Object(s["a"])({url:l+"/findAddaddEmployes/"+e})}function u(e){return Object(s["a"])({url:l+"/findAddGroupNum",params:e})}function h(e){return Object(s["a"])({url:i+"/trajectory/findTrajectory",params:e})}function m(e){return Object(s["a"])({url:l+"/addOrEditWaitHandle",method:"post",data:e})}function f(e){return Object(s["a"])({url:l+"/addOrUpdatePersonTags",method:"post",data:e})}function p(e){return Object(s["a"])({url:l+"/synchMomentsInteracte/"+e,method:"get"})}function v(e){return Object(s["a"])({url:l+"/findSysFieldTemplate",params:e})}function g(e){return Object(s["a"])({url:l+"/updateRemarkCorpInfo",method:"post",data:e})}},d950:function(e,t,a){},e6bf:function(e,t,a){},f9fc:function(e,t,a){"use strict";a("e6bf")}}]);