<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupSopPicMapper">
    <insert id="batchSopPic">
        insert into we_group_sop_pic(rule_id, pic_url) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.ruleId},#{item.picUrl})
        </foreach>
    </insert>
</mapper>