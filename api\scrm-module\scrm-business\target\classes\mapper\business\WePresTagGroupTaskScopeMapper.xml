<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WePresTagGroupTaskScopeMapper">

    <resultMap type="org.scrm.domain.community.vo.WeCommunityTaskEmplVo" id="WeEmplVoResult">
        <result property="userId" column="user_id"/>
        <result property="name" column="user_name"/>
        <result property="avatar" column="head_image_url"/>
        <result property="isDone" column="sent"/>
    </resultMap>

    <select id="getScopeListByTaskId" parameterType="Long" resultMap="WeEmplVoResult">
        SELECT DISTINCT wu.we_user_id as user_id,
                        wu.user_name,
                        wu.avatar as head_image_url,
                        st.sent
        FROM sys_user wu
                 LEFT JOIN we_pres_tag_group_stat st ON st.user_id = wu.user_id
            AND st.del_flag = 0
        WHERE st.task_id = #{taskId}
    </select>

    <insert id="batchBindsTaskScopes">
        insert into we_pres_tag_group_scope(task_id, we_user_id, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.taskId},#{item.weUserId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>
</mapper>