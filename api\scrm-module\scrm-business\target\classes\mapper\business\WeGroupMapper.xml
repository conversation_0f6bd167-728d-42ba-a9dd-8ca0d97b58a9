<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupMapper">



    <resultMap id="GroupDetailResult" type="org.scrm.domain.groupchat.vo.LinkGroupChatVo">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="chatId" column="chat_id" jdbcType="VARCHAR"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
        <result property="notice" column="notice" jdbcType="VARCHAR"/>
        <result property="owner" column="owner" jdbcType="VARCHAR"/>
        <result property="ownerName" column="owner_name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="allocateState" column="allocate_state" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    <!--    <collection property="memberList" ofType="vo.org.scrm.domain.groupchat.WeGroupMemberVo">
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="unionId" column="union_id" jdbcType="VARCHAR"/>
            <result property="joinTime" column="join_time" jdbcType="TIMESTAMP"/>
            <result property="joinScene" column="join_scene" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="groupNickName" column="group_nick_name" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="invitorUserId" column="invitor_user_id" jdbcType="VARCHAR"/>
            <result property="invitorUserName" column="invitor_user_name" jdbcType="VARCHAR"/>
        </collection>-->
    </resultMap>

    <sql id="getDetail">
        select
            wg.id,
            wg.chat_id,
            wg.group_name,
            wg.add_time,
            wg.notice,
            (select su.nick_name from sys_user su where su.we_user_id = wg.owner and su.del_flag = 0 limit 1) as owner_name,
            wg.owner,
            wg.status,
            wg.allocate_state,
            wg.create_time,
            wg.create_by,
            wg.update_time,
            wg.update_by,
            wg.del_flag,
            wgm.user_id,
            wgm.union_id,
            wgm.join_time,
            wgm.join_scene,
            wgm.type,
            wgm.group_nick_name,
            wgm.name,
            wgm.invitor_user_id,
            (select su.nick_name from sys_user su where su.we_user_id = wg.invitor_user_id and su.del_flag = 0 limit 1) as invitor_user_name
        from we_group wg
        left join we_group_member wgm on wg.id = wgm.group_id and wgm.del_flag = 0
    </sql>




    <select id="findWeGroupListIds"  resultType="java.lang.String">
        SELECT
          wg.chat_id
        FROM
        we_group wg
        left join we_group_member wgm  on wgm.chat_id=wg.chat_id and wgm.del_flag=0
        left join we_group_tag_rel wgtr  on wg.chat_id=wgtr.chat_id and wgtr.del_flag = 0
        left join we_tag  wt  on wt.id = wgtr.tag_id
        left join sys_user su on su.we_user_id = wg.owner and su.del_flag = 0
        where wg.del_flag=0
        <if test="query.id !=null">
            and wg.id = #{query.id}
        </if>
        <if test="query.chatId !=null and query.chatId !=''">
            and wg.chat_id in
            <foreach item="item" index="index" collection="query.chatId.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.groupName != null  and query.groupName != ''">
            AND wg.group_name  LIKE CONCAT('%',#{query.groupName},'%')
        </if>

        <if test="query.groupLeaderName != null  and query.groupLeaderName != ''">
            and su.user_name like concat('%', #{query.groupLeaderName}, '%')
        </if>
        <if test="query.beginTime != null and query.beginTime != '' and query.endTime != null and query.endTime != ''">
            AND  date_format(wg.add_time,'%Y-%m-%d') BETWEEN #{query.beginTime} AND #{query.endTime}
        </if>

        <if test="query.userIds != null and query.userIds !=''">
            <if test="query.userIds.indexOf(',') != -1">
                AND wg.`owner` in
                <foreach item="item" index="index" collection="query.userIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.userIds.indexOf(',') == -1">
                AND wg.`owner`=#{query.userIds}
            </if>
        </if>
        <if test="query.tagIds != null and query.tagIds !=''">
            AND wt.`id` in
            <foreach item="item" index="index" collection="query.tagIds.split(',')" open="("  close=")" separator=",">
                #{item}
            </foreach>
        </if>
        GROUP BY wg.chat_id
        <if test="query.groupMemberUp != null and query.groupMemberDown != null">
            HAVING
            count(DISTINCT wgm.user_id) BETWEEN  #{query.groupMemberDown}  and  #{query.groupMemberUp}
        </if>
        ORDER BY wg.add_time desc
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>
    </select>

    <select id="countWeGroupListIds"  resultType="long">
        SELECT
          count(DISTINCT wg.id)
        FROM
        we_group wg
        left join we_group_tag_rel wgtr  on wg.chat_id=wgtr.chat_id and wgtr.del_flag = 0
        left join we_tag  wt  on wt.id = wgtr.tag_id
        left join sys_user su on su.we_user_id = wg.owner and su.del_flag = 0
        where wg.del_flag=0
        <if test="query.id !=null">
            and wg.id = #{query.id}
        </if>
        <if test="query.chatId !=null and query.chatId !=''">
            and wg.chat_id in
            <foreach item="item" index="index" collection="query.chatId.split(',')"  open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.groupName != null  and query.groupName != ''">
            AND  CONCAT( IFNULL(wg.group_name,''),IFNULL(su.user_name,''))
            LIKE CONCAT('%',#{query.groupName,jdbcType=VARCHAR},'%')
        </if>

        <if test="query.groupLeaderName != null  and query.groupLeaderName != ''">
            and su.user_name like concat('%', #{query.groupLeaderName}, '%')
        </if>

        <if test="query.beginTime != null and query.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(wg.add_time,'%y%m%d') &gt;= date_format(#{query.beginTime},'%y%m%d')
        </if>
        <if test="query.endTime != null and query.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(wg.add_time,'%y%m%d') &lt;= date_format(#{query.endTime},'%y%m%d')
        </if>
        <if test="query.userIds != null and query.userIds !=''">
            <if test="query.userIds.indexOf(',') != -1">
                AND wg.`owner` in
                <foreach item="item" index="index" collection="query.userIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.userIds.indexOf(',') == -1">
                AND wg.`owner`=#{query.userIds}
            </if>
        </if>
        <if test="query.tagIds != null and query.tagIds !=''">
            AND wt.`id` in
            <foreach item="tagId" index="index" collection="query.tagIds.split(',')" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>

    </select>

    <select id="selectWeGroupListByIds" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
        wg.id,
        wg.chat_id,
        wg.group_name,
        wg.add_time,
        wg.create_time,
        wg.notice,
        wg.status,
        wg.`owner`,
        IFNULL(su.user_name,wg.`owner`) as groupLeaderName,
        count(DISTINCT wgm.user_id) as memberNum,
        count(DISTINCT IF(wgm.type = 2,1, 0)) as customerNum,
        sum(IF(date_format(wgm.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and wgm.quit_scene IS NULL, 1, 0)) as toDayMemberNum,
        sum(IF(date_format(wgm.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and wgm.quit_scene IS NOT NULL, 1, 0)) as toDayExitMemberNum,
        GROUP_CONCAT(distinct wt.`name`) as tags,
        GROUP_CONCAT(distinct wt.`id`) as tagIds
        FROM
        we_group wg
        left join we_group_member wgm  on wgm.chat_id=wg.chat_id and wgm.del_flag=0
        left join we_group_tag_rel wgtr  on wg.chat_id=wgtr.chat_id and wgtr.del_flag = 0
        left join we_tag  wt  on wt.id = wgtr.tag_id
        left join sys_user su on su.we_user_id = wg.owner and su.del_flag = 0
        where wg.del_flag=0
        <if test="ids != null and ids.size() > 0">
            and wg.chat_id in
        <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        </if>
        GROUP BY wg.id
        order by  wg.add_time desc
    </select>

    <select id="selectWeGroupList" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
            wg.id,
            wg.chat_id,
            wg.group_name,
            wg.add_time,
            wg.create_time,
            wg.notice,
            wg.status,
            wg.`owner`,
            IFNULL(su.user_name,wg.`owner`) as groupLeaderName,
            count(DISTINCT wgm.user_id) as memberNum,
            count(DISTINCT IF(wgm.type = 2,1, 0)) as customerNum,
            sum(IF(date_format(wgm.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and wgm.quit_scene IS NULL, 1, 0)) as toDayMemberNum,
            sum(IF(date_format(wgm.join_time, '%Y-%m-%d') = date_format(now(), '%Y-%m-%d') and wgm.quit_scene IS NOT NULL, 1, 0)) as toDayExitMemberNum,
            GROUP_CONCAT(distinct wt.`name`) as tags,
            GROUP_CONCAT(distinct wt.`id`) as tagIds
        FROM
        we_group wg
        left join we_group_member wgm  on wgm.chat_id=wg.chat_id and wgm.del_flag=0
        left join we_group_tag_rel wgtr  on wg.chat_id=wgtr.chat_id and wgtr.del_flag = 0
        left join we_tag  wt  on wt.id = wgtr.tag_id
        left join sys_user su on su.we_user_id = wg.owner and su.del_flag = 0
        where wg.del_flag=0
            <if test="id !=null">
                and wg.id = #{id}
            </if>
            <if test="chatId !=null and chatId !=''">
                and wg.chat_id in
                <foreach item="item" index="index" collection="chatId.split(',')"  open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="groupName != null  and groupName != ''">
                AND  CONCAT( IFNULL(wg.group_name,''),IFNULL(su.user_name,''))
                LIKE CONCAT('%',#{groupName,jdbcType=VARCHAR},'%')
            </if>

            <if test="groupLeaderName != null  and groupLeaderName != ''">
                and su.user_name like concat('%', #{groupLeaderName}, '%')
            </if>

            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(wg.add_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND date_format(wg.add_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="userIds != null and userIds !=''">
                <if test="userIds.indexOf(',') != -1">
                    AND wg.`owner` in
                    <foreach item="item" index="index" collection="userIds.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="userIds.indexOf(',') == -1">
                    AND wg.`owner`=#{userIds}
                </if>
            </if>
            <if test="tagIds != null and tagIds !=''">
                AND wt.`id` in
                <foreach item="tagId" index="index" collection="tagIds.split(',')" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </if>
        group by  wg.id
        order by  wg.add_time desc
    </select>

    <select id="selectWeGroupDetail" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
            wg.id,
            wg.chat_id,
            wg.group_name,
            wg.add_time,
            wg.create_time,
            wg.notice,
            wg.status,
            wg.`owner`,
            su.user_name as groupLeaderName
        FROM
        we_group wg
        left join sys_user su on su.we_user_id = wg.owner and su.del_flag = 0
        where wg.del_flag=0
        <if test="chatId !=null and chatId !=''">
            and wg.chat_id = #{chatId}
        </if>
    </select>

    <select id="selectWeGroupDetailCountInfo" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
            COUNT(CASE WHEN del_flag=0 THEN 1 END) AS memberNum,
            COUNT(CASE WHEN del_flag=0 AND type = 2 THEN 1 END) AS customerNum,
            SUM(CASE WHEN DATE(join_time) = CURDATE() AND del_flag = 0 THEN 1 ELSE 0 END) AS toDayMemberNum,
            SUM(CASE WHEN DATE(quit_time) = CURDATE() THEN 1 ELSE 0 END) AS toDayExitMemberNum
        FROM we_group_member
        <where>
            <if test="chatId !=null and chatId !=''">
                and chat_id = #{chatId}
            </if>
        </where>
    </select>

    <select id="selectWeGroupListByApp" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
            wg.chat_id,
            wg.add_time,
            wg.group_name,
            GROUP_CONCAT(DISTINCT su.user_name)  as  groupLeaderName,
            (SELECT count(DISTINCT wgm.user_id) FROM we_group_member wgm WHERE wgm.chat_id=wg.chat_id and wgm.del_flag=0 ) as memberNum
        FROM
            we_group wg
            LEFT JOIN we_group_tag_rel wgtr ON wg.chat_id=wgtr.chat_id and wgtr.del_flag = 0
            LEFT JOIN sys_user su ON su.we_user_id=wg.owner and su.del_flag = 0
        WHERE
           wg.del_flag=0
            <if test="groupName != null  and groupName != ''">
                AND  CONCAT( IFNULL(wg.group_name,''),IFNULL(su.user_name,''))
                LIKE CONCAT('%',#{groupName,jdbcType=VARCHAR},'%')
            </if>
            <if test="tagIds !=null and tagIds !=''">
                AND wgtr.tag_id in
                <foreach item="item" index="index" collection="tagIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(wg.add_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND date_format(wg.add_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            <if test="userIds != null and userIds !=''">
                <if test="userIds.indexOf(',') != -1">
                    AND wg.`owner` in
                    <foreach item="item" index="index" collection="userIds.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="userIds.indexOf(',') == -1">
                    AND wg.`owner`=#{userIds}
                </if>
            </if>
        GROUP BY wg.chat_id
    </select>


    <insert id="insertBatch">
        insert into we_group (id,chat_id, group_name, owner, add_time, notice, status,admin_user_id, create_by, create_time,update_by, update_time,del_flag,create_by_id, update_by_id)
        values
        <foreach collection="weGroups" item="weGroup" index="index" separator=",">
            (#{weGroup.id},#{weGroup.chatId},#{weGroup.groupName},#{weGroup.owner},#{weGroup.addTime},#{weGroup.notice},#{weGroup.status},#{weGroup.adminUserId},
            #{weGroup.createBy},#{weGroup.createTime},#{weGroup.updateBy},#{weGroup.updateTime},#{weGroup.delFlag},#{weGroup.createById},#{weGroup.updateById})
        </foreach>
        on duplicate key update group_name= values(group_name),owner= values(owner),
        notice= values(notice),status= values(status),admin_user_id= values(admin_user_id),
        del_flag= values(del_flag),
        update_by= values(update_by),update_time= values(update_time),update_by_id= values(update_by_id);
    </insert>


    <select id="findWeGroupByCustomer" resultType="org.scrm.domain.customer.vo.WeCustomerAddGroupVo">
        SELECT
            wg.chat_id,
            wg.group_name,
            wu.user_name as ownerName,
            COUNT(wgm.id)  as groupMemberNum,
            wgm.join_time,
            wgm.join_scene as joinScene,
            (SELECT count(*) FROM we_group_member wgmr WHERE wgmr.chat_id=wg.chat_id and wgmr.user_id=#{userId}) as commonGroup
        FROM
            `we_group` wg
        LEFT JOIN we_group_member wgm ON wgm.chat_id=wg.chat_id
        LEFT JOIN sys_user wu ON wg.`owner`=wu.we_user_id
        WHERE wgm.user_id=#{externalUserid}
        GROUP BY wg.chat_id
    </select>

    <select id="findGroupByUserId" resultType="org.scrm.domain.WeGroup">
        SELECT
            wg.*,
            wgm.join_time
        FROM
            we_group wg
        LEFT JOIN we_group_member wgm on wg.chat_id=wgm.chat_id
        where
           wg.del_flag=0 and wgm.quit_time IS  NULL
            <if test="chatUserId !=null and chatUserId !=''">
                AND wgm.user_id in
                <foreach collection="chatUserId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="states !=null and states !=''">
                AND wgm.state IN
                <foreach collection="states.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="chatIds !=null and chatIds !=''">
                and wg.chat_id in
                <foreach collection="chatIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

    </select>
    <select id="selectChatByMember" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT *
        FROM (
            SELECT
            wg.chat_id,
            wg.add_time,
            wgm.user_id as memberId,
            (SELECT count(wgmm.id) from we_group_member wgmm where wgmm.chat_id=wg.chat_id and wgmm.quit_scene IS NULL) as memberNum
            FROM
            we_group wg
            LEFT JOIN we_group_member wgm ON wg.chat_id = wgm.chat_id and wgm.quit_scene is NULL
            LEFT JOIN we_group_tag_rel wgtr ON wgtr.chat_id=wg.chat_id and wgtr.del_flag=0
            WHERE wg.del_flag=0
            <if test="query.beginTime != null and query.beginTime != '' and query.endTime != null and query.endTime != ''">
                AND  date_format(wg.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>
            <if test="query.tagIds !=null and query.tagIds !=''">
                AND  wgtr.tag_id in
                <foreach collection="query.tagIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.memberIds !=null and query.memberIds !=''">
                and wgm.user_id in
                <foreach collection="query.memberIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="query.userIds != null and query.userIds !=''">
                <if test="query.userIds.indexOf(',') != -1">
                    AND wg.`owner` in
                    <foreach item="item" index="index" collection="query.userIds.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.userIds.indexOf(',') == -1">
                    AND wg.`owner`=#{userIds}
                </if>
            </if>

            <if test="query.chatId !=null and query.chatId !=''">
                and wg.chat_id=#{query.chatId}
            </if>
        ) AS subquery
           <if test="query.groupMemberUp != null and query.groupMemberDown != null">
               where  subquery.memberNum BETWEEN #{query.groupMemberDown} and #{query.groupMemberUp}
           </if>
    </select>


    <select id="getGroupSimpleByChatIds" resultType="org.scrm.domain.groupchat.vo.WeGroupSimpleVo">
        SELECT
        distinct
        wg.chat_id,
        wg.group_name
        FROM
        we_group wg
        <where>
            and wg.chat_id in
            <foreach item="item" index="index" collection="chatIds"  open="(" separator="," close=")">
                #{item}
            </foreach>
            and wg.del_flag = 0
        </where>
    </select>

    <update id="disbandGroup">
       update  we_group set del_flag=1,update_time=now()
       where chat_id = #{chatId}
    </update>


</mapper>
