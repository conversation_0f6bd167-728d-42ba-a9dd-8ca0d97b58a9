<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeKfAnswerMapper">

    <resultMap type="org.scrm.domain.WeKfAnswer" id="WeKfAnswerResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="qtName" column="qt_name" jdbcType="VARCHAR"/>
        <result property="intentId" column="intent_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="org.scrm.domain.kf.vo.WeKfAnswerVo" id="WeKfAnswerDetailResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="groupId" column="group_id" jdbcType="INTEGER"/>
        <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
        <result property="qtName" column="qt_name" jdbcType="VARCHAR"/>
        <result property="intentId" column="intent_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <collection property="qtLikeQuestions" ofType="org.scrm.domain.kf.vo.WeKfAnswerLikeQuestionVo">
            <result property="id" column="lq_id" jdbcType="INTEGER"/>
            <result property="answerId" column="lq_answer_id" jdbcType="INTEGER"/>
            <result property="qtName" column="lq_qt_name" jdbcType="VARCHAR"/>
        </collection>
        <collection property="answerList" ofType="org.scrm.domain.kf.vo.WeKfAnswerAttachmentsVo">
            <result property="id" column="att_id" jdbcType="INTEGER"/>
            <result property="answerId" column="att_answer_id" jdbcType="INTEGER"/>
            <result property="materialId" column="material_id" jdbcType="INTEGER"/>
            <result property="msgType" column="msg_type" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="mediaId" column="media_id" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
            <result property="linkUrl" column="link_url" jdbcType="VARCHAR"/>
            <result property="picUrl" column="pic_url" jdbcType="VARCHAR"/>
            <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="selectWeKfAnswerVo">
        select id,
               group_id,
               intent_id,
               qt_name,
               create_by,
               create_by_id,
               update_time,
               update_by,
               update_by_id,
               del_flag
        from we_kf_answer
    </sql>

    <sql id="selectWeKfAnswerDetailVo">
        select wka.id,
               wka.group_id,
               if(wka.group_id = 1,'默认分组',wkag.name) as group_name,
               wka.intent_id,
               wka.qt_name,
               wka.create_by,
               wka.create_by_id,
               wka.update_time,
               wka.update_by,
               wka.update_by_id,
               wkalq.id        as lq_id,
               wkalq.answer_id as lq_answer_id,
               wkalq.qt_name   as lq_qt_name,
               wkaa.id         as att_id,
               wkaa.answer_id  as att_answer_id,
               wkaa.material_id,
               wkaa.msg_type,
               wkaa.content,
               wkaa.media_id,
               wkaa.title,
               wkaa.description,
               wkaa.file_url,
               wkaa.link_url,
               wkaa.pic_url,
               wkaa.app_id
        from we_kf_answer wka
                 left join we_kf_answer_like_question wkalq on wka.id = wkalq.answer_id and wkalq.del_flag = 0
                 left join we_kf_answer_attachments wkaa on wka.id = wkaa.answer_id and wkaa.del_flag = 0
                 left join we_kf_answer_group wkag  on wkag.id = wka.group_id and wkag.del_flag = 0
    </sql>

    <select id="getAnswerDetail" resultMap="WeKfAnswerDetailResult">
        <include refid="selectWeKfAnswerDetailVo"/>
        <where>
            and wka.id = #{id}
        </where>
    </select>

    <select id="getAnswerIdByQuery" resultType="java.lang.Long">
        select distinct wka.id
        from we_kf_answer wka
        left join we_kf_answer_like_question wkalq on wka.id = wkalq.answer_id and wkalq.del_flag = 0
        left join we_kf_answer_attachments wkaa on wka.id = wkaa.answer_id and wkaa.del_flag = 0
        <where>
            <if test="name != null and name !=''">
                and  wka.qt_name  = #{name}
            </if>
            <if test="groupId != null and groupId !=''">
                and  wka.group_id  = #{groupId}
            </if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND date_format(wka.update_time,'%Y-%m-%d') &gt;= date_format(#{beginTime},'%Y-%m-%d')
            </if>

            <if test="endTime != null"><!-- 结束时间检索 -->
                AND date_format(wka.update_time,'%Y-%m-%d') &lt;= date_format(#{endTime},'%Y-%m-%d')
            </if>
            and wka.del_flag = 0
        </where>
        order by wka.update_time desc
    </select>

    <select id="getAnswerListByIds"  resultMap="WeKfAnswerDetailResult">
        <include refid="selectWeKfAnswerDetailVo"/>
        <where>
            and wka.id in
            <foreach item="item" collection="answerIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
        order by wka.update_time desc
    </select>

</mapper>
