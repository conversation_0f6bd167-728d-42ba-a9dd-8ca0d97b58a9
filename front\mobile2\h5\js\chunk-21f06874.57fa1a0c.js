(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-21f06874"],{"365c":function(t,s,a){"use strict";a.d(s,"b",(function(){return c})),a.d(s,"c",(function(){return r})),a.d(s,"a",(function(){return d})),a.d(s,"d",(function(){return u}));var i=a("b775");const e=window.sysConfig.services.wecom,n=window.sysConfig.services.weChat,o=n+"/live";function c(t){return Object(i["a"])({url:e+"/operation/getCustomerAnalysisForApp",params:t})}function r(t){return Object(i["a"])({url:e+"/operation/getGroupAnalysisByApp",params:t})}function d(t){return Object(i["a"])({url:e+"/trajectory/findCompanyDynamics",params:t})}function u(t){return Object(i["a"])({url:o+"/getLivingCode",params:t})}},"37f9":function(t,s,a){"use strict";a.r(s);a("14d9");var i=function(){var t=this,s=t._self._c;return s("div",{staticClass:"index"},[s("div",{staticClass:"title-content"},[s("div",{staticClass:"title-left"},[s("div",{staticClass:"left-img"},[t.userInfo.avatar?s("img",{attrs:{src:t.userInfo.avatar,alt:""}}):s("svg-icon",{staticClass:"user-icon",attrs:{name:"portrait"}})],1),s("div",{staticClass:"left-text"},[s("div",{staticClass:"text1"},[t._v(t._s(t.userInfo.nickName))]),s("div",{staticClass:"text2"},[t.userInfo.position?s("span",[t._v(t._s(t.userInfo.position)+"  ")]):t._e()]),s("div",{staticClass:"text3"},[s("span",[t._v(t._s(t.userInfo.companyName))])])])]),s("div",{staticClass:"title-right"},[s("div",{staticStyle:{margin:"5px 0"},on:{click:function(s){return t.$router.push("/message")}}},[s("van-badge",{attrs:{content:t.msgNum}},[s("svg-icon",{staticClass:"icon-notice",attrs:{name:"notice"}})],1)],1)])]),s("div",{staticClass:"index-card"},[s("div",{staticClass:"card index-card-item"},[s("div",{staticClass:"card-title"},[s("span"),s("span",[t._v("客户数据")]),s("svg-icon",{staticClass:"question-icon",attrs:{name:"question"},on:{click:function(s){return t.showPopup(0)}}})],1),s("div",{staticClass:"card-box"},[s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.totalCnt))+" ")]),s("div",{staticClass:"text"},[t._v("客户总数")])]),s("div",{staticClass:"card-box-item divide-line"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.tdCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日新增")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.tdFollowUpCustomer))+" ")]),s("div",{staticClass:"text"},[t._v("今日跟进")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.tdNetCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日净增")])]),s("div",{staticClass:"card-box-item divide-line"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.tdLostCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日流失")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.customerOutForm.ydNewApplyCnt))+" ")]),s("div",{staticClass:"text"},[t._v("昨日发送申请")])])])]),s("div",{staticClass:"card index-card-item"},[s("div",{staticClass:"card-title"},[s("span"),s("span",[t._v("客群数据")]),s("svg-icon",{staticClass:"question-icon",attrs:{name:"question"},on:{click:function(s){return t.showPopup(1)}}})],1),s("div",{staticClass:"card-box"},[s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.totalCnt))+" ")]),s("div",{staticClass:"text"},[t._v("客群总数")])]),s("div",{staticClass:"card-box-item divide-line"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.tdGroupAddCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日新增客群")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.tdGroupDissolveCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日解散客群")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.memberTotalCnt))+" ")]),s("div",{staticClass:"text"},[t._v("客群成员总数")])]),s("div",{staticClass:"card-box-item divide-line"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.tdMemberAddCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日新增成员")])]),s("div",{staticClass:"card-box-item"},[s("div",{staticClass:"data"},[t._v(" "+t._s(t.setThousandsMark(t.groupOutForm.tdMemberQuitCnt))+" ")]),s("div",{staticClass:"text"},[t._v("今日退出成员")])])])]),s("div",{staticClass:"card dynamics"},[s("div",{staticClass:"card-title"},[s("span"),s("span",[t._v("企业动态")]),t.dynamicsOutList.length?s("div",{staticClass:"right",on:{click:t.searchAll}},[t._v(" 查看全部 "),s("svg-icon",{staticClass:"right-icon",attrs:{name:"right"}})],1):t._e()]),s("div",{staticClass:"card-box"},[t.dynamicsOutList.length?t._e():s("Empty"),t.dynamicsOutList.length?s("div",{staticClass:"enterprise-dynamics"},t._l(t.dynamicsOutList,(function(a,i){return s("div",{key:i,staticClass:"dynamics-item"},[s("div",{staticClass:"dynamics-left"},[s("div",{staticClass:"top"},[s("span",{class:1===a.operatorType?"customer":"staff"},[t._v(" "+t._s(1===a.operatorType?"客户":"员工")+" ")]),s("span",[t._v(t._s(a.operatorName))])]),s("div",{staticClass:"bottom"},[s("span",{class:"删除员工"===a.action||"解散群聊"===a.action||"退出群聊"===a.action?"unnomal":"nomal"},[t._v(" "+t._s(a.action)+"   ")]),s("span",[t._v(t._s(a.operatoredObjectName))])])]),s("div",{staticClass:"dynamics-right"},[s("span",[t._v(" "+t._s(1===a.trajectoryType?"客户动态":5===a.trajectoryType?"客群动态":"")+" ")]),s("span",[t._v(t._s(a.createTime))])])])})),0):t._e()],1)]),s("div",{staticClass:"card my-task"},[s("div",{staticClass:"card-title mb10"},[s("span"),s("span",[t._v("我的任务")]),s("div",{staticClass:"right",on:{click:function(s){return t.$router.push("/historyTask?type=history")}}},[t._v(" 查看历史任务 "),s("svg-icon",{staticClass:"right-icon",attrs:{name:"right"}})],1)]),s("MyTask")],1)]),s("Loading",{attrs:{isLoad:t.isLoad}}),s("van-overlay",{attrs:{show:t.questionShow,"z-index":"999"}},[s("div",{staticClass:"popup"},[s("div",{staticClass:"popup-content"},[s("div",{staticClass:"pop-content"},[s("div",{staticClass:"pop-bg-top"}),s("div",{staticClass:"pop-bg-bottom"}),s("div",{staticClass:"pop-text"},[s("div",{staticClass:"pop-title"},[t._v("数据指标")]),t._l(t.questionData,(function(a,i){return s("div",{key:i,staticClass:"pop-item"},[s("div",{staticClass:"item-top"},[t._v(t._s(a.title))]),s("div",{staticClass:"item-bottom"},[t._v(t._s(a.content))])])})),s("div",{staticClass:"pop-button"},[s("div",{on:{click:function(s){t.questionShow=!1}}},[t._v("确定")])])],2)]),s("svg-icon",{staticClass:"pop-close-icon",attrs:{name:"pop-close"},on:{click:t.popupClose}})],1)])])],1)},e=[],n=(a("e9f5"),a("7d54"),a("75c6")),o=a("365c"),c=a("764a"),r=a("3a5e"),d=a("fe26"),u={name:"",components:{MySwitch:n["a"],Loading:r["a"],MyTask:d["a"]},data(){return{checked:!0,questionShow:!1,questionData:{},dynamicsList:[{}],userInfo:{avatar:"",nickName:"",position:"",companyName:""},customerForm:{dataScope:!1},customerOutForm:{},groupOutForm:{},dynamicsFrom:{trajectoryType:"",dataScope:!1,pageSize:5,pageNum:1},dynamicsOutList:[],isLoad:!1,msgNum:0,showTask:!1}},computed:{},watch:{},created(){this.getNum(),this.getCustomerData(),this.getGroupData(),this.getDynamicsData()},mounted(){this.userInfo.avatar=sessionStorage.getItem("avatar"),this.userInfo.companyName=sessionStorage.getItem("companyName"),this.userInfo.nickName=sessionStorage.getItem("nickName")?sessionStorage.getItem("nickName"):sessionStorage.getItem("userName"),this.userInfo.position=sessionStorage.getItem("position")},methods:{getNum(){Object(c["b"])().then(t=>{this.msgNum=t.data})},changeFn(){this.checked=!this.checked,this.isLoad=!0,this.customerForm.dataScope=!this.checked,this.dynamicsFrom.dataScope=!this.checked,this.getCustomerData(),this.getGroupData(),this.getDynamicsData()},showPopup(t){let s=[[{title:"【客户总数】",content:"当前员工权限范围内的全部客户数量(含重复)"},{title:"【今日新增】",content:"当前员工权限范围内今日添加的客户数(含重复 及流失)"},{title:"【今日跟进】",content:"当前员工权限范围内今日添加的客户数(含重复及流失)"},{title:"【今日净增】",content:"当前员工权限范围内今日添加的客户数(不含重复及流失)"},{title:"【今日流失】",content:"当前员工权限范围内的流失的全部客户数量"},{title:"【昨日发送申请】",content:"当前员工数权限范围内主动向客户发起的申请数"}],[{title:"【客户总数】",content:"当前员工权限范围内的全部客群数量"},{title:"【今日新增客群】",content:"当前员工权限范围内今日创建的客群数"},{title:"【今日解散客群】",content:"当前员工权限范围内今日创建的客群数"},{title:"【客群成员总数】",content:"当前员工权限范围内客群成员的总数(含员工)"},{title:"【今日新增成员】",content:"当前员工权限范围内今日新增客群成员数(含成员)"},{title:"【今日退出成员】",content:"当前员工数权限范围内今日退出客群成员数(含员工)"}]];this.questionData=s[t],this.questionShow=!0},popupClose(){this.questionShow=!1},searchAll(){this.$router.push({name:"dynamics",query:{dataScope:this.dynamicsFrom.dataScope}})},getCustomerData(){Object(o["b"])(this.customerForm).then(t=>{200===t.code&&(this.customerOutForm=t.data),this.isLoad=!1})},getGroupData(){Object(o["c"])(this.customerForm).then(t=>{200===t.code&&(this.groupOutForm=t.data),this.isLoad=!1})},getDynamicsData(){Object(o["a"])(this.dynamicsFrom).then(t=>{200===t.code&&(this.dynamicsOutList=t.rows,this.dynamicsOutList.forEach(t=>{t.operatoredObjectType=1===t.operatoredObjectType?"客户":2===t.operatoredObjectType?"员工":"客群"}))})},setThousandsMark(t){if(t){t+="";let s=/(?!^)(?=(\d{3})+$)/g;return t.replace(s,",")}return 0}}},l=u,p=(a("5ea1"),a("2877")),v=Object(p["a"])(l,i,e,!1,null,"7fad9747",null);s["default"]=v.exports},"3a5e":function(t,s,a){"use strict";var i=function(){var t=this,s=t._self._c;return s("van-overlay",{attrs:{show:t.isLoad,"z-index":"999"},on:{click:function(s){t.show=!1}}},[s("div",{staticClass:"popup"},[s("div",{staticClass:"pop-box"},[s("div",{staticClass:"pop-box-bg"}),s("div",{staticClass:"pop-box-content"},[s("svg-icon",{staticClass:"lodash-icon",attrs:{name:"lodash"}}),s("span",[t._v("数据查询中，请稍后...")])],1)])])])},e=[],n={props:{isLoad:{type:Boolean,default:!1}}},o=n,c=(a("97db"),a("2877")),r=Object(c["a"])(o,i,e,!1,null,"4cb44c8c",null);s["a"]=r.exports},"5c3e":function(t,s,a){},"5ea1":function(t,s,a){"use strict";a("5c3e")},"73d1":function(t,s,a){},"74c7":function(t,s,a){"use strict";a("f2d5")},"75c6":function(t,s,a){"use strict";var i=function(){var t=this,s=t._self._c;return s("div",[s("span",{staticClass:"weui-switch",class:{"weui-switch-on":t.isChecked},staticStyle:{position:"relative"},attrs:{value:t.value},on:{click:t.toggle}},[t.isChecked&&t.direction.length>0?s("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",padding:"0 5px","line-height":"22px",color:"#FFF","user-select":"none"}},[t._v(" "+t._s(t.direction[0])+" ")]):t._e(),!t.isChecked&&t.direction.length>0?s("div",{staticStyle:{width:"100%",height:"100%",position:"absolute",padding:"0 5px",right:"2px","line-height":"22px",color:"#fff","text-align":"right","user-select":"none"}},[t._v(" "+t._s(t.direction[1])+" ")]):t._e()])])},e=[],n={name:"switch-component",props:{value:{type:Boolean,default:!0},text:{type:String,default:""}},data(){return{isChecked:this.value}},computed:{direction(){return this.text?this.text.split("|"):[]}},watch:{value(t){this.isChecked=t},isChecked(t){this.$emit("change",t)}},methods:{toggle(){this.isChecked=!this.isChecked}}},o=n,c=(a("8957"),a("2877")),r=Object(c["a"])(o,i,e,!1,null,"df57befc",null);s["a"]=r.exports},"764a":function(t,s,a){"use strict";a.d(s,"a",(function(){return d})),a.d(s,"b",(function(){return u})),a.d(s,"c",(function(){return l}));var i=a("b775");const{get:e,post:n,put:o,del:c}=i["b"],r="/message/notification";function d(t){return e(`${r}/${t.type}/list`)}function u(t){return e(r+"/num")}function l(){return n(r+"/read")}},8957:function(t,s,a){"use strict";a("73d1")},"97db":function(t,s,a){"use strict";a("a507")},a507:function(t,s,a){},f21d:function(t,s,a){"use strict";a.d(s,"b",(function(){return d})),a.d(s,"a",(function(){return u}));var i=a("b775");const{get:e,post:n,put:o,del:c}=i["b"],r="/weTasks";function d(t){return t=Object.assign({pageNum:1,pageSize:10,type:"list"},t),e(`${r}/${t.type}`,t)}function u(t){return o(`${r}/finish/${t}`)}},f2d5:function(t,s,a){},fe26:function(t,s,a){"use strict";var i=function(){var t=this,s=t._self._c;return s("PullRefreshScrollLoadList",{attrs:{request:t.getList,dealQueryFun:t.dealQueryFun,dealDataFun:t.dealDataFun},scopedSlots:t._u([{key:"default",fn:function(a){return t._l(a,(function(a,i){return s("div",{key:i,class:["task",0!=a.status&&"history"],on:{click:function(s){return t.goRoute(a)}}},[s("div",[a.statusStr?s("van-tag",{attrs:{type:"primary",size:"medium"}},[t._v(t._s(a.statusStr))]):t._e(),s("span",{staticClass:"task-title"},[t._v(t._s(a.title))])],1),a.contentIsObj?t._l(a.content,(function(a,i){return s("div",{key:i,staticClass:"task-info"},[s("span",{staticClass:"key"},[t._v(t._s(i)+":")]),s("span",{staticClass:"value"},[t._v(t._s(a))])])})):s("div",{staticClass:"task-info"},[t._v(" "+t._s(t.content)+" ")]),s("div",{staticClass:"task-time"},[t._v(t._s(a.sendTime))])],2)}))}}])})},e=[],n=(a("14d9"),a("e9f5"),a("7d54"),a("f21d")),o={name:"",props:{type:{type:String,default:"list"}},data(){return{getList:n["b"],queryType:"list"}},computed:{},watch:{},created(){},mounted(){},methods:{dealQueryFun(t){let s=this.$route.query.type;this.queryType=t.type=s||this.type},dealDataFun(t){t.forEach(t=>{let s=t.content;s&&s.includes("{")&&(t.content=JSON.parse(s),t.contentIsObj=!0)})},goRoute(t){/^http/.test(t.url)?location.href=t.url:this.$router.push(`${t.url}${t.url.includes("?")?"&":"?"}tasksId=${t.id}&status=${t.status}`)}}},c=o,r=(a("74c7"),a("2877")),d=Object(r["a"])(c,i,e,!1,null,"2e8294ac",null);s["a"]=d.exports}}]);