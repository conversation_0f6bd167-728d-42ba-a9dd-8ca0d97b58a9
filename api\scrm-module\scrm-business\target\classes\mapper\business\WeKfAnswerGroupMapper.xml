<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeKfAnswerGroupMapper">

    <resultMap type="org.scrm.domain.WeKfAnswerGroup" id="WeKfAnswerGroupResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="groupId" column="group_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectWeKfAnswerGroupVo">
        select id,
               group_id,
               name,
               is_default,
               create_time,
               create_by,
               create_by_id,
               update_time,
               update_by,
               update_by_id,
               del_flag
        from we_kf_answer_group
    </sql>

    <select id="getAnswerGroupList" resultType="org.scrm.domain.kf.vo.WeKfAnswerGroupVo">
        <include refid="selectWeKfAnswerGroupVo"/>
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>

</mapper>
