(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-656c16cd"],{"0833":function(t,e,i){"use strict";i("b160")},1013:function(t,e,i){"use strict";i("a8ce1")},"11d5":function(t,e,i){},"194d":function(t,e,i){"use strict";i("11d5")},"20fb":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return t._list&&t._list.length||t.emptyText?e("div",{staticClass:"tag-ellipsis"},[t._list&&t._list.length?t._e():e("div",[t._v(t._s(t.emptyText))]),e("div",{staticClass:"tag-all"},[t._l(t._list.slice(0,t.showMoreTag?void 0:+t.limit),(function(i,s){return e("van-tag",t._b({key:s,attrs:{round:""}},"van-tag",t.elTagProps,!1),[t._v(" "+t._s(i[t.defaultProps]||i)+" ")])})),t._list.length>+t.limit?e("van-tag",t._b({key:"a",attrs:{round:"",type:"primary"},on:{click:function(e){t.showMoreTag=!t.showMoreTag}}},"van-tag",t.elTagProps,!1),[t._v(" "+t._s(t.showMoreTag?"收起":`...展开全部${t._list.length}个`)+" ")]):t._e()],2)]):t._e()},a=[],o=(i("e9f5"),i("910d"),{name:"TagEllipsis",components:{},props:{list:{type:Array,default:()=>[]},limit:{type:[String,Number],default:2},defaultProps:{type:String,default:"name"},emptyText:{type:String,default:""}},data(){return{showMoreTag:!1}},computed:{_list(){return this.list.filter(t=>"string"===typeof t?t:t[this.defaultProps])}},watch:{},created(){this.elTagProps=Object.assign({},this.$attrs),delete this.elTagProps.style},mounted(){},methods:{}}),n=o,l=(i("1013"),i("2877")),r=Object(l["a"])(n,s,a,!1,null,"55d52006",null);e["a"]=r.exports},"266b":function(t,e,i){},"2b91":function(t,e,i){"use strict";i.d(e,"l",(function(){return c})),i.d(e,"j",(function(){return u})),i.d(e,"d",(function(){return d})),i.d(e,"a",(function(){return p})),i.d(e,"c",(function(){return m})),i.d(e,"k",(function(){return f})),i.d(e,"i",(function(){return h})),i.d(e,"m",(function(){return g})),i.d(e,"o",(function(){return v})),i.d(e,"n",(function(){return b})),i.d(e,"p",(function(){return y})),i.d(e,"e",(function(){return w})),i.d(e,"b",(function(){return k})),i.d(e,"h",(function(){return _})),i.d(e,"g",(function(){return C})),i.d(e,"f",(function(){return I}));var s=i("b775");const{get:a,post:o,put:n,del:l}=s["b"],r="/leads";function c(){return Object(s["b"])({url:"/sea/manager/list",method:"get"})}function u(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(s["b"])({url:r+"/sea/list",params:t})}function d(){return Object(s["b"])({url:r+"/template/settings/editable"})}function p(t){return t.leadsId=t.leadId,Object(s["b"])({url:r+"/manual/add",method:"post",data:t})}function m(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(s["b"])({url:r+"/manual/list",params:t})}function f(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(s["b"])({url:r+"/my/follow",params:t})}function h(t){return Object(s["b"])({url:r+"/get/"+t,method:"get"})}function g(t){return Object(s["b"])({url:r+"/receive",method:"get",params:{leadsId:t}})}function v(t){return t.leadsId=t.leadId,Object(s["b"])({url:r+"/user/return",data:t,method:"post"})}function b(t){return t.leadsId=t.leadId,Object(s["b"])({url:r+"/update",data:t,method:"put"})}function y(t){return t.leadsId=t.leadId,Object(s["b"])({url:r+"/bind/customer",method:"post",data:t})}function w(){return Object(s["b"])({url:r+"/follow/record/getFollowMode",method:"get"})}function k(t){return Object(s["b"])({url:r+"/follow/record/addFollow",data:t,method:"post"})}function _(t){return Object(s["b"])({url:r+"/follow/record/getFollowUpList/"+t,method:"get"})}function C(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(s["b"])({url:r+"/follow/record/list",params:t,method:"get"})}function I(t){return Object(s["b"])({url:r+"/follow/record/"+t})}},"37d3":function(t,e,i){},4642:function(t,e,i){},"696e":function(t,e,i){"use strict";i("37d3")},"711e":function(t,e,i){"use strict";i("a823")},"74f1":function(t,e,i){"use strict";var s=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"timeline-time-wrap"},[e("van-list",{attrs:{finished:t.finished,"finished-text":t.finishedText,error:t.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(e){t.error=e},load:function(e){return t.getList()}},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},[t.list.length?t._e():e("Empty",{attrs:{description:"暂无跟进历史记录"}}),e("ul",{staticClass:"infinite-list",staticStyle:{overflow:"auto"}},t._l(t.list,(function(i,s){return e("li",{key:s,staticClass:"infinite-list-item"},[i[0].createTime?e("div",{staticClass:"timeline-time"},[t._v(" "+t._s(t.dateFormat(i[0].createTime,"yyyy-MM-dd w"))+" ")]):t._e(),e("van-steps",{staticClass:"timeline-box",attrs:{active:"nu",direction:"vertical","inactive-icon":"-","active-icon":"-"}},t._l(i,(function(i,s){return e("van-step",{key:s,staticClass:"timeline-box-item",attrs:{placement:"top",type:"primary"}},[i.createTime?e("span",{staticClass:"timeline-box-item-timestamp"},[t._v(" "+t._s(i.createTime.slice(10,16))+" ")]):t._e(),i.recordStatusFullName?e("p",{staticClass:"timeline-box-item-title"},[t._v(" "+t._s(i.recordStatusFullName||"-")+" ")]):t._e(),t._l(i.contents,(function(i,s){return e("div",{key:s,staticClass:"flex timeline-box-item-content"},[e("span",{staticClass:"timeline-box-item-content-name"},[t._v(t._s(i.itemKey||"-")+":")]),t._l(i.itemValue,(function(i,s){return e("div",{key:s,staticClass:"item-content gap10 gap5"},[e("div",{domProps:{innerHTML:t._s(i.content)}}),1==i.isAttachment?t._l(i.attachments,(function(i,s){return e("a",{key:s,staticClass:"link",attrs:{target:"_blank"},on:{click:function(e){return e.preventDefault(),t.previewFile(i)}}},[t._v(" "+t._s(i.title||"-")+" ")])})):t._e()],2)}))],2)}))],2)})),1)],1)})),0)],1)],1),e("Preview",{attrs:{urlList:t.previewFileObj.url,type:t.previewFileObj.type},model:{value:t.previewFileObj.visible,callback:function(e){t.$set(t.previewFileObj,"visible",e)},expression:"previewFileObj.visible"}})],1)},a=[],o=(i("14d9"),i("e9f5"),i("7d54"),i("2b91")),n=i("ed08"),l=function(){var t=this,e=t._self._c;return t.visible?e("div",{staticClass:"preview-box"},[0===t.type?e("van-image-preview",{attrs:{images:[t.url]},on:{close:t.closeDialog},model:{value:t.showImage,callback:function(e){t.showImage=e},expression:"showImage"}}):t._e(),2===t.type?e("van-overlay",{attrs:{show:t.showVideo}},[e("div",{staticClass:"box"},[e("video",{staticClass:"preview-item video",attrs:{src:t.url,controls:""}})])]):t._e()],1):t._e()},r=[],c={name:"Preview",components:{},props:{value:{type:Boolean,default:!1},title:{type:String,default:"预览"},type:{type:[Number,String],default:0},urlList:{type:[String,Array],default:""}},data(){return{list:[],showImage:!1,showVideo:!1}},computed:{visible:{get(){return this.value},set(t){this.$emit("update:value",t)}},url(){return Array.isArray(this.urlList)?this.urlList[0]:this.urlList||""}},watch:{visible(t){t&&0===this.type&&this.$nextTick(()=>{this.showImage=!0}),t&&2===this.type&&this.$nextTick(()=>{this.showVideo=!0})}},created(){},methods:{closeDialog(){this.showImage=!1,this.showVideo=!1,this.visible=!1}}},u=c,d=(i("8d12"),i("2877")),p=Object(d["a"])(u,l,r,!1,null,"e5e53540",null),m=p.exports,f={name:"",components:{Preview:m},props:{isData:{type:Boolean,default:!1},data:{},id:{type:String,default:null},leadId:{type:String,default:null},followUserId:{type:String,default:null}},data(){return{loading:!1,query:{pageNum:1,pageSize:10,weLeadsId:"",followUserId:""},refreshing:!1,finished:!1,finishedText:"看到底啦~",error:!1,list:[],dateFormat:n["b"],previewFileObj:{}}},watch:{},created(){},methods:{initData(t){if(!t)return[];const e=[],i=[];t.forEach(t=>{t.createTime=t.createTime;const e=this.dateFormat(t.createTime,"yyyyMMdd");i.includes(e)||i.push(e)}),i.sort((t,e)=>e-t);for(let s=0;s<i.length;s++){const a=[];for(let e=0;e<t.length;e++)i[s]===this.dateFormat(t[e].createTime,"yyyyMMdd")&&a.push(t[e]);e.push(a)}return e},getList(t){if(this.isData)return this.list.push([this.data]),void(this.finished=!0);this.loading=!0,this.finished=!1,this.query.id=this.id,this.query.weLeadsId=this.leadId,this.query.followUserId=this.followUserId,t&&(this.query.pageNum=t),Object(o["g"])(this.query).then(({rows:t,total:e})=>{1==this.query.pageNum&&(this.list=[]),this.list.push(...this.initData(t)),this.loading=!1,this.refreshing=!1,this.finished=!0,this.list.length>=+e?this.finished=!0:this.query.pageNum++}).catch(()=>{this.error=!0,this.loading=!1})},previewFile(t){[0,2].includes(t.type)?(t.visible=!0,this.previewFileObj=t):window.open(t.url)}}},h=f,g=(i("0833"),Object(d["a"])(h,s,a,!1,null,"d06d2e52",null));e["a"]=g.exports},"7d58":function(t,e,i){"use strict";i("266b")},"7f76":function(t,e,i){"use strict";i.r(e);i("14d9");var s=function(){var t=this,e=t._self._c;return e("div",{staticClass:"page"},[e("div",{},[e("div",{staticClass:"custom",class:{"is-my":t.isMy&&1===t.customInfo.leadsStatus,"is-new":t.isNew}},[e("div",{staticClass:"card-custom flex"},[e("div",{staticClass:"custom-right",staticStyle:{flex:"auto"}},[e("div",{staticClass:"custom-name fxbw aic"},[e("span",[t._v(t._s(t.customInfo.name))]),t.isMy?e("div",{staticStyle:{flex:"none"}},[e("van-button",{attrs:{round:"",type:"info",size:"mini"},on:{click:function(e){t.showTransfer=!0}}},[t._v(" 转化为客户 ")]),e("svg-icon",{staticClass:"ml5",staticStyle:{"font-size":"20px","vertical-align":"middle"},attrs:{name:"question"},on:{click:function(e){return t.showPopup(0)}}})],1):t._e()]),t.customInfo.preFollowerName?e("div",{staticClass:"custom-info"},[e("span",{staticClass:"key"},[t._v("前跟进人")]),e("span",{staticClass:"value"},[t._v(t._s(t.customInfo.preFollowerName))])]):t._e(),e("div",{staticClass:"custom-info"},[e("span",{staticClass:"key"},[t._v(t._s({0:"投放",1:"领取",3:"回收"}[t.customInfo.leadsStatus])+"时间")]),e("span",{staticClass:"value"},[t._v(" "+t._s(0===t.customInfo.leadsStatus?t.customInfo.createTime:t.customInfo.updateTime)+" ")])])])]),t.isMy?e("div",{staticClass:"action flex ais"},[e("div",{staticClass:"item",on:{click:function(e){return t.phoneCall(t.customInfo.phone)}}},[e("svg-icon",{staticClass:"icon",attrs:{name:"phone"}}),e("span",{staticClass:"text"},[t._v("打电话")])],1),t.customInfo.externalUserid?e("div",{staticClass:"item",on:{click:function(e){return t.$router.push("/portrait")}}},[e("van-icon",{staticClass:"icon",attrs:{name:"user-circle-o"}}),e("span",{staticClass:"text"},[t._v("客户画像")])],1):e("div",{staticClass:"item",on:{click:function(e){return t.addFriend(t.customInfo.phone)}}},[e("svg-icon",{staticClass:"icon",attrs:{name:"add-friend"}}),e("span",{staticClass:"text"},[t._v("加好友")])],1)]):t._e()]),t.isNew?[e("van-cell",{attrs:{title:"手机号码",value:t.customInfo.phone||"-"}}),e("van-cell",{attrs:{title:"性别",value:t.customInfo.sexStr||"-"}}),e("van-cell",{attrs:{title:"线索来源",value:t.customInfo.sourceStr||"-"}}),t._l(t.customInfo.propertiesList,(function(t,i){return e("van-cell",{key:i,attrs:{title:t.name,value:t.value||"-"}})}))]:[t.customInfo.labelsNames||t.isMy?e("div",{staticClass:"labels"},[t.isMy?[e("div",{staticClass:"label-t mb10"},[e("span",[t._v("标签信息")]),e("span",{staticClass:"btn-edit",on:{click:function(e){t.showEdit=!0}}},[t._v("修改")])])]:t._e(),e("TagEllipsis",{attrs:{list:t.tags,limit:8}}),!t.customInfo.labelsNames&&t.isMy?e("Empty",{staticStyle:{height:"116px"}}):t._e()],2):t._e(),e("van-tabs",{ref:"tabs",staticClass:"detail-tab"},[t.isMy?e("van-tab",{attrs:{title:"我的跟进",name:"follow"}},[e("Record",{ref:"record",attrs:{leadId:t.leadsId,followUserId:t.customInfo.followerId}})],1):t._e(),e("van-tab",{attrs:{title:"详细资料",name:"detail"}},[e("van-cell",{attrs:{title:"手机号码",value:t.customInfo.phone||"-"}}),e("van-cell",{attrs:{title:"性别",value:t.customInfo.sexStr||"-"}}),e("van-cell",{attrs:{title:"线索来源",value:t.customInfo.sourceStr||"-"}}),t.customInfo.propertiesList?t._l(t.customInfo.propertiesList,(function(t,i){return e("van-cell",{key:i,attrs:{title:t.name,value:t.value||"-"}})})):t._e()],2),[1,3].includes(t.customInfo.leadsStatus)?e("van-tab",{attrs:{title:"前跟进人记录",name:"record"}},[e("van-tabs",t._l(t.beforeFollowUp,(function(i,s){return e("van-tab",{key:s,scopedSlots:t._u([{key:"title",fn:function(){return[e("span",[t._v(t._s(i.followUserName))])]},proxy:!0}],null,!0)},[e("Record",{ref:"record2",refInFor:!0,attrs:{id:i.id}})],1)})),1)],1):t._e()],1)]],2),e("div",{staticClass:"footer"},[[0,3].includes(t.customInfo.leadsStatus)?e("van-button",{attrs:{type:"info",round:"",block:""},on:{click:t.collectClue}},[t._v(" 领取线索 ")]):t._e(),t.isMy&&[1].includes(t.customInfo.leadsStatus)?e("div",{staticClass:"flex"},[e("van-button",{attrs:{type:"danger",round:"",block:""},on:{click:function(e){t.showBack=!0}}},[t._v("线索退回")]),e("van-button",{staticClass:"ml10",attrs:{type:"info",round:"",block:""},on:{click:function(e){t.showFollowup=!0}}},[t._v("添加跟进")])],1):t._e()],1),e("edit",{attrs:{leadData:t.customInfo},on:{success:t.updateInfo},model:{value:t.showEdit,callback:function(e){t.showEdit=e},expression:"showEdit"}}),e("back",{attrs:{leadId:t.leadsId},on:{success:t.updateInfo},model:{value:t.showBack,callback:function(e){t.showBack=e},expression:"showBack"}}),e("transfer",{attrs:{leadId:t.leadsId},on:{success:t.updateInfo},model:{value:t.showTransfer,callback:function(e){t.showTransfer=e},expression:"showTransfer"}}),e("followup",{attrs:{title:"添加跟进",leadId:t.leadsId},on:{success:function(e){return t.$refs.record.getList(1)}},model:{value:t.showFollowup,callback:function(e){t.showFollowup=e},expression:"showFollowup"}})],1)},a=[],o=i("20fb"),n=i("74f1"),l=function(){var t=this,e=t._self._c;return e("van-action-sheet",t._b({attrs:{position:"bottom",title:"线索退回",closeable:!1,round:"","close-on-click-overlay":!1}},"van-action-sheet",t.$attrs,!1),[e("van-form",{attrs:{"validate-first":""},on:{failed:t.onFailed,submit:t.submit}},[e("van-field",{staticClass:"back-reson",attrs:{readonly:"",clickable:"",value:t.form.seaName,label:"选择公海",placeholder:"请选择",rules:[{required:!0}],"input-align":"right","right-icon":"arrow",required:""},on:{click:function(e){t.showPicker=!0}}}),e("van-popup",{attrs:{position:"bottom",round:""},model:{value:t.showPicker,callback:function(e){t.showPicker=e},expression:"showPicker"}},[e("van-picker",{attrs:{"show-toolbar":"",title:"选择退回公海",columns:t.seaList,"value-key":"name"},on:{confirm:t.onConfirm,cancel:function(e){t.showPicker=!1}}})],1),e("van-field",{attrs:{label:"退回备注",rows:"6",autosize:"",type:"textarea",maxlength:"100","show-word-limit":"",placeholder:"请输入"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}}),e("div",{staticClass:"flex",staticStyle:{margin:"20px"}},[e("van-button",{attrs:{round:"",block:"",plain:"","native-type":"button"},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{round:"",block:"",type:"info","native-type":"submit"}},[t._v("提交")])],1)],1)],1)},r=[],c=i("2b91"),u={name:"back",components:{},props:{leadId:""},data(){return{form:{remark:""},seaList:[],showPicker:!1,showBack:!0}},created(){this.getSeaList()},computed:{},methods:{onFailed(t){console.log("failed",t)},submit(){const t=this.$toast.loading();this.form.leadId=this.leadId,Object(c["o"])(this.form).then(t=>{this.showConfirm=!1,this.cancel(),this.$emit("success"),this.$toast.success("操作成功")}).finally(()=>{t.clear()})},getSeaList(){Object(c["l"])().then(t=>{this.seaList=t.data})},onConfirm(t,e){this.form.seaId=t.id,this.form.seaName=t.name,this.showPicker=!1},cancel(){this.$emit("input",!1)}}},d=u,p=i("2877"),m=Object(p["a"])(d,l,r,!1,null,"37e504fc",null),f=m.exports,h=(i("e9f5"),i("a732"),function(){var t=this,e=t._self._c;return e("van-action-sheet",t._b({attrs:{title:"修改标签",round:"",position:"bottom",closeable:!1,"close-on-click-overlay":!1},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},"van-action-sheet",t.$attrs,!1),[e("div",{staticClass:"content"},[t._l(t.alllabel,(function(i,s){return e("div",{key:s},[e("div",{staticClass:"mb15 mt5 tag-group-name"},[t._v(t._s(i.groupName))]),e("div",{staticClass:"labelstyle"},t._l(i.weTags,(function(i,s){return e("div",{key:s,staticClass:"label",class:t.addTag.some(t=>t.tagId==i.tagId)&&"active",on:{click:function(e){return t.clickLabel(i)}}},[t._v(" "+t._s(i.name)+" ")])})),0)])})),t.alllabel&&t.alllabel.length?t._e():e("van-empty",{attrs:{"image-size":"50",description:"暂无数据"}})],2),e("div",{staticClass:"footer flex",staticStyle:{margin:"20px"}},[e("van-button",{attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{type:"info","native-type":"submit",round:"",block:""},on:{click:t.submit}},[t._v("提交")])],1)])}),g=[],v=(i("f665"),i("7d54"),i("ab43"),i("c2eb")),b={name:"",props:{value:{},leadData:{type:Object,default:()=>({})}},components:{},data(){return{addTag:[],alllabel:[]}},computed:{},watch:{value:{handler(t){t&&this.labelEdit()},immediate:!0}},created(){},mounted(){},methods:{getAllTags(t){this.$toast.loading({message:"loading...",duration:0,forbidClick:!0});let e={groupTagType:1};return"person"===t&&(e={groupTagType:3,userId:this.userId}),Object(v["f"])(e).then(({data:t})=>{this.alllabel=this.listTagOneArray=t,"person"!==this.editLabelType&&(this.listTagOneArray=[],t.forEach(t=>{t.weTags.forEach(t=>{this.listTagOneArray.push(t)})})),this.$toast.clear()}).catch(t=>{console.log(t),this.$toast.clear()})},async labelEdit(t){var e;await((null===(e=this.alllabel)||void 0===e?void 0:e.length)||this.getAllTags(t)),this.addTag=[];let i=this.leadData.labelsIds.split(","),s=this.leadData.labelsNames.split(","),a=i.map((t,e)=>({tagId:t,name:s[e]})),o=[];a&&a.forEach(t=>{let e=this.listTagOneArray.find(e=>e.tagId===t.tagId);e?this.addTag.push(e):o.push(t.name)})},clickLabel(t){let e=this.addTag.findIndex(e=>t.tagId==e.tagId);-1==e?this.addTag.push({groupId:t.groupId,name:t.name,tagId:t.tagId}):this.addTag.splice(e,1)},submit(){const t=this.$toast.loading();let e={leadId:this.leadData.id,labelsIds:this.addTag.map(t=>t.tagId).join(",")};Object(c["n"])(e).then(e=>{this.cancel(),t.clear(),this.$emit("success"),this.$toast.success("操作成功")})},cancel(){this.$emit("input",!1)}}},y=b,w=(i("194d"),Object(p["a"])(y,h,g,!1,null,"3fd821de",null)),k=w.exports,_=function(){var t=this,e=t._self._c;return e("van-popup",t._b({staticStyle:{width:"95%",height:"100%"},attrs:{position:"right","overlay-style":{backgroundColor:"rgba(0, 0, 0, 0.6)"},"close-on-click-overlay":!1}},"van-popup",t.$attrs,!1),[e("div",{staticClass:"content"},[e("customer",{attrs:{isSelectCustomer:""},on:{change:t.change}}),e("div",{staticClass:"footer flex",staticStyle:{margin:"20px",position:"sticky",bottom:"0px"}},[e("van-button",{attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{type:"info","native-type":"submit",round:"",block:""},on:{click:t.submit}},[t._v("提交")])],1)],1)])},C=[],I=i("9495"),x={name:"",props:{leadId:""},components:{customer:I["default"]},data(){return{customerId:""}},computed:{},watch:{},created(){},mounted(){},methods:{change(t){this.customerId=t.id,this.externalUserid=t.externalUserid},submit(){this.customerId&&this.externalUserid?this.$dialog.confirm({title:"关联线索",message:"线索关联后不可取消，是否确认关联?"}).then(()=>{let t={leadId:this.leadId,customerId:this.customerId,externalUserid:this.externalUserid};const e=this.$toast.loading();Object(c["p"])(t).then(t=>{this.cancel(),this.$toast.success("操作成功")}).finally(()=>{e.clear()})}):this.$toast("请先选择客户")},cancel(){this.$emit("input",!1)}}},O=x,T=(i("f59b"),Object(p["a"])(O,_,C,!1,null,"4b905624",null)),j=T.exports,S=function(){var t=this,e=t._self._c;return e("van-action-sheet",t._b({attrs:{round:"",position:"bottom",closeable:!1,"close-on-click-overlay":!1}},"van-action-sheet",t.$attrs,!1),[e("van-form",{on:{submit:t.onSubmit}},[e("van-field",{attrs:{readonly:"",required:"",label:"跟进方式",placeholder:"请选择",rules:t.followRules.followMode},on:{click:function(e){return t.$set(t.show,"mode",!0)}},model:{value:t.followForm.followModeName,callback:function(e){t.$set(t.followForm,"followModeName","string"===typeof e?e.trim():e)},expression:"followForm.followModeName"}}),e("van-popup",{attrs:{round:"",position:"bottom"},model:{value:t.show.mode,callback:function(e){t.$set(t.show,"mode",e)},expression:"show.mode"}},[e("van-picker",{attrs:{title:"请选择跟进方式","value-key":"label","show-toolbar":"",columns:t.typeEnums},on:{cancel:function(e){t.show.mode=!1},confirm:t.modeChange}})],1),e("van-field",{attrs:{rules:t.followRules.recordContent,type:"textarea",rows:"6",required:"",autosize:"",label:"记录内容",placeholder:"请输入"},scopedSlots:t._u([{key:"input",fn:function(){return[e("div",{staticClass:"diy-input",on:{input:function(t){}}},[e("div",{ref:"contenteditable",staticClass:"contenteditable-textarea",attrs:{contenteditable:"true"},domProps:{innerHTML:t._s(t.followForm.recordContent)}}),e("van-button",{staticClass:"mt10",attrs:{type:"primary",size:"mini","native-type":"button",round:""},on:{click:function(e){return t.$set(t.show,"user",!0)}}},[t._v(" 添加协作成员 "),e("van-icon",{attrs:{name:"info"},nativeOn:{click:function(e){return e.stopPropagation(),t.showDialog("user")}}})],1)],1)]},proxy:!0}]),model:{value:t.followForm.recordContent,callback:function(e){t.$set(t.followForm,"recordContent",e)},expression:"followForm.recordContent"}}),e("van-field",{attrs:{readonly:"",clickable:"",name:"datetimePicker",value:t.followForm.cooperateTime,label:"协作日期",placeholder:"请选择"},on:{click:function(e){t.$set(t.show,"dateTime",!0),t.followForm._cooperateTime=new Date(t.followForm.cooperateTime||new Date)}},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",[t._v("协作日期")]),e("van-icon",{attrs:{name:"info ml10"},nativeOn:{click:function(e){return e.stopPropagation(),t.showDialog("datetime")}}})]},proxy:!0}])}),e("van-popup",{attrs:{position:"bottom"},model:{value:t.show.dateTime,callback:function(e){t.$set(t.show,"dateTime",e)},expression:"show.dateTime"}},[e("van-datetime-picker",{attrs:{title:"选择日期和时间",type:"datetime"},on:{confirm:t.dateTimConfirm,cancel:function(e){t.show.dateTime=!1}},model:{value:t.followForm._cooperateTime,callback:function(e){t.$set(t.followForm,"_cooperateTime",e)},expression:"followForm._cooperateTime"}})],1),e("van-field",{staticClass:"upload",attrs:{label:"上传附件",placeholder:"请选择"},scopedSlots:t._u([{key:"label",fn:function(){return[e("span",[t._v("上传附件")]),e("van-icon",{attrs:{name:"info ml10"},nativeOn:{click:function(e){return e.stopPropagation(),t.showDialog("upload")}}})]},proxy:!0},{key:"input",fn:function(){return[e("Upload",{ref:"upload",attrs:{format:t.format,fileList:t.followForm.attachmentList,multiple:"",limit:"10","result-type":"file"},on:{"update:fileList":function(e){return t.$set(t.followForm,"attachmentList",e)},"update:file-list":function(e){return t.$set(t.followForm,"attachmentList",e)}}})]},proxy:!0}])}),e("div",{staticClass:"footer flex",staticStyle:{margin:"20px"}},[e("van-button",{attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{type:"info","native-type":"submit",round:"",block:""}},[t._v("提交")])],1)],1),e("SelectStaff",{on:{change:t.selectStaff},model:{value:t.show.user,callback:function(e){t.$set(t.show,"user",e)},expression:"show.user"}})],1)},$=[],F=(i("910d"),i("ed08")),L=function(){var t=this,e=t._self._c;return e("div",[t.multiple?[t._l(t.fileListWatch,(function(i,s){return[0==(i.myType||t.getFileType(i.url))?e("div",{key:s,staticClass:"upload-item img-item"},[e("van-image",{ref:"image",refInFor:!0,staticClass:"upload-img uploader-size",attrs:{src:i.url,fit:"contain","preview-src-list":t.fileListWatch.map(t=>t.url),alt:""}}),e("div",{staticClass:"action-mask"},[t.isOnlyShowFile?t._e():e("div",{staticClass:"icon-img-delete",on:{click:function(e){return t.remove(s)}}},[e("van-icon",{attrs:{name:"cross"}})],1)])],1):e("div",{key:s,staticClass:"upload-item file-item"},[e("span",{},[t._v(t._s(i.name))]),t.isOnlyShowFile?t._e():e("van-icon",{staticClass:"icon",attrs:{name:"delete"},on:{click:function(e){return t.remove(s)}}})],1)]}))]:t._e(),t.isOnlyShowFile||t.multiple&&t.limit&&!(t.fileListWatch.length<t.limit)?t._e():e("van-uploader",{staticClass:"uploader",attrs:{accept:t.accept||t.acceptAuto,"preview-image":!1,"file-list":t.fileListWatch,disabled:t.disabled||t.loading,multiple:t.multiple&&1!=t.limit,"max-count":t.limit,"max-size":1024*t.maxSize*1024,"before-read":t.handleBeforeUpload,"after-read":t.upload,"result-type":"file"}},[t._t("default",(function(){return[t.loading||t.fileUrlWatch?t._e():e("div",{staticClass:"uploader-icon upload-action uploader-size"},[e("van-icon",{attrs:{name:"plus"}})],1),e("TransitionGroup",[t.loading?e("div",{key:1,staticClass:"upload-action uploader-size"},[e("van-loading",{staticClass:"cc"}),t.speed?e("div",{staticClass:"cc",staticStyle:{"margin-top":"35px"}},[t._v(" "+t._s(t.speed+"M/s")+" ")]):t._e()],1):t._e(),t.loading||!t.fileUrlWatch||t.multiple?t._e():e("div",{key:2,staticClass:"upload-item"},[0==t.type?[t.fileUrlWatch?e("van-image",{ref:"image",staticClass:"upload-img upload-img-single uploader-size",attrs:{src:t.fileUrlWatch,"preview-src-list":[t.fileUrlWatch],"preview-teleported":"",fit:"contain"},on:{click:function(t){t.stopPropagation()}}}):t._e(),e("div",{staticClass:"action-mask",on:{click:function(t){if(t.target!==t.currentTarget)return null;t.stopPropagation()}}},[e("van-icon",{attrs:{name:"search"},on:{click:function(e){return e.stopPropagation(),t.showView(0)}}}),e("van-icon",{attrs:{name:"edit"}})],1)]:2==t.type?[e("video",{key:t.fileUrlWatch,ref:"video",staticClass:"upload-video",attrs:{id:"myVideo",width:"100%",controls:"","webkit-playsinline":"true",playsinline:"true",autoplay:!1,preload:"auto"}},[e("source",{attrs:{src:t.fileUrlWatch,type:"video/mp4"}})]),e("div",{staticClass:"action-mask",staticStyle:{height:"30%"},on:{click:function(t){if(t.target!==t.currentTarget)return null;t.stopPropagation()}}},[e("van-icon",{attrs:{name:"edit"}})],1)]:[t._v(" "+t._s(t.fileNameWatch||t.fileUrlWatch)+" "),e("van-icon",{attrs:{name:"edit"}})]],2)])]}))],2),e("div",{staticClass:"tip"},[t._t("tip")],2)],2)},N=[],U=(i("88a7"),i("271a"),i("5494"),i("2934")),M=i("40b3"),P=i.n(M),A=i("016c"),E=i.n(A),W=i("9b15"),D=i.n(W),z=i("28a2"),q={components:{},props:{fileUrl:{type:String,default:""},fileName:{type:String,default:""},imgSize:{type:String,default:""},fileList:{type:Array,default:()=>[]},type:{type:String,default:void 0,validator:function(t){return["0","1","2","3"].includes(t)}},maxSize:{type:Number,default:void 0},maxImgPx:{type:Array,default:null},format:{type:Array,default:void 0},accept:{type:String,default:""},multiple:{type:Boolean,default:!1},limit:{type:[Number,String],default:void 0},disabled:{type:Boolean,default:!1},isOnlyShowFile:{type:Boolean,default:!1}},data(){return{loading:!1,fileUrlWatch:this.fileUrl,fileNameWatch:this.fileName,fileListWatch:this.fileList,picUrl:"",file:void 0,speed:0,percentage:0,cosConfig:{bucketName:"",cosImgUrlPrefix:"",region:""},cosInstance:void 0,ossObj:void 0}},watch:{fileUrl:{handler(t){this.fileUrlWatch=t}},fileName:{handler(t){this.fileNameWatch=t}},fileList:{handler(t){this.fileListWatch=t},deep:!0},loading(t){this.$emit("loadingChange",t)}},computed:{acceptAuto(){let t=this.format.map(t=>"."+t).join(", ");return t||["image/*","amr/*","video/*"][this.type]}},created(){Object(U["c"])().then(t=>{this.cosConfig=t,"tencentOss"==this.cosConfig.fileObject?this.cosInstance=new P.a({SecretId:t.secretId,SecretKey:t.secretKey}):"local"==this.cosConfig.fileObject||"minio"==this.cosConfig.fileObject||(this.ossObj=new D.a({region:t.region.split(".")[0],accessKeyId:t.secretId,accessKeySecret:t.secretKey,bucket:t.bucketName}))})},mounted(){},methods:{upload(){"tencentOss"==this.cosConfig.fileObject?this.tencentFn():"local"==this.cosConfig.fileObject||"minio"==this.cosConfig.fileObject?this.localFn():"aliOss"==this.cosConfig.fileObject&&this.aliOss()},uploadSuccess(t,e){this.$emit("upSuccess",e),2==t.myType?Object(U["g"])({url:e}).then(t=>{this.loading=!1,this.$emit("getPicUrl",t.data.url,t.data)}):this.loading=!1;let i=window.URL.createObjectURL(t),s=t.name,a=t.size,o=t.myType;this.multiple?(this.fileListWatch=this.fileListWatch.concat({name:s,url:i,memorySize:a,myType:o}),this.$emit("update:fileList",this.fileList.concat({name:s,url:e,memorySize:a,myType:o}))):(this.fileUrlWatch=i,this.$emit("update:fileUrl",e),this.$emit("update:fileName",this.fileNameWatch=s),this.$emit("update:imgSize",a))},localFn(){this.loading=!0;let t=void 0;t=this.multiple&&1!=this.limit?this.file.shift():this.file;let e=new FormData;e.append("file",t),Object(U["m"])(e).then(e=>{if(200==e.code){let i=e.data.url;this.uploadSuccess(t,i)}else this.loading=!1,this.$toast.fail("上传失败，请稍后再试")})},async aliOss(){if(!this.ossObj)return void this.$toast.fail("存储空间正忙，请稍后再试");this.loading=!0;let t=void 0;t=this.multiple&&1!=this.limit?this.file.shift():this.file;let e=new Date,i=t.name.match(/\.(\w+)$/g);i=i&&i[0];let s=`${Object(F["b"])(e,"yyyy-MM-dd")}/t${e.getTime()}-${Object(F["i"])()}${i}`;try{const e=await this.ossObj.multipartUpload(s,t,{progress:t=>{t>0&&(this.percentage=100*t.toFixed(2))}});if(e){this.percentage=this.speed=0;let i=this.cosConfig.cosImgUrlPrefix+e.name;this.uploadSuccess(t,i)}}catch(a){this.loading=!1,this.$toast.fail("上传失败，请稍后再试")}},tencentFn(){if(!this.cosInstance)return void this.$toast.fail("存储空间正忙，请稍后再试");this.loading=!0;let t=void 0;t=this.multiple&&1!=this.limit?this.file.shift():this.file;let e=new Date,i=t.name.match(/\.(\w+)$/g);i=i&&i[0];const s={Bucket:this.cosConfig.bucketName,Region:this.cosConfig.region,Key:`/${Object(F["b"])(e,"yyyy-MM-dd")}/t${e.getTime()}-${Object(F["i"])()}${i}`};this.cosInstance.uploadFile({...s,Body:t,onProgress:t=>{t.percent>0&&(this.percentage=100*t.percent.toFixed(2)),t.speed>0&&(this.speed=(t.speed/1024/1024).toFixed(2))}},(e,i)=>{if(this.percentage=this.speed=0,e)this.loading=!1,this.$toast.fail("上传失败，请稍后再试");else{let e="https://"+i.Location;this.uploadSuccess(t,e)}})},remove(t){this.$dialog.confirm({title:"提示",message:"确定要删除吗?"}).then(()=>{let e=JSON.parse(JSON.stringify(this.fileList));this.fileListWatch.splice(t,1),e.splice(t,1),this.$emit("update:fileList",e)})},handleExceed(t,e){this.$toast.fail("最多上传"+this.limit+"张"),this.loading=!1},getFileType(t){if(this.type)return this.type;let e=3,i=t.split("."),s=i.length,a=s>1?i[s-1].toLowerCase():"unknow";return["png","jpg","jpeg"].includes(a)?e=0:["amr"].includes(a)?e=1:["mp4"].includes(a)?e=2:["xls","xlsx","doc","docx","pdf","ppt","pptx","pps","pptsx"].includes(a)&&(e=3),e},async handleBeforeUpload(t,e){this.loading=!0;let i=!0,s=!0;t.myType=this.getFileType(t.name);let a=this.format,o="";if(this.type&&(!a||!a.length)){let t={0:{tip:"png/jpg",value:["png","jpg","jpeg"]},1:{value:["amr"]},2:{value:["mp4"]},3:{tip:"word/pdf/ppt",value:["doc","docx","pdf","ppt","pptx","pps","pptsx"]}};a=t[this.type].value,o=t[this.type].tip}const n=new RegExp(`\\.(${a.join("|")})$`,"ig");if(i="*"===a[0]||n.test(t.name),!i)return this.$toast.fail("文件格式错误，仅支持 "+(o||a.join("，"))+" 格式!"),this.loading=!1,Promise.reject();let l=this.maxSize;if(!l){let e={0:2,1:2,2:100,3:50};l||(l=e[t.myType])}if(s=t.size/1024/1024<l,!s)return this.$toast.fail("上传文件大小不能超过 "+l+"MB!"),this.loading=!1,Promise.reject();let r=!0;if(0==t.myType){let e=this.maxImgPx;if(e)try{await new Promise(i=>{let s,a,o=new Image;if(o.onload=()=>{s=o.width,a=o.height,s>e[0]?(r=!1,this.$toast.fail(`图片“宽”度超过${e[0]}像素，请重新选择`)):a>e[1]&&(this.$toast.fail(`图片“高”度超过${e[1]}像素，请重新选择`),r=!1),window.URL&&window.URL.revokeObjectURL(o.src),i()},window.URL){let e=window.URL.createObjectURL(t);o.src=e}else if(window.FileReader){let e=new FileReader;e.onload=function(t){let e=t.target.result;o.src=e},e.readAsDataURL(t)}})}catch(c){console.error(c)}}else if(2==t.myType){let e=await this.checkVideoCode(t);if(-1===e.mime.indexOf("video/mp4"))return this.$toast.fail("mp4 格式不正确, 请使用标准编码视频!"),this.loading=!1,Promise.reject()}else t.myType;return r?this.multiple&&1!=this.limit?(Array.isArray(this.file)||(this.file=[]),this.file.push(t)):this.file=t:this.loading=!1,r||Promise.reject()},onError(t,e,i){this.loading=!1,this.$toast.fail("上传文件失败")},checkVideoCode(t){return new Promise((e,i)=>{const s=E.a.createFile(),a=new FileReader;a.readAsArrayBuffer(t),a.onload=function(t){const e=t.target.result;e.fileStart=0,s.appendBuffer(e)},s.onReady=function(t){e(t)},s.onError=function(t){i(t)}})},showView(t){Object(z["a"])({images:[this.fileUrlWatch],startPosition:t})}}},R=q,B=(i("7d58"),Object(p["a"])(R,L,N,!1,null,"d8d820b0",null)),J=B.exports,V=function(){var t=this,e=t._self._c;return e("van-popup",t._b({staticStyle:{width:"95%",height:"100%"},attrs:{position:"right","close-on-click-overlay":!1}},"van-popup",t.$attrs,!1),[e("div",{staticClass:"content"},[e("div",{staticClass:""},[e("div",{staticClass:"title"},[e("span",[t._v("选择成员")])]),e("van-search",{attrs:{placeholder:"请输入搜索关键词",clearable:""},on:{input:t.onSearch},model:{value:t.query.userName,callback:function(e){t.$set(t.query,"userName",e)},expression:"query.userName"}}),t.list.length?e("van-list",{attrs:{finished:t.finished,"finished-text":"已经到底啦"},on:{load:t.onLoad},model:{value:t.loading,callback:function(e){t.loading=e},expression:"loading"}},t._l(t.list,(function(i,s){return e("div",{key:s,staticClass:"cell-item",class:t.selectList.some(t=>t.userId==i.userId)?"cell-item-active":"",on:{click:function(e){return t.cellClick(i)}}},[e("div",{staticClass:"cell-item-left"},[e("div",{staticClass:"user-name"},[e("span",[t._v(t._s(i.userName))])])]),e("van-icon",{directives:[{name:"show",rawName:"v-show",value:t.selectList.some(t=>t.userId==i.userId),expression:"selectList.some((e) => e.userId == item.userId)"}],staticClass:"check-outlined-icon",attrs:{name:"success"}})],1)})),0):e("Empty")],1),e("div",{staticClass:"footer flex",staticStyle:{margin:"20px"}},[e("van-button",{attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{"native-type":"button",round:"",block:"",plain:""},on:{click:t.reset}},[t._v("重置")]),e("van-button",{staticClass:"ml10",attrs:{type:"info","native-type":"submit",round:"",block:""},on:{click:t.submit}},[t._v("提交")])],1)])])},H=[],K={components:{},data(){return{loading:!1,finished:!1,query:{pageSize:10,pageNum:1,userName:""},total:0,list:[],selectList:[]}},created(){this.getUserList()},methods:{onSearch(){this.query.pageNum=1,this.list=[],this.total=0,this.loading=!0,this.finished=!1,this.getUserList()},onLoad(){this.total>this.list.length?(this.query.pageNum++,this.getUserList()):this.finished=!0},getUserList(){this.loading=!0,Object(U["d"])(this.query).then(({data:t,total:e})=>{this.loading=!1,this.total=+e,this.list.push(...t)})},cancel(){this.$emit("input",!1)},reset(){this.selectList=[]},submit(){this.$emit("change",JSON.parse(JSON.stringify(this.selectList))),this.reset(),this.cancel()},cellClick(t){let e=this.selectList.findIndex(e=>e.userId==t.userId);e>-1?this.selectList.splice(e,1):this.selectList.push(t)}}},G=K,Q=(i("696e"),Object(p["a"])(G,V,H,!1,null,"7724c1cf",null)),X=Q.exports;const Y={cooperateTime:Object(F["b"])(new Date,"yyyy/MM/dd hh:mm"),_cooperateTime:new Date,attachmentList:[],followMode:null,recordContent:"请输入",cooperateUsers:[]};var Z={components:{Upload:J,SelectStaff:X},props:{leadId:"",type:{type:String,default:"add"}},data(){return{dateFormat:F["b"],loading:!1,fileList:[],clueStatusEnums:[],typeObj:{},typeEnums:[],consultEnum:[],isOpen:!1,loading:!1,followForm:JSON.parse(JSON.stringify(Y)),followRules:{followMode:[{required:!0,message:"",trigger:"blur"}],recordContent:[{required:!0,message:"",trigger:"blur"}],consult:[{required:!0,message:"请选择项目",trigger:"blur"}],pendingStatusCode:[{required:!0,message:"请选择线索状态",trigger:"blur"}]},pendingStatusCode:null,minDate:new Date,showAppointmentTime:!1,showMode:!1,showTip:!1,showDateTime:!1,showConsult:!1,showWxstatus:!1,showNearbyArea:!1,showLeadsStatus:!1,consult:[],leadsStatus:[],weixinStatusEnum:{0:"验证中",1:"不存在",2:"加微信已通过"},wxStatus:[{label:"验证中",value:0},{label:"不存在",value:1},{label:"加微信已通过",value:2}],nearbyArea:[],nearbyAreaOld:[],allNearbyArea:[],searchName:"",primaryArea:"",secondAreaList:[],secondArea:"",thirdAreaList:[],thirdArea:"",leadsId:null,format:["png","jpg","jpeg","mp4","mov","doc","docx","pdf","ppt","pptx","pps","pptsx","xls","xlsx"],maxSizeDefault:{0:20,1:2,2:100,3:50},maxImgPx:[1440,1080],show:{}}},watch:{},computed:{},created(){},mounted(){this.getFollowMode()},methods:{getFollowMode(){Object(c["e"])().then(t=>{const e=t.data,i=[];for(const s in e)if(Object.hasOwnProperty.call(e,s)){const t={label:s,value:e[s]};i.push(t)}this.typeEnums=i})},modeChange(t){this.followForm.followMode=t.value,this.followForm.followModeName=t.label,this.show.mode=!1},contentedit(t){this.followForm.recordContent=this.$refs.contenteditable.innerHTML},selectStaff(t){let e=window.getSelection(),i=e.anchorNode,s="";if(t.forEach(t=>{s+=`&nbsp;<span contenteditable="false" class="at-user ${t.weUserId}">@${t.userName}</span>&nbsp;`}),i&&i.textContent){let t=i.textContent.slice(0,e.anchorOffset)+s+i.textContent.slice(e.anchorOffset);if(3==i.nodeType)if(i.parentElement.innerHTML===i.textContent)i.parentElement.innerHTML=t;else{let e=document.createElement("span");e.innerHTML=t,i.replaceWith(e)}}else this.$toast("请先选中 记录内容 输入框");t.forEach(t=>{this.followForm.cooperateUsers.some(e=>e.weUserId==t.weUserId)||this.followForm.cooperateUsers.push(t)})},dateTimConfirm(t){this.followForm.cooperateTime=Object(F["b"])(t,"yyyy/MM/dd hh:mm"),this.show.dateTime=!1},showDialog(t){let e={user:{title:"添加协作",message:"添加协作成员可通知其他同事帮助自己跟进当前线索，例如请求对方提供相关资料、协同参会等。"},datetime:{title:"协作日期",message:"协作日期可指定某一天与该线索客户的约定事项进行跟进，例如本次跟进约定本周三进行见面约谈或者线上会议。\n如果添加了协作成员也会提醒协作成员协助参与"},upload:{title:"附件要求",message:"仅支持上传视频、图片与文件，共不超过10个。\n①视频仅支持mp4、mov；单个限制100M\n②图片仅支持jpg、png、jpeg；单个限制20M\n③文件仅支持pdf、ppt、word；单个限制50M"}};this.$dialog.alert({title:e[t].title,message:e[t].message,confirmButtonText:"我知道了",messageAlign:"left",theme:"round-button"})},cancel(){this.$emit("input",!1)},onSubmit(){const t=this.$toast.loading();this.followForm.recordContent=this.$refs.contenteditable.innerHTML;const e=JSON.parse(JSON.stringify(this.followForm));let i=this.$refs.contenteditable.getElementsByClassName("at-user"),s=Array.from(i).map(t=>t.classList[1]);e.cooperateUsers=e.cooperateUsers.filter(t=>s.includes(t.weUserId)),e.weLeadsId=this.leadId,e.attachmentList=e.attachmentList.map(t=>({attachmentType:t.myType,attachmentName:t.name,attachmentAddress:t.url})),Object(c["b"])(e).then(t=>{this.loading=!1,this.$toast.success("操作成功"),this.$emit("success"),this.cancel(),this.followForm=JSON.parse(JSON.stringify(Y))}).finally(()=>{t.clear()})}}},tt=Z,et=(i("711e"),Object(p["a"])(tt,S,$,!1,null,"361f61aa",null)),it=et.exports,st={components:{TagEllipsis:o["a"],Record:n["a"],back:f,edit:k,transfer:j,followup:it},props:{},data(){return{isPreview:!1,previewUrl:[],previewType:0,isNew:!1,isMy:!1,userId:sessionStorage.getItem("sysId"),leadsId:void 0,customInfo:{},beforeFollowUp:[],showBack:!1,showEdit:!1,showTransfer:!1,showFollowup:!1}},computed:{tags(){let t=[];return this.customInfo.labelsNames&&(t=this.customInfo.labelsNames.split(",")),t}},watch:{},created(){this.leadsId=this.$route.query.id},async mounted(){this.updateInfo()},methods:{showPopup(){this.$dialog.alert({title:"线索转化",message:"转化为客户则代表该线索已成为企业的有效客户资源，需选择同一手机号的微信好友进行绑定关联进行后续跟进。如未添加该线索为好友，请尽快添加。",theme:"round-button",confirmButtonText:"我知道了"})},previewFile(t,e){this.previewUrl=[t],this.previewType=e,this.isPreview=!0},async updateInfo(){await this.getLeadsDeatail(),this.getFollowUpList()},async getLeadsDeatail(){const t=this.$toast.loading();await Object(c["i"])(this.leadsId).then(({data:e})=>{"暂无权限查看该线索"===e?this.customInfo={}:(this.customInfo=e||{},this.editForm=e,this.isNew=0===e.leadsStatus,t.clear())}).catch(e=>{t.clear()})},getFollowUpList(){Object(c["h"])(this.leadsId).then(({data:t})=>{t&&t[0]&&(this.isMy=this.userId==t[0].followerId&&1==t[0].followerStatus,this.isMy?this.beforeFollowUp=t.slice(1):this.beforeFollowUp=t)})},phoneCall(t){t&&(window.location.href="tel:"+t)},handleCopy(t){const e=document.createElement("input");e.style.cssText="opacity: 0;",e.type="text",e.value=t,document.body.appendChild(e),e.select(),document.execCommand("copy"),this.$toast.success("微信号已复制到剪切板")},addFriend(t){t&&(this.handleCopy(t),setTimeout(()=>{wx.invoke("navigateToAddCustomer",{},(function(t){console.log(t)}))},500)),wx.invoke("navigateToAddCustomer",{},(function(t){console.log(t)}))},collectClue(){this.$dialog.confirm({title:"领取线索",message:"是否确认领取该线索客户进行跟进？"}).then(()=>(this.$toast.loading(),Object(c["m"])(this.leadsId))).then(t=>{this.$toast.success("操作成功"),this.updateInfo()}).finally(()=>{this.$toast.clear()})}}},at=st,ot=(i("b7ff"),Object(p["a"])(at,s,a,!1,null,"e3471bd6",null));e["default"]=ot.exports},"8d12":function(t,e,i){"use strict";i("93fc")},"93fc":function(t,e,i){},a823:function(t,e,i){},a8ce1:function(t,e,i){},b160:function(t,e,i){},b7ff:function(t,e,i){"use strict";i("4642")},c165:function(t,e,i){},c2eb:function(t,e,i){"use strict";i.d(e,"g",(function(){return n})),i.d(e,"k",(function(){return l})),i.d(e,"f",(function(){return r})),i.d(e,"l",(function(){return c})),i.d(e,"d",(function(){return u})),i.d(e,"c",(function(){return d})),i.d(e,"e",(function(){return p})),i.d(e,"a",(function(){return m})),i.d(e,"b",(function(){return f})),i.d(e,"i",(function(){return h})),i.d(e,"h",(function(){return g})),i.d(e,"j",(function(){return v}));var s=i("b775");const a=window.sysConfig.services.wecom,o=a+"/portrait";function n(t){return Object(s["a"])({url:o+"/findWeCustomerInfo",params:t})}function l(t){return Object(s["a"])({url:o+"/updateWeCustomerInfo",method:"post",data:t})}function r(t){return Object(s["a"])({url:o+"/findAllTags",params:t})}function c(t){return Object(s["a"])({url:o+"/updateWeCustomerPorTraitTag",method:"post",data:t})}function u(t){return Object(s["a"])({url:o+"/findAddaddEmployes/"+t})}function d(t){return Object(s["a"])({url:o+"/findAddGroupNum",params:t})}function p(t){return Object(s["a"])({url:a+"/trajectory/findTrajectory",params:t})}function m(t){return Object(s["a"])({url:o+"/addOrEditWaitHandle",method:"post",data:t})}function f(t){return Object(s["a"])({url:o+"/addOrUpdatePersonTags",method:"post",data:t})}function h(t){return Object(s["a"])({url:o+"/synchMomentsInteracte/"+t,method:"get"})}function g(t){return Object(s["a"])({url:o+"/findSysFieldTemplate",params:t})}function v(t){return Object(s["a"])({url:o+"/updateRemarkCorpInfo",method:"post",data:t})}},f59b:function(t,e,i){"use strict";i("c165")}}]);