<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WePresTagGroupTaskMapper">


    <select id="findWePresTagGroupTasks" resultType="org.scrm.domain.taggroup.WePresTagGroupTask">
        select * from we_pres_tag_group ${ew.customSqlSegment}
    </select>

    <select id="countTab" resultType="org.scrm.domain.taggroup.vo.WePresTagGroupTaskTabCountVo">
        SELECT
            (
                SELECT
                    count(*)
                FROM
                    we_pres_tag_group_stat where sent=1 and task_id=#{task.id}
            ) as touchWeCustomerNumber,
            (
                SELECT
                    count(*)
                FROM we_group_member where state=#{task.groupCodeState}

            ) as joinGroupCustomerNumber,
            (
                SELECT
                    count(*)
                FROM
                    we_pres_tag_group_stat where sent=1 and date_format(update_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d') and task_id=#{task.id}
            ) as tdTouchWeCustomerNumber,
            (
                SELECT
                    count(*)
                FROM we_group_member WHERE date_format(join_time,'%Y-%m-%d') = date_format(curdate(),'%Y-%m-%d') and state=#{task.groupCodeState}
            ) as tdJoinGroupCustomerNumber
    </select>


    <select id="findTrendCountVo" resultType="org.scrm.domain.taggroup.vo.WePresTagGroupTaskTrendCountVo">
        SELECT
            sdd.date,
            (
                SELECT
                    count(*)
                FROM
                    we_pres_tag_group_stat ws
                WHERE
                        DATE_FORMAT( ws.update_time, '%Y-%m-%d' )= DATE_FORMAT( sdd.date, '%Y-%m-%d' )
               and ws.sent=1 and  ws.task_id=#{task.id}
            ) AS touchWeCustomerNumber,
            (
                SELECT
                    count(*)
                FROM
                    we_group_member wgm
                WHERE
                        DATE_FORMAT( wgm.join_time, '%Y-%m-%d' )= DATE_FORMAT( sdd.date, '%Y-%m-%d' )
                  AND wgm.state = #{task.groupCodeState}
                ) AS joinGroupCustomerNumber
        FROM
            sys_dim_date sdd
        WHERE
            DATE_FORMAT( sdd.date, '%Y-%m-%d' ) BETWEEN #{task.beginTime} AND #{task.endTime}
    </select>

    <select id="findWePresTagGroupTaskTable" resultType="org.scrm.domain.taggroup.vo.WePresTagGroupTaskTableVo">
        SELECT
            wc.avatar,
            wc.customer_name,
            (SELECT GROUP_CONCAT(su.user_name) FROM sys_user su where su.we_user_id=wc.add_user_id) as user_name,
            wc.customer_type,
            wc.external_userid AS externalUserid,
            wc.add_user_id,
            wc.add_time,
            wc.gender,
            (SELECT wgm.join_time from we_group_member wgm WHERE wc.external_userid = wgm.user_id
            <if test="query.chatId !=null and query.chatId !=''">
               and wgm.chat_id in
                <foreach collection="query.chatId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and wgm.quit_time IS NULL ORDER BY  wgm.join_time ASC  limit 1 ) as joinTime,
			(SELECT count(*) from we_group_member wgm WHERE wc.external_userid = wgm.user_id
            <if test="query.chatId !=null and query.chatId !=''">
                and wgm.chat_id in
                <foreach collection="query.chatId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
             and wgm.quit_time IS NULL) as joinGroupNumber,
			 IF((SELECT count(*) from we_group_member wgm WHERE wc.external_userid = wgm.user_id
            <if test="query.chatId !=null and query.chatId !=''">
                and wgm.chat_id in
                <foreach collection="query.chatId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            and wgm.quit_time IS NULL)>0,1,0) as isJoinGroup
        FROM
            we_pres_tag_group_stat wptgs
            LEFT JOIN we_customer wc ON wc.external_userid = wptgs.external_userid AND wc.add_user_id = wptgs.user_id
        where
            wptgs.sent=1
            <if test="query.id != null and query.id !=''">
                and wptgs.task_id = #{query.id}
            </if>
            <if test="query.customerName != null and query.customerName !='' ">
                and  wc.customer_name  LIKE CONCAT('%',#{query.customerName,jdbcType=VARCHAR},'%')
            </if>

            <if test="query.startAddTime != null and query.startAddTime !='' and query.endAddTime != null and query.endAddTime !='' ">
                AND  date_format(wc.add_time,'%Y-%m-%d') BETWEEN #{query.startAddTime} AND #{query.endAddTime}
            </if>
            <choose>
                <when test="query.isJoinGroup != null">
                    <if test="query.isJoinGroup ==1 ">
                        HAVING(joinGroupNumber)>0
                    </if>
                    <if test="query.isJoinGroup ==0 ">
                        HAVING(joinGroupNumber) &lt;= 0
                    </if>
                </when>
            </choose>
            <if test="pageDomain !=null">
                <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                    limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
                </if>
            </if>
    </select>


    <select id="countWePresTagGroupTaskTable" resultType="long">
        SELECT
        COUNT(*)
        FROM (
            SELECT
                wc.external_userid AS externalUserid,
                (SELECT count(*) from we_group_member wgm WHERE wc.external_userid = wgm.user_id
                <if test="query.chatId !=null and query.chatId !=''">
                    and wgm.chat_id in
                    <foreach collection="query.chatId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                and wgm.quit_time IS NULL) as joinGroupNumber
            FROM
            we_pres_tag_group_stat wptgs
            LEFT JOIN we_customer wc ON wc.external_userid = wptgs.external_userid AND wc.add_user_id = wptgs.user_id
            where
            wptgs.sent=1
            <if test="query.id != null and query.id !=''">
                and wptgs.task_id = #{query.id}
            </if>
            <if test="query.customerName != null and query.customerName !='' ">
                and  wc.customer_name  LIKE CONCAT('%',#{query.customerName,jdbcType=VARCHAR},'%')
            </if>

            <if test="query.startAddTime != null and query.startAddTime !='' and query.endAddTime != null and query.endAddTime !='' ">
                AND  date_format(wc.add_time,'%Y-%m-%d') BETWEEN #{query.startAddTime} AND #{query.endAddTime}
            </if>
            <choose>
                <when test="query.isJoinGroup != null">
                    <if test="query.isJoinGroup ==1 ">
                        HAVING(joinGroupNumber)>0
                    </if>
                    <if test="query.isJoinGroup ==0 ">
                        HAVING(joinGroupNumber) &lt;= 0
                    </if>
                </when>
            </choose>
        ) AS subquery
    </select>


</mapper>