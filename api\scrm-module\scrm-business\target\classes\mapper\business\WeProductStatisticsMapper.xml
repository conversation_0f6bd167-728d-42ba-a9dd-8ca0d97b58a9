<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeProductStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.WeProductStatistics">
        <id column="id" property="id"/>
        <result column="order_total_num" property="orderTotalNum"/>
        <result column="order_total_fee" property="orderTotalFee"/>
        <result column="refund_total_fee" property="refundTotalFee"/>
        <result column="net_income" property="netIncome"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_total_num, order_total_fee, refund_total_fee, net_income, create_time, update_time, del_flag
    </sql>

</mapper>
