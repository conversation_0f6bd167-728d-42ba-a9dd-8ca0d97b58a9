<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeProductDayStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.WeProductDayStatistics">
        <id column="id" property="id" />
        <result column="day_order_total_num" property="dayOrderTotalNum" />
        <result column="day_order_total_fee" property="dayOrderTotalFee" />
        <result column="day_refund_total_fee" property="dayRefundTotalFee" />
        <result column="day_net_income" property="dayNetIncome" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="create_by_id" property="createById" />
        <result column="update_time" property="updateTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_id" property="updateById" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, day_order_total_num, day_order_total_fee, day_refund_total_fee, day_net_income, create_time, create_by, create_by_id, update_time, update_by, update_by_id, del_flag
    </sql>

</mapper>
