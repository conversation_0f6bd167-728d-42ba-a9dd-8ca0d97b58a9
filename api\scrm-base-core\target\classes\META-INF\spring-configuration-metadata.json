{"groups": [{"name": "scrm", "type": "org.scrm.base.config.ScrmConfig", "sourceType": "org.scrm.base.config.ScrmConfig"}], "properties": [{"name": "scrm.address-enabled", "type": "java.lang.Bo<PERSON>an", "description": "获取地址开关", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": false}, {"name": "scrm.anon-url", "type": "java.lang.String[]", "description": "匿名访问的URL", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.app-msg-url", "type": "java.lang.String", "description": "短链推广-应用消息页面", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.authorize-url", "type": "java.lang.String", "description": "JS SDK 身份校验url", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": "https://open.weixin.qq.com/connect/oauth2/authorize"}, {"name": "scrm.baidu-maps-ak", "type": "java.lang.String", "description": "百度地图开发者ak", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.community-new-group-url", "type": "java.lang.String", "description": "新客拉群h5地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.customer-short-link-domain-name", "type": "java.lang.String", "description": "获客助手短链", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.customer-sop-redirect-url", "type": "java.lang.String", "description": "客户sop h5跳转地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.data-synch-interval", "type": "java.lang.Integer", "description": "数据同步使劲啊间隔", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": 5}, {"name": "scrm.default-pwd", "type": "java.lang.String", "description": "系统默认密码", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.file", "type": "org.scrm.base.config.FileConfig", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.file-view-url", "type": "java.lang.String", "description": "文件预览服务地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.fincace-proxy-config", "type": "org.scrm.base.config.FincaceProxyConfig", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.fission-url", "type": "java.lang.String", "description": "裂变url", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.group-code-url", "type": "java.lang.String", "description": "客户访问地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.group-sop-redirect-url", "type": "java.lang.String", "description": "客群sop h5跳转地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.guide-code-url", "type": "java.lang.String", "description": "导购码", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.guide-group-url", "type": "java.lang.String", "description": "导购群码", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.h5-domain", "type": "java.lang.String", "description": "移动端域名", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.key-word-group-url", "type": "java.lang.String", "description": "关键词群h5链接", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.know-customer-url", "type": "java.lang.String", "description": "识客码", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.leads-covenant-wait-follow-up-url", "type": "java.lang.String", "description": "线索中心移动端-待办任务-线索约定事项待跟进-详情页", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.leads-detail-url", "type": "java.lang.String", "description": "线索中心移动端-待办任务-线索长时间未跟进-详情页", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.live-url", "type": "java.lang.String", "description": "直播页面h5链接", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.lost-customer-redirect-url", "type": "java.lang.String", "description": "流失客户地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.lx-qr-code-url", "type": "java.lang.String", "description": "拉新活码H5地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.lxqr-config", "type": "org.scrm.base.config.LxqrConfig", "description": "拉新红包相关配置", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.material-detail-url", "type": "java.lang.String", "description": "素材中心素材详情", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.moments-url", "type": "java.lang.String", "description": "朋友圈移动端列表页", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.qi-rule-config", "type": "org.scrm.base.config.QiRuleConfig", "description": "会话质检相关配置", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.qr-group-short-link-domain-name", "type": "java.lang.String", "description": "群活码短链域名", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.qr-short-link-domain-name", "type": "java.lang.String", "description": "活码短链域名", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.seas-redirect-url", "type": "java.lang.String", "description": "客户公海访问地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.service-provider-id", "type": "java.lang.String", "description": "服务商id", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.short-applet-url", "type": "java.lang.String", "description": "短链小程序地址", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": "pages/index/index"}, {"name": "scrm.short-domain", "type": "java.lang.String", "description": "短域名", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.short-domain-protocol", "type": "java.lang.String", "description": "端域名所使用的协议(http或https)", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": "https"}, {"name": "scrm.short-envversion", "type": "java.lang.String", "description": "微信短链生成的环境:正式版为\"release\"，体验版为\"trial\"，开发版为\"develop\"", "sourceType": "org.scrm.base.config.ScrmConfig", "defaultValue": "develop"}, {"name": "scrm.short-link-domain-name", "type": "java.lang.String", "description": "短链域名", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.tag-redirect-url", "type": "java.lang.String", "description": "老客标签建群地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.tx-ai-region", "type": "java.lang.String", "description": "混元大模型地区", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.tx-ai-secret-id", "type": "java.lang.String", "description": "混元大模型秘钥ID", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.tx-ai-secret-key", "type": "java.lang.String", "description": "混元大模型秘钥", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.we-come-proxy-config", "type": "org.scrm.base.config.WeComeProxyConfig", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.we-market-url", "type": "java.lang.String", "description": "抽奖活动，h5跳转地址", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.wecome-login-url", "type": "java.lang.String", "description": "扫码登陆授权url", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.wx-auth-url", "type": "java.util.List<java.lang.String>", "description": "auth服务中授权微信端相关的授权的接口", "sourceType": "org.scrm.base.config.ScrmConfig"}, {"name": "scrm.wx-chat-robot-config", "type": "org.scrm.base.config.WxChatRobotConfig", "sourceType": "org.scrm.base.config.ScrmConfig"}], "hints": []}