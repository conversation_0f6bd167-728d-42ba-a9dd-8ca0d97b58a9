(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d84a538"],{"4bd2":function(t,s,i){"use strict";i("5d60")},"5d60":function(t,s,i){},dee6:function(t,s,i){"use strict";i.r(s);var a=function(){var t=this,s=t._self._c;return s("div",{staticClass:"apps"},[s("div",{staticClass:"apps-bg"}),s("div",{staticClass:"apps-content"},[s("div",{staticClass:"apps-content-item"},[s("div",{staticClass:"item-bg"}),s("div",{staticClass:"item-cotent"},[t._m(0),s("div",{staticClass:"item-cotent-bottom"},[s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("clueHighseas")}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"xsgh"}}),s("span",[t._v("线索公海")])],1),s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("lostCustomers")}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"lskh"}}),s("span",[t._v("流失客户")])],1),s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("personalSOPDetails")}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"csop"}}),s("span",[t._v("客户SOP")])],1)])])]),s("div",{staticClass:"apps-content-item"},[s("div",{staticClass:"item-bg"}),s("div",{staticClass:"item-cotent"},[t._m(1),s("div",{staticClass:"item-cotent-bottom"},[s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("customerBaseDetails",2)}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"gsop"}}),s("span",[t._v("客群SOP")])],1)])])]),s("div",{staticClass:"apps-content-item"},[s("div",{staticClass:"item-bg"}),s("div",{staticClass:"item-cotent"},[t._m(2),s("div",{staticClass:"item-cotent-bottom"},[s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("quality")}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"quality"}}),s("span",[t._v("会话质检")])],1),s("div",{staticClass:"bottom-item",on:{click:function(s){return t.goRouter("friends")}}},[s("svg-icon",{staticClass:"xsgh-icon",attrs:{name:"friends"}}),s("span",[t._v("朋友圈营销")])],1)])])])])])},n=[function(){var t=this,s=t._self._c;return s("div",{staticClass:"item-cotent-top"},[s("span"),s("span",[t._v("客户管理")])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"item-cotent-top"},[s("span"),s("span",[t._v("社群运营")])])},function(){var t=this,s=t._self._c;return s("div",{staticClass:"item-cotent-top"},[s("span"),s("span",[t._v("客情维系")])])}],o=(i("14d9"),{name:"",components:{},data(){return{}},computed:{},watch:{},created(){},mounted(){},methods:{goRouter(t){this.$router.push({name:t})},goRouterTwo(t,s){this.$router.push({path:t,query:{type:s}})}}}),c=o,e=(i("4bd2"),i("2877")),r=Object(e["a"])(c,a,n,!1,null,"3d151462",null);s["default"]=r.exports}}]);