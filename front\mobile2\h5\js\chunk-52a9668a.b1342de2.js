(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-52a9668a"],{"0e7f":function(t,e,n){"use strict";n("503a")},"4e79":function(t,e,n){"use strict";n.d(e,"c",(function(){return o})),n.d(e,"d",(function(){return a})),n.d(e,"e",(function(){return r})),n.d(e,"b",(function(){return c})),n.d(e,"a",(function(){return d}));var s=n("b775");const i=window.sysConfig.services.weChat;function o(t){return Object(s["a"])({url:i+"/groupCode/getActualCode/"+t})}function a(t){return Object(s["a"])({url:i+"/groupCode/findWeCommunityNewGroupById/"+t})}function r(t){return Object(s["a"])({url:i+"/groupCode/findWePresTagGroupById/"+t})}function c(t){return Object(s["a"])({url:i+"/storeCode/findStoreCode",params:t})}function d(t){return Object(s["a"])({url:i+"/storeCode/findWeStoreCodeConfig",params:t})}},"503a":function(t,e,n){},9651:function(t,e,n){t.exports=n.p+"img/no_code.821c4c8d.svg"},c99a:function(t,e,n){"use strict";n.r(e);var s=function(){var t,e=this,s=e._self._c;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"bg"},[s("van-field",{attrs:{readonly:"",label:"",placeholder:"请选择所在地区"},on:{click:function(t){e.addressShow=!0}},model:{value:e.form.area,callback:function(t){e.$set(e.form,"area",t)},expression:"form.area"}}),s("van-field",{attrs:{readonly:"",clickable:"",value:e.currentItem.storeName,placeholder:"请选择门店"},on:{click:function(t){e.showPicker=!0}}}),null!==(t=e.columns)&&void 0!==t&&t.length?[s("div",{staticClass:"tip",staticStyle:{"text-align":"center"}},[s("van-icon",{attrs:{name:"location-o"}}),e._v(" "+e._s(e.currentItem.address)+" ")],1),s("div",{staticClass:"code_content"},[e.currentItem.shopGuideUrl?s("img",{staticClass:"code_img",attrs:{src:e.currentItem.shopGuideUrl,alt:""},on:{touchstart:e.setStart,touchend:e.setEnd}}):s("img",{staticClass:"code_img",attrs:{src:n("9651"),alt:""}})]),e.currentItem.shopGuideUrl?s("div",{staticClass:"sub-des"},[e._v(" 长按识别二维码添加门店导购 ")]):e._e()]:[s("div",{staticClass:"tip"},[e._v(" "+e._s(e.data.outOfRangeTip)+" ")]),s("div",{staticClass:"code_content"},[1===e.data.codeState?s("img",{staticClass:"code_img",attrs:{src:n("9651"),alt:""}}):s("img",{staticClass:"code_img",attrs:{src:e.data.customerServiceUrl,alt:""},on:{touchstart:e.setStart,touchend:e.setEnd}})]),1!==e.data.codeState?s("div",{staticClass:"sub-des"},[e._v(" 长按识别二维码添加客服 ")]):e._e()],s("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.showPicker,callback:function(t){e.showPicker=t},expression:"showPicker"}},[s("van-picker",{attrs:{"value-key":"storeName","show-toolbar":"",columns:e.columns},on:{cancel:function(t){e.showPicker=!1},confirm:e.onConfirm}})],1),s("van-popup",{attrs:{round:"",position:"bottom"},model:{value:e.addressShow,callback:function(t){e.addressShow=t},expression:"addressShow"}},[s("van-cascader",{attrs:{title:"请选择所在地区",options:e.addressOptions,"field-names":{text:"name",value:"name"}},on:{close:function(t){e.addressShow=!1},finish:e.selectedArea},model:{value:e.area,callback:function(t){e.area=t},expression:"area"}})],1)],2)},i=[],o=(n("e9f5"),n("ab43"),n("ed08")),a=n("2934"),r=n("4e79"),c={name:"store-guide-code",data(){return{loading:!1,area:"",userInfo:{},tenantId:"",addressOptions:[],addressShow:!1,form:{area:"",latitude:"",longitude:""},showPicker:!1,columns:[],value:"",data:{outOfRangeTip:"",codeState:1,customerServiceUrl:""},currentItem:{storeName:"",address:""},showTip:!1,timer:"",query:{currentLat:"",currentLng:"",storeCodeId:"",unionid:"",source:1,tenantId:""}}},methods:{setStart(){this.timer=setTimeout(()=>{this.query.storeCodeId=this.currentItem.id,this.query.unionid=this.userInfo.unionId,this.query.tenantId=sessionStorage.getItem("tenantId")},500)},setEnd(){clearTimeout(this.timer)},selectedArea({selectedOptions:t}){this.addressShow=!1,delete this.form.latitude,delete this.form.longitude,this.form.area=t.map(t=>t.name).join(""),this.getStoreList()},getAreaList(){Object(a["b"])().then(t=>{this.addressOptions=t})},onConfirm(t,e){t&&(this.currentItem=t),this.showPicker=!1},getStoreList(t){this.loading=!0,Object(r["b"])({storeCodeType:1,unionid:this.userInfo.unionId,longitude:this.form.longitude,latitude:this.form.latitude,area:this.form.area,tenantId:sessionStorage.getItem("tenantId")}).then(e=>{if(200===e.code)if(this.columns=e.data.weStoreCodes||[],this.columns&&0!==this.columns.length)if(t){this.currentItem=this.columns[0],this.form.area=this.currentItem.area;let t=Object(o["g"])(this.currentItem.area);t[2]&&(this.area=[2])}else this.currentItem=this.columns[0];else this.currentItem={storeName:"",address:""},this.getNullFn();this.loading=!1})},getNullFn(){Object(r["a"])({storeCodeType:1,unionid:this.userInfo.unionId,longitude:this.form.longitude,latitude:this.form.latitude,area:this.form.area}).then(t=>{200===t.code&&t.data&&(this.data=t.data)})},back(){document.addEventListener("WeixinJSBridgeReady",(function(){WeixinJSBridge.call("closeWindow")}),!1),WeixinJSBridge.call("closeWindow")}},async mounted(){if(await Object(o["d"])(),Object(o["c"])("code")&&Object(o["c"])("state")){this.userInfo=JSON.parse(sessionStorage.getItem("userinfo")),this.getAreaList();let t=this;this.loading=!0,Object(a["k"])(window.location.href.split("#")[0]).then(e=>{if(200===e.code){let{timestamp:n,nonceStr:s,signature:i}=e.data;wx.config({beta:!0,appId:sessionStorage.getItem("weAppId"),timestamp:n,nonceStr:s,signature:i,jsApiList:["getLocation"],success:function(t){},fail:e=>{t.loading=!1,alert("config失败:"+JSON.stringify(e)),e.errMsg.indexOf("function not exist")>-1&&alert("版本过低请升级")}}),wx.ready((function(){wx.getLocation({type:"wgs84",success:function(e){t.form.latitude=e.latitude,t.form.longitude=e.longitude,t.query.currentLat=e.latitude,t.query.currentLng=e.longitude,t.getStoreList("init")},fail:function(e){console.log("获取定位位置信息失败",e),alert("获取定位位置信息失败"),t.loading=!1},cancel:function(e){t.back()}})}))}})}},created(){this.tenantId=this.$route.query.tenantId,sessionStorage.setItem("tenantId",this.tenantId)}},d=c,u=(n("0e7f"),n("2877")),l=Object(u["a"])(d,s,i,!1,null,"6995218a",null);e["default"]=l.exports}}]);