<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSvipGroupRecordMapper">

    <select id="findSvipGroupTrend" resultType="org.scrm.domain.svipgroup.vo.WeSvipGroupVo">
        SELECT
        date as xTime,
        (SELECT count(id) from we_svip_group_record where DATE_FORMAT(add_time,'%Y-%m-%d') &lt;= date
        AND del_flag=0
        <if test="groupRecord.svipGroupId != null">
            and svip_group_id=#{groupRecord.svipGroupId}
        </if>
        ) as addGroupTotalNumber
        FROM
        sys_dim_date
        <where>
            <if test="groupRecord.beginTime != null and groupRecord.beginTime !='' and groupRecord.endTime !=null and groupRecord.endTime != ''">
                DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{groupRecord.beginTime} and #{groupRecord.endTime}
            </if>
        </where>
        ORDER BY date ASC

    </select>


</mapper>
