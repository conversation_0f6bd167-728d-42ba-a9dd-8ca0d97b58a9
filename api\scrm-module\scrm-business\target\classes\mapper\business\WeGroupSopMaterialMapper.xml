<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupSopMaterialMapper">
    <insert id="batchBindsSopMaterial">
        insert into we_group_sop_material(id,rule_id, material_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},#{item.ruleId},#{item.materialId})
        </foreach>
    </insert>
</mapper>