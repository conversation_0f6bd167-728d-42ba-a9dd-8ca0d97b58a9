<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeProductMapper">



    <resultMap type="org.scrm.domain.WeProduct" id="WeProductResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="productId" column="product_id" jdbcType="VARCHAR"/>
        <result property="picture" column="picture" jdbcType="VARCHAR"/>
        <result property="describe" column="describe" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="VARCHAR"/>
        <result property="productSn" column="product_sn" jdbcType="VARCHAR"/>
        <result property="attachments" column="attachments" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="selectWeProductVo">
        select id,
               product_id,
               picture,
               `describe`,
               price,
               product_sn,
               attachments,
               create_time,
               create_by,
               create_by_id,
               update_time,
               update_by,
               update_by_id
        from we_product
    </sql>

    <select id="queryProductList" resultType="org.scrm.domain.product.product.vo.WeProductListVo">
        select wp.id,
        wp.product_id,
        wp.picture,
        wp.`describe`,
        wp.price,
        wp.product_sn,
        wp.attachments,
        wp.create_by,
        wp.update_time,
        ( SELECT count( 1 ) FROM we_product_order o WHERE o.product_id = wp.id ) AS order_num,
        COALESCE(( SELECT sum(total_fee) FROM we_product_order po WHERE po.product_id = wp.id ),0) AS order_total_amount
        from we_product wp
        <where>
            <if test="name != null and name != ''">
                and wp.`describe` like concat('%', #{name}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(wp.create_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND date_format(wp.create_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
            and wp.del_flag = 0
        </where>
        order by  wp.update_time desc
    </select>

    <select id="statistics" resultType="org.scrm.domain.product.product.vo.WeProductStatisticsVo">
        SELECT count(1)                                                          AS orderNum,
               COALESCE(sum(o.total_fee), 0)                                     AS orderFee,
               COALESCE((SELECT sum(r.refund_fee)
                         FROM we_product_order_refund r
                         WHERE r.order_no = o.order_no and refund_state = 2), 0) AS refundFee
        FROM we_product_order o
        WHERE o.product_id = #{productId}
          AND o.del_flag = 0
    </select>


</mapper>
