<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMomentsTaskRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.moments.entity.WeMomentsTaskRelation">
        <id column="id" property="id"/>
        <result column="moment_task_id" property="momentTaskId"/>
        <result column="job_id" property="jobId"/>
        <result column="job_id_expire" property="jobIdExpire"/>
        <result column="moment_id" property="momentId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, moment_task_id, job_id, job_id_expire, moment_id
    </sql>

</mapper>
