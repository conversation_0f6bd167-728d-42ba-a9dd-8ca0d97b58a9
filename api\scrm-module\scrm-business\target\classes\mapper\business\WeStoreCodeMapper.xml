<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeStoreCodeMapper">

    <select id="findStoreCode" resultType="org.scrm.domain.storecode.entity.WeStoreCode">
        SELECT
        a.*
        FROM
        (
        SELECT
        *
        <if test="currentLng != null and currentLng !='' and currentLat != null and currentLat !=''">
                   ,st_distance(POINT(longitude, latitude),POINT (#{currentLng} , #{currentLat}))* 111195/1000 AS distanc
         </if>
        FROM we_store_code where
        del_flag=0 and store_state=0
        <if test="area != null and area !=''">
            AND area = #{area}
        </if>
        ) a
        <if test="raidus != null and raidus !=''">
            where a.distanc&lt;=convert(#{raidus},DECIMAL)
        </if>
    </select>

    <!-- Batch update state -->
    <update id="batchUpdateState">
        UPDATE we_store_code
        <set>
            store_state = #{storeState}
        </set>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id, jdbcType=INTEGER}
        </foreach>
    </update>




</mapper>
