<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLeaveInfoMapper">

    <select id="findSysUserByWeUserId" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT
            su.*,
            sd.dept_name
        FROM
            sys_user su
                LEFT JOIN sys_dept sd on su.dept_id=sd.dept_id
        WHERE
            we_user_id = #{weUserId}
    </select>

    <select id="findNoAllocateCustomer" resultType="org.scrm.domain.customer.vo.WeCustomersVo">
        SELECT
          wlis.allocate_name as customerName,
          wlis.allocate_id as externalUserid
        FROM
            we_leave_info_sub wlis
        WHERE wlis.take_we_user_id IS NULL
         <if test="leaveInfoId != null">
             AND wlis.leave_info_id=#{leaveInfoId}
         </if>
    </select>

    <select id="findNoAllocateGroup" resultType="org.scrm.domain.groupchat.vo.LinkGroupChatListVo">
        SELECT
            wlis.allocate_name as groupName,
            wlis.allocate_id as chatId
        FROM
            we_leave_info_sub wlis
        WHERE wlis.take_we_user_id IS NULL
        <if test="leaveInfoId != null">
            AND wlis.leave_info_id=#{leaveInfoId}
        </if>
    </select>

    <select id="findAllocatedInfo" resultType="org.scrm.domain.leave.dto.WeLeaveInfoDto">
        SELECT
            wli.we_user_id,
            wlis.take_we_user_id,
            wlis.allocate_id,
            wlis.id as leaveInfoSubId
        FROM
            we_leave_info wli
        LEFT JOIN we_leave_info_sub wlis ON wli.id = wlis.leave_info_id
        where wli.allocate=0 and wlis.take_we_user_id IS NOT NULL and wli.type=1
    </select>

    <delete id="physicalDeleteByIds">
        DELETE FROM we_leave_info WHERE id in
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>




</mapper>
