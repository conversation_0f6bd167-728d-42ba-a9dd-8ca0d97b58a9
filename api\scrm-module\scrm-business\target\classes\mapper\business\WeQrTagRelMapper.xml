<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeQrTagRelMapper">



    <resultMap type="org.scrm.domain.qr.WeQrTagRel" id="WeQrTagRelResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
        <result property="tagId" column="tag_id" jdbcType="INTEGER"/>
        <result property="businessType" column="business_type" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="selectWeQrTagRelVo">
        select id, qr_id, tag_id, business_type,  create_by, create_time, update_by, update_time, del_flag from we_qr_tag_rel
    </sql>

</mapper>
