package org.scrm.domain.phone.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Pattern;

/**
 * 拨打电话记录DTO
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhoneCallRecordDto {

    /** SOP基础ID */
    @NotBlank(message = "SOP基础ID不能为空")
    @Size(max = 64, message = "SOP基础ID长度不能超过64个字符")
    private String sopBaseId;

    /** SOP执行目标ID */
    @Size(max = 64, message = "SOP执行目标ID长度不能超过64个字符")
    private String executeTargetId;

    /** SOP执行目标附件ID（用于区分同一SOP的不同时间段） */
    @NotNull(message = "SOP执行目标附件ID不能为空")
    private Long executeTargetAttachId;

    /** 客户外部ID */
    @NotBlank(message = "客户外部ID不能为空")
    @Size(max = 64, message = "客户外部ID长度不能超过64个字符")
    private String externalUserid;

    /** 客户姓名 */
    @Size(max = 100, message = "客户姓名长度不能超过100个字符")
    private String customerName;

    /** 客户电话号码 */
    @NotBlank(message = "客户电话号码不能为空")
    @Size(max = 30, message = "客户电话号码长度不能超过30个字符")
    @Pattern(regexp = "^[0-9+\\-\\s()]{7,30}$", message = "客户电话号码格式不正确")
    private String customerPhone;

    /** 拨打方式(mobile/unknown/wechat_voice) */
    @Size(max = 20, message = "拨打方式长度不能超过20个字符")
    @Pattern(regexp = "^(mobile|unknown|wechat_voice)$", message = "拨打方式只能是mobile、unknown或wechat_voice")
    private String callMethod;

    /** 备注 */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}


