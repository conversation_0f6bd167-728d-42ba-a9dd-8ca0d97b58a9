<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLeadsMapper">

    <select id="seaLeadsList" resultType="org.scrm.domain.leads.leads.vo.WeLeadsVO">
        SELECT
        t1.id,
        t1.name,
        t1.sex,
        t1.leads_status,
        t1.labels_ids,
        t1.create_time,
        t1.update_time,
        t2.follower_name as preFollowerName
        FROM we_leads t1
        LEFT JOIN we_leads_follower t2 ON t1.id = t2.leads_id AND t2.is_latest = 1 AND t1.leads_status = 0
        WHERE t1.leads_status IN (0, 3)
        <if test="name!=null and name!=''">
            AND t1.name like CONCAT('%',#{name},'%')
        </if>
        <if test="seaId!=null">
            AND t1.sea_id = #{seaId}
        </if>
    </select>

    <!-- 线索列表 -->
    <select id="leadsList" resultType="org.scrm.domain.leads.leads.vo.WeLeadsVO">
        SELECT
        t1.id,
        t1.sea_id,
        t1.name,
        t1.phone,
        t1.source,
        t1.leads_status,
        (SELECT GROUP_CONCAT(t2.follower_name) FROM we_leads_follower t2 WHERE t1.id = t2.leads_id and t2.is_latest = 1) as preFollowerName
        FROM
        we_leads t1
        WHERE
        t1.sea_id = #{request.seaId}
        and t1.del_flag = 0
        <if test="request.name!=null and request.name!=''">AND t1.name like concat('%',#{request.name},'%')</if>
        <if test="request.phone!=null and request.phone!=''">AND t1.phone like concat('%',#{request.phone},'%')</if>
        <if test="request.leadsStatus!=null">AND t1.leads_status = #{request.leadsStatus}</if>
        ORDER BY
        t1.create_time,t1.id DESC
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>
    </select>

    <select id="countLeadsList" resultType="long">
        SELECT
         count(t1.id)
        FROM
        we_leads t1
        WHERE
        t1.sea_id = #{request.seaId}
        and t1.del_flag = 0
        <if test="request.name!=null and request.name!=''">AND t1.name like concat('%',#{request.name},'%')</if>
        <if test="request.phone!=null and request.phone!=''">AND t1.phone like concat('%',#{request.phone},'%')</if>
        <if test="request.leadsStatus!=null">AND t1.leads_status = #{request.leadsStatus}</if>
    </select>
</mapper>
