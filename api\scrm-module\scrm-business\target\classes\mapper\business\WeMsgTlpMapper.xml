<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMsgTlpMapper">


    <select id="getList" resultType="org.scrm.domain.msgtlp.vo.WeMsgTlpVo">
        SELECT
        t.*,
        ( SELECT count( 1 ) FROM we_tlp_material t1 WHERE t.id = t1.tlp_id) AS attachTotalNum
        FROM
        we_msg_tlp t
        <where>
            t.del_flag = 0
            <if test="query.tplType !=null ">and t.tpl_type = #{query.tplType}</if>
            <if test="query.categoryId !=null ">and t.category_id = #{query.categoryId}</if>
            <if test="query.templateType !=null ">and t.template_type = #{query.templateType}</if>
            <if test="query.templateInfo !=null and query.templateInfo != '' ">and t.template_info like concat('%',
                #{query.templateInfo},'%')
            </if>
            <if test="userIds !=null and userIds.size()>0">
                <foreach collection="userIds" item="item" open="AND (FIND_IN_SET("
                         separator=", user_ids) OR FIND_IN_SET( "
                         close=", user_ids))">
                    #{item}
                </foreach>
            </if>
        </where>
        order by update_time desc
    </select>
</mapper>
