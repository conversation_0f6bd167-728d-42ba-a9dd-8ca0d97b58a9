package org.scrm.controller;

import lombok.extern.slf4j.Slf4j;
import org.scrm.base.core.controller.BaseController;
import org.scrm.base.core.domain.AjaxResult;
import org.scrm.base.utils.SecurityUtils;
import org.scrm.base.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.scrm.domain.phone.WePhoneCallRecord;
import org.scrm.domain.phone.dto.PhoneCallRecordDto;
import org.scrm.service.IWePhoneCallRecordService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 拨打电话记录Controller
 * 仅支持手机拨打方式
 *
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@RestController
@RequestMapping("/phone")
public class WePhoneCallController extends BaseController {

    @Autowired
    private IWePhoneCallRecordService phoneCallRecordService;

    /**
     * 记录拨打电话
     */
    @PostMapping("/recordCall")
    public AjaxResult recordCall(@Validated @RequestBody PhoneCallRecordDto dto) {
        try {
            // 获取当前登录用户
            String weUserId = SecurityUtils.getLoginUser().getSysUser().getWeUserId();
            if (StringUtils.isEmpty(weUserId)) {
                return AjaxResult.error("用户企微ID不能为空");
            }

            // 数据权限验证：确保用户只能为自己创建拨打记录
            // 这里weUserId来自登录用户，确保数据安全

            // 构建记录对象
            WePhoneCallRecord record = WePhoneCallRecord.builder()
                    .sopBaseId(dto.getSopBaseId())
                    .executeTargetId(dto.getExecuteTargetId())
                    .executeTargetAttachId(dto.getExecuteTargetAttachId())
                    .externalUserid(dto.getExternalUserid())
                    .customerName(dto.getCustomerName())
                    .customerPhone(dto.getCustomerPhone())
                    .weUserId(weUserId)
                    .callMethod(dto.getCallMethod())
                    .remark(dto.getRemark())
                    .build();

            boolean success = phoneCallRecordService.recordPhoneCall(record);
            if (success) {
                return AjaxResult.success("记录拨打电话成功", record.getId());
            } else {
                return AjaxResult.error("记录拨打电话失败");
            }
        } catch (Exception e) {
            log.error("记录拨打电话失败", e);
            return AjaxResult.error("记录拨打电话失败：" + e.getMessage());
        }
    }



    /**
     * 获取拨打电话统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getCallStatistics(@RequestParam(required = false) String sopBaseId) {
        try {
            String weUserId = SecurityUtils.getLoginUser().getSysUser().getWeUserId();
            if (StringUtils.isEmpty(weUserId)) {
                return AjaxResult.error("用户企微ID不能为空");
            }

            Map<String, Object> statistics = phoneCallRecordService.getCallStatistics(weUserId, sopBaseId);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取拨打电话统计信息失败", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取特定SOP和时间段的拨打统计信息
     */
    @GetMapping("/sopStatistics")
    public AjaxResult getSopCallStatistics(@RequestParam String sopBaseId,
                                          @RequestParam(required = false) String executeTargetAttachId) {
        try {
            String weUserId = SecurityUtils.getLoginUser().getSysUser().getWeUserId();
            if (StringUtils.isEmpty(weUserId)) {
                return AjaxResult.error("用户企微ID不能为空");
            }

            if (StringUtils.isEmpty(sopBaseId)) {
                return AjaxResult.error("SOP基础ID不能为空");
            }

            Map<String, Object> statistics = phoneCallRecordService.getSopCallStatistics(weUserId, sopBaseId, executeTargetAttachId);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取SOP拨打统计信息失败", e);
            return AjaxResult.error("获取统计信息失败：" + e.getMessage());
        }
    }



    /**
     * 查询拨打记录
     */
    @GetMapping("/records")
    public AjaxResult getCallRecords(@RequestParam(required = false) String sopBaseId,
                                     @RequestParam(required = false) String externalUserid) {
        try {
            String weUserId = SecurityUtils.getLoginUser().getSysUser().getWeUserId();
            if (StringUtils.isEmpty(weUserId)) {
                return AjaxResult.error("用户企微ID不能为空");
            }

            List<WePhoneCallRecord> records;
            if (StringUtils.isNotEmpty(externalUserid)) {
                // 查询指定客户的拨打记录
                records = phoneCallRecordService.findCallRecordsByCustomer(externalUserid, weUserId);
            } else {
                // 查询员工的拨打记录
                records = phoneCallRecordService.findCallRecordsByWeUserId(weUserId, sopBaseId);
            }

            return AjaxResult.success(records);
        } catch (Exception e) {
            log.error("查询拨打记录失败", e);
            return AjaxResult.error("查询拨打记录失败：" + e.getMessage());
        }
    }


}
