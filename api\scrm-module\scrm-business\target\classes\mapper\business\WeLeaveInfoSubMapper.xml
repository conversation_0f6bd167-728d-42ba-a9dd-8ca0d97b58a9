<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLeaveInfoSubMapper">

    <delete id="physicalDeleteByIds">
        DELETE FROM we_leave_info_sub WHERE leave_info_id in
        <foreach item="id" index="index" collection="infoIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
