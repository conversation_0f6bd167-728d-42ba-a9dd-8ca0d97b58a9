<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSensitiveActMapper">



    <resultMap type="org.scrm.domain.WeSensitiveAct" id="WeSensitiveActResult">
                <result property="id" column="id" jdbcType="INTEGER"/>
                <result property="actName" column="act_name" jdbcType="VARCHAR"/>
                <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
                <result property="enableFlag" column="enable_flag" jdbcType="INTEGER"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
                <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            </resultMap>

    <sql id="selectWeSensitiveActVo">
        select id, act_name, order_num, enable_flag, create_time, create_by, create_by_id, update_time, update_by, update_by_id, del_flag from we_sensitive_act
    </sql>

</mapper>
