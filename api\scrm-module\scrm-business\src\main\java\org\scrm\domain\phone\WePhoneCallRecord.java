package org.scrm.domain.phone;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.scrm.base.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 拨打电话记录实体类
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("we_phone_call_record")
public class WePhoneCallRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** SOP基础ID */
    @NotBlank(message = "SOP基础ID不能为空")
    private String sopBaseId;

    /** SOP执行目标ID */
    private String executeTargetId;

    /** SOP执行目标附件ID（用于区分同一SOP的不同时间段） */
    @NotNull(message = "SOP执行目标附件ID不能为空")
    private Long executeTargetAttachId;

    /** 客户外部ID */
    @NotBlank(message = "客户外部ID不能为空")
    private String externalUserid;

    /** 客户姓名 */
    private String customerName;

    /** 客户电话号码 */
    @NotBlank(message = "客户电话号码不能为空")
    private String customerPhone;

    /** 执行员工企微ID */
    @NotBlank(message = "执行员工企微ID不能为空")
    private String weUserId;

    /** 拨打方式(mobile/unknown/wechat_voice) */
    private String callMethod;

    /** 备注 */
    private String remark;



}
