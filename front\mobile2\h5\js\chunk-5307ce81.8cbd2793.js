(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5307ce81"],{aba1:function(t,s,i){"use strict";i("c1c8")},b199:function(t,s,i){"use strict";i.d(s,"c",(function(){return r})),i.d(s,"a",(function(){return c})),i.d(s,"b",(function(){return u}));var e=i("b775");const n=window.sysConfig.services.wecom,o=n+"/fission",a=window.sysConfig.services.weChat+"/fission";function r(t,s,i){return Object(e["a"])(o+`/complete/${t}/records/${s}`,i,"post")}function c(t){return Object(e["a"])({url:a+"/findFissionPoster",params:t})}function u(t){return Object(e["a"])({url:a+"/findWeFissionProgress",params:t})}},c1c8:function(t,s,i){},c430b:function(t,s,i){"use strict";i.r(s);var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"container"},[s("img",{staticClass:"posterImg",attrs:{src:t.poster}}),s("div",{staticClass:"footerLink"},[s("div",{staticClass:"sharePic",on:{click:t.tip}},[t._v("分享图片")]),s("div",{staticClass:"myTaskDetail",on:{click:t.goDetail}},[t._v("我的邀请任务进度")])]),s("div",{staticClass:"myStyle"})])},n=[],o=(i("14d9"),i("b199")),a=i("ed08"),r={name:"",components:{},data(){return{poster:"",query:{unionid:"",fissionId:""}}},computed:{},watch:{},created(){this.$toast.loading(),Object(a["d"])().then(({openId:t,unionId:s,avatar:i})=>{s?(this.query.unionid=s,this.query.fissionId=this.$route.query.id,Object(o["a"])(this.query).then(t=>{this.poster=t.data.fissionPosterUrl}).finally(()=>{this.$toast.clear()})):this.$toast.clear()}).catch(()=>{})},mounted(){},methods:{tip(){this.$dialog.alert({title:"分享图片提示",message:"长按图片，在弹出菜单中“发送给朋友”或者可“保存图片”分享至朋友圈",getContainer:".myStyle"}).then(()=>{}).catch(()=>{})},goDetail(){this.$router.push({path:"/taskProcess",query:{...this.query,poster:this.poster}})}}},c=r,u=(i("aba1"),i("2877")),d=Object(u["a"])(c,e,n,!1,null,"7653042e",null);s["default"]=d.exports}}]);