<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupUserStatisticMapper">



    <resultMap type="org.scrm.domain.WeGroupUserStatistic" id="WeGroupUserStatisticResult">
                <result property="id" column="id" jdbcType="INTEGER"/>
                <result property="statTime" column="stat_time" jdbcType="TIMESTAMP"/>
                <result property="userId" column="user_id" jdbcType="VARCHAR"/>
                <result property="newChatCnt" column="new_chat_cnt" jdbcType="INTEGER"/>
                <result property="chatTotal" column="chat_total" jdbcType="INTEGER"/>
                <result property="chatHasMsg" column="chat_has_msg" jdbcType="INTEGER"/>
                <result property="newMemberCnt" column="new_member_cnt" jdbcType="INTEGER"/>
                <result property="memberTotal" column="member_total" jdbcType="INTEGER"/>
                <result property="memberHasMsg" column="member_has_msg" jdbcType="INTEGER"/>
                <result property="msgTotal" column="msg_total" jdbcType="INTEGER"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            </resultMap>

    <sql id="selectWeGroupUserStatisticVo">
        select id, stat_time, user_id, new_chat_cnt, chat_total, chat_has_msg, new_member_cnt, member_total, member_has_msg, msg_total, create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag from we_group_user_statistic
    </sql>

</mapper>
