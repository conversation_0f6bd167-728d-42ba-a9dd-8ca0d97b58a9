package org.scrm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.scrm.domain.phone.WePhoneCallRecord;

import java.util.List;
import java.util.Map;

/**
 * 拨打电话记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface IWePhoneCallRecordService extends IService<WePhoneCallRecord> {

    /**
     * 记录拨打电话
     * 
     * @param record 拨打记录
     * @return 是否成功
     */
    boolean recordPhoneCall(WePhoneCallRecord record);

    /**
     * 查询指定员工的拨打电话记录
     * 
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID（可选）
     * @return 拨打记录列表
     */
    List<WePhoneCallRecord> findCallRecordsByWeUserId(String weUserId, String sopBaseId);

    /**
     * 查询指定客户的拨打电话记录
     * 
     * @param externalUserid 客户外部ID
     * @param weUserId 员工企微ID
     * @return 拨打记录列表
     */
    List<WePhoneCallRecord> findCallRecordsByCustomer(String externalUserid, String weUserId);

    /**
     * 检查客户是否已被拨打过电话
     * 
     * @param externalUserid 客户外部ID
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID
     * @return 是否已拨打过
     */
    boolean hasCalledCustomer(String externalUserid, String weUserId, String sopBaseId);

    /**
     * 获取员工在拨打电话SOP下的统计信息
     *
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID（可选）
     * @return 统计信息 Map
     */
    Map<String, Object> getCallStatistics(String weUserId, String sopBaseId);

    /**
     * 获取特定SOP和时间段的拨打统计信息
     *
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID
     * @param executeTargetAttachId 执行目标附件ID（可选，用于特定时间段）
     * @return 统计信息 Map，包含waitingCount（待拨打）和calledCount（已拨打）
     */
    Map<String, Object> getSopCallStatistics(String weUserId, String sopBaseId, String executeTargetAttachId);





}
