(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-41f82ea5","chunk-097ac29a"],{1419:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e._self._c;return t("div",{staticClass:"no-data"},[t("svg-icon",{staticClass:"no-data-icon",attrs:{name:"no-data"}}),t("span",[e._v(e._s(e.description))])],1)},r=[],s={props:{description:{type:String,default:"暂无数据"}}},a=s,i=(n("8b72"),n("2877")),c=Object(i["a"])(a,o,r,!1,null,"6b545506",null);t["default"]=c.exports},"270e":function(e,t,n){},2982:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAflJREFUOE+lk8FLFGEYxt9vZmzUltC22nHdjTotdAvr0qFLF5Fwdi1CmNlNTYyMOkQbQRBRFMRmhzBKkxU1F3FDcSvKP6BOdYlAutY3lUS6UuHYtt8Tg7ZJM7tEvZfv8vJ7n+f53pcREQGQnfcfCqwSgB9O7GGFH+0gthOEvMykJx9a9k819fSItWHlAVw3rxFEnIjuMUV5DQgNxWKcMWbLWrCtYeDGoiPeUwHXjRMEdKv++uatw7c/lawNvpT445v9BGwP5TIHvQFO06M+Lm2oag4+GHnlyiWblfnY9BxUuSOcHXvmUsCPdO4ie2U6lMtEyoXKdaOPEZtvnLl/3QWwDnXuxfeVwdDDzO5yACtqXBagYnhm/JILsNTbu+kLX3zL6v07Gkf6814Q3mrMSoo8FJwanSwBeFtCl3w1L4KjA9Y73UgzUCGUGz/+J+B9NNEiROFOVaQpEkidXS4BrJh5CkWclOp8B7D0tRvABSI2oVSrV7XJ9JuPXac3FxY+H2UC52VVjTVkh5+7fsHSzXOAOEPE8qitiUnLdoeAMBmwjRjZBPYUG2svhieG5jwXaT3AUeLYcRrnk8nqQCple+Txe5F41EySwLFVC98MAF3KFv8+LX1rocKNrAO0GnelOt+VX1O5Hm+XtcDs2sqWY1Q+pr+4zlXA/9RPYZgAEfn10d8AAAAASUVORK5CYII="},"3a5e":function(e,t,n){"use strict";var o=function(){var e=this,t=e._self._c;return t("van-overlay",{attrs:{show:e.isLoad,"z-index":"999"},on:{click:function(t){e.show=!1}}},[t("div",{staticClass:"popup"},[t("div",{staticClass:"pop-box"},[t("div",{staticClass:"pop-box-bg"}),t("div",{staticClass:"pop-box-content"},[t("svg-icon",{staticClass:"lodash-icon",attrs:{name:"lodash"}}),t("span",[e._v("数据查询中，请稍后...")])],1)])])])},r=[],s={props:{isLoad:{type:Boolean,default:!1}}},a=s,i=(n("97db"),n("2877")),c=Object(i["a"])(a,o,r,!1,null,"4cb44c8c",null);t["a"]=c.exports},"4e28":function(e,t){e.exports="data:image/png;base64,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"},"8b72":function(e,t,n){"use strict";n("270e")},"8d34":function(e,t,n){"use strict";n.r(t),n.d(t,"PHONE_CALL_ERRORS",(function(){return o})),n.d(t,"normalizePhoneNumber",(function(){return s})),n.d(t,"validatePhoneNumber",(function(){return a})),n.d(t,"makePhoneCallWithWx",(function(){return l})),n.d(t,"makePhoneCallWithBrowser",(function(){return A})),n.d(t,"makePhoneCall",(function(){return E})),n.d(t,"hasPhoneCallSop",(function(){return h})),n.d(t,"customerHasPhoneCallSop",(function(){return f})),n.d(t,"findCustomersWithSamePhone",(function(){return C})),n.d(t,"createSafeCustomerSelectCallback",(function(){return p})),n.d(t,"getRecommendedCallConfig",(function(){return g})),n.d(t,"batchMakePhoneCall",(function(){return I})),n.d(t,"DataSecurity",(function(){return d}));n("14d9"),n("e9f5"),n("910d"),n("ab43"),n("a732"),n("88a7"),n("271a"),n("5494");const o={INVALID_PHONE:"INVALID_PHONE",PHONE_TOO_SHORT:"PHONE_TOO_SHORT",PHONE_TOO_LONG:"PHONE_TOO_LONG",INVALID_FORMAT:"INVALID_FORMAT",WX_API_FAILED:"WX_API_FAILED",BROWSER_FAILED:"BROWSER_FAILED",USER_CANCELLED:"USER_CANCELLED",NO_CUSTOMER_SELECTED:"NO_CUSTOMER_SELECTED"},r={CHINA_MOBILE:/^((\+86|0086)?[\s-]?)?(1[3-9]\d{9})$/,CHINA_LANDLINE:/^((\+86|0086)?[\s-]?)?(0\d{2,3}[\s-]?\d{7,8})$/,INTERNATIONAL:/^(\+\d{1,3}[\s-]?)?\d{4,15}$/,WITH_EXTENSION:/^(\+?\d{1,4}[\s-]?)?\d{3,15}([\s-]?(转|ext|分机)[\s-]?\d{1,6})?$/i,GENERAL:/^[\d\s\-\+\(\)转ext分机]{3,30}$/i};function s(e){if(!e||"string"!==typeof e)return"";let t=e.trim().replace(/\s+/g,"");return t=t.replace(/转|分机/g,"ext"),t=t.replace(/[－—]/g,"-"),t}function a(e,t={}){const{strict:n=!1,region:a="CN"}=t;if(!e||"string"!==typeof e||""===e.trim())return{isValid:!1,cleanPhone:"",normalizedPhone:"",message:"客户未设置电话号码",errorCode:o.INVALID_PHONE};const i=s(e),c=i.replace(/[^\d\+\-\(\)ext]/gi,"");if(c.length<3)return{isValid:!1,cleanPhone:"",normalizedPhone:"",message:"电话号码过短",errorCode:o.PHONE_TOO_SHORT};if(c.length>30)return{isValid:!1,cleanPhone:"",normalizedPhone:"",message:"电话号码过长",errorCode:o.PHONE_TOO_LONG};let l=!1,u="unknown";if(n)switch(a){case"CN":l=r.CHINA_MOBILE.test(i)||r.CHINA_LANDLINE.test(i),u=r.CHINA_MOBILE.test(i)?"mobile":"landline";break;case"INTL":l=r.INTERNATIONAL.test(i),u="international";break;default:l=r.GENERAL.test(i)}else l=r.WITH_EXTENSION.test(i)||r.GENERAL.test(i),r.CHINA_MOBILE.test(i)?u="mobile":r.CHINA_LANDLINE.test(i)?u="landline":i.includes("ext")||i.includes("转")?u="extension":i.startsWith("+")&&(u="international");return l?{isValid:!0,cleanPhone:c,normalizedPhone:i,phoneType:u,message:"",errorCode:null}:{isValid:!1,cleanPhone:"",normalizedPhone:"",message:"电话号码格式不正确",errorCode:o.INVALID_FORMAT}}function i(e){return e&&"object"===typeof e?{name:String(e.name||e.customerName||"未知客户").substring(0,50),id:String(e.externalUserid||e.id||"unknown").substring(0,50),company:e.company||e.corpName||""}:{name:"未知客户",id:"unknown"}}function c(e,t,n,o={}){const r=i(n),s={timestamp:(new Date).toISOString(),action:e,phone:t?t.substring(0,20):"unknown",customer:r,result:o,userAgent:navigator.userAgent.substring(0,100)};console.log("通话日志:",s)}function l(e,t,n=null,o={}){return new Promise(o=>{if(!window.wx||!window.wx.invoke)return c("wx_call_failed",e,n,{error:"wx_not_available",message:"企业微信环境不可用"}),void o(!1);c("wx_call_start",e,n);try{window.wx.invoke("makePhoneCall",{phoneNumber:e},r=>{if("makePhoneCall:ok"===r.err_msg)c("wx_call_success",e,n,{response:r}),o(!0);else{if(c("wx_call_failed",e,n,{error:r.err_msg,response:r}),console.warn("企业微信拨打电话失败:",r.err_msg),t&&"function"===typeof t)try{t(e,r.err_msg)}catch(s){console.error("回调函数执行失败:",s)}o(!1)}})}catch(r){if(c("wx_call_error",e,n,{error:r.message,stack:r.stack}),console.error("企业微信API调用异常:",r),t&&"function"===typeof t)try{t(e,r.message)}catch(s){console.error("回调函数执行失败:",s)}o(!1)}})}function u(){const e=/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),t="https:"===window.location.protocol||"localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname;return e||t}function A(e,t,n=null,o={}){return new Promise(o=>{try{if(!u()){const r="当前浏览器环境不支持自动拨号，请手动拨打电话";return c("browser_call_unsupported",e,n,{message:r,userAgent:navigator.userAgent}),t&&"function"===typeof t&&t(e,r),void o(!1)}c("browser_call_start",e,n);const r=e.replace(/[^\d\+\-]/g,""),s=document.createElement("a");s.href="tel:"+r,s.style.display="none",document.body.appendChild(s);const a=()=>{c("browser_call_success",e,n),i(),o(!0)},i=()=>{s&&s.parentNode&&document.body.removeChild(s)},l=setTimeout(()=>{a()},1e3);s.click(),clearTimeout(l),a()}catch(r){if(c("browser_call_error",e,n,{error:r.message,stack:r.stack}),console.error("浏览器拨打电话失败:",r),t&&"function"===typeof t)try{t(e,r.message||"拨号异常")}catch(s){console.error("错误回调函数执行失败:",s)}o(!1)}})}const d={safeString(e,t="",n=100){if(null===e||void 0===e)return t;const o=String(e).trim();return o.length>n?o.substring(0,n)+"...":o},sanitizeHtml(e){return e&&"string"===typeof e?e.replace(/<[^>]*>/g,"").replace(/[<>&"']/g,e=>{const t={"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;","'":"&#x27;"};return t[e]}):""},isUrlSafe(e){if(!e||"string"!==typeof e)return!1;const t=["http:","https:","data:"];try{const n=new URL(e);return t.includes(n.protocol)}catch{return!1}}};async function E(e,t={},n=null,r=null,s={}){const{onError:i,onValidationError:c,onCustomerSelect:l,onSuccess:u,onCancel:A}=t,{skipConfirm:h=!1,preferredMethod:f="wx",strictValidation:C=!1}=s;try{const i=a(e,{strict:C});if(!i.isValid){const t={code:i.errorCode,message:i.message,phone:e};return c&&c(t),{success:!1,error:t,action:"validation_failed"}}const{cleanPhone:p,normalizedPhone:g}=i;let I=n;if(r&&r.length>1){if(l)return new Promise(n=>{l(r,p,async r=>{if(!r){const e={success:!1,error:{code:o.NO_CUSTOMER_SELECTED,message:"未选择客户"},action:"customer_selection_cancelled"};return A&&A(e),void n(e)}const a=await E(e,t,r,null,{...s,skipConfirm:!0});n(a)})});{const e=r.map(e=>d.safeString(e.customerName||e.name,"未知客户",20)).join("、"),t=`该电话号码对应多个客户：${e}\n\n确认要拨打电话吗？\n电话：${p}`;if(!confirm(t)){const e={success:!1,error:{code:o.USER_CANCELLED,message:"用户取消拨打"},action:"user_cancelled"};return A&&A(e),e}I=r[0]}}if(!h&&I){const e=d.safeString(I.name||I.customerName,"未知客户",30),t=d.safeString(I.company||I.corpName,"",30),n=`确认要拨打电话给客户"${e}"吗？\n电话：${p}${t?"\n公司："+t:""}`;if(!confirm(n)){const e={success:!1,error:{code:o.USER_CANCELLED,message:"用户取消拨打"},action:"user_cancelled"};return A&&A(e),e}}const S=await m(p,I,t,{preferredMethod:f});return S.success&&u&&u(S),S}catch(p){const e={success:!1,error:{code:"UNEXPECTED_ERROR",message:p.message||"未知错误",stack:p.stack},action:"unexpected_error"};return console.error("拨打电话过程中发生异常:",p),i&&i(e.error),e}}async function m(e,t,n={},r={}){const{onError:s}=n,{preferredMethod:a="wx"}=r,u=i(t);c("call_start",e,u,{preferredMethod:a});try{let t=null;if("wx"===a){const n=await l(e,null,u);if(n)t={success:!0,method:"wechat",phone:e,customer:u,action:"call_success"};else{const n=await A(e,null,u);t=n?{success:!0,method:"browser",phone:e,customer:u,action:"call_success_fallback"}:{success:!1,error:{code:o.BROWSER_FAILED,message:"所有拨打方式都失败了，请手动拨打电话"},phone:e,customer:u,action:"call_failed_all_methods"}}}else{const n=await A(e,null,u);if(n)t={success:!0,method:"browser",phone:e,customer:u,action:"call_success"};else{const n=await l(e,null,u);t=n?{success:!0,method:"wechat",phone:e,customer:u,action:"call_success_fallback"}:{success:!1,error:{code:o.WX_API_FAILED,message:"所有拨打方式都失败了，请手动拨打电话"},phone:e,customer:u,action:"call_failed_all_methods"}}}return c("call_end",e,u,t),!t.success&&s&&s(t.error),t}catch(d){const t={success:!1,error:{code:"CALL_PROCESS_ERROR",message:d.message||"拨打过程中发生异常",stack:d.stack},phone:e,customer:u,action:"call_process_error"};return c("call_error",e,u,t),s&&s(t.error),t}}function h(e){return!!Array.isArray(e)&&e.some(e=>e&&7===e.businessType)}function f(e){return!!(e&&e.weSopToBeSentContentInfoVo&&Array.isArray(e.weSopToBeSentContentInfoVo))&&e.weSopToBeSentContentInfoVo.some(e=>e&&7===e.businessType)}function C(e,t){if(!e||!Array.isArray(t))return[];const n=a(e);if(!n.isValid)return[];const{normalizedPhone:o}=n;return t.filter(e=>{if(!e||!e.phone)return!1;const t=a(e.phone);return!!t.isValid&&t.normalizedPhone===o})}function p(e){return function(t,n,o){try{if(!Array.isArray(t)||0===t.length)return void console.warn("客户列表为空或无效");if("function"!==typeof o)return void console.warn("选择回调函数无效");const r=t.map(e=>({...e,customerName:d.safeString(e.customerName||e.name,"未知客户"),corpName:d.safeString(e.corpName||e.company,""),position:d.safeString(e.position,""),avatar:e.avatar&&d.isUrlSafe(e.avatar)?e.avatar:null,externalUserid:d.safeString(e.externalUserid||e.id,"unknown")}));e(r,n,o)}catch(r){console.error("客户选择回调执行失败:",r)}}}function g(e={}){const{userAgent:t=navigator.userAgent,isWxWork:n=window.wx&&window.wx.invoke,customerCount:o=1,phoneType:r="unknown"}=e,s=/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(t);return{preferredMethod:n?"wx":"browser",strictValidation:!s,skipConfirm:1===o&&"mobile"===r,showCustomerInfo:o>1,enableLogging:!0,timeout:s?3e3:5e3}}async function I(e,t={}){if(!Array.isArray(e)||0===e.length)return[];const{maxConcurrent:n=1,delay:o=2e3,onProgress:r=null,...s}=t,a=[];for(let c=0;c<e.length;c++){const t=e[c];try{const n=await E(t.phone,t.callbacks||{},t.customer||null,t.allCustomers||null,{...s,skipConfirm:!0});a.push({index:c,phone:t.phone,customer:t.customer,result:n}),r&&r(c+1,e.length,n),c<e.length-1&&await new Promise(e=>setTimeout(e,o))}catch(i){a.push({index:c,phone:t.phone,customer:t.customer,result:{success:!1,error:{code:"BATCH_CALL_ERROR",message:i.message}}})}}return a}t["default"]={makePhoneCall:E,validatePhoneNumber:a,normalizePhoneNumber:s,findCustomersWithSamePhone:C,hasPhoneCallSop:h,customerHasPhoneCallSop:f,getRecommendedCallConfig:g,batchMakePhoneCall:I,PHONE_CALL_ERRORS:o,DataSecurity:d}},"97db":function(e,t,n){"use strict";n("a507")},9815:function(e,t,n){"use strict";n.d(t,"a",(function(){return a})),n.d(t,"e",(function(){return i})),n.d(t,"b",(function(){return c})),n.d(t,"c",(function(){return l})),n.d(t,"d",(function(){return u}));var o=n("b775");const r=window.sysConfig.services.wecom,s=r+"/sop";function a(){return Object(o["a"])({url:s+"/findWeCustomerSop"})}function i(){return Object(o["a"])({url:s+"/findTodayGroupSop"})}function c(e){return Object(o["a"])({url:s+"/findCustomerSopContent",params:e})}function l(e){return Object(o["a"])({url:s+"/findGroupSopContent",params:e})}function u(e){return Object(o["a"])({url:s+"/executeSop/"+e,method:"post"})}},a507:function(e,t,n){},a732:function(e,t,n){"use strict";var o=n("23e7"),r=n("c65b"),s=n("2266"),a=n("59ed"),i=n("825a"),c=n("46c4"),l=n("2a62"),u=n("f99f"),A=u("some",TypeError);o({target:"Iterator",proto:!0,real:!0,forced:A},{some:function(e){i(this);try{a(e)}catch(o){l(this,"throw",o)}if(A)return r(A,this,e);var t=c(this),n=0;return s(t,(function(t,o){if(e(t,n++))return o()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},d378:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAexJREFUOE+l001oE1EQB/D/vE1N0Si0JQXb0GRjES2C6EXsQQq99SAUzDFgToKH3jyY2BK1u239AEEPKlQvgoeeVPRQCI0ILYiiCEotuNv0w0MDSSCUNnHfm7JKxWabYnWu8/jNvDfzCP8ZxMwCAP2LQ0TyrwEtZY+A0fqrkJiTZnhsV4DvSu4sS+eYAg2CaJlNvbMucOE9xLOpb03RsL88HQtV3ZrNaau9WKWMIEwqRt+2QOvN+WC+pNxWzwHkAGgEc8bvE/cqku8K4kcHGsTTUoUneUQ/uqWDlhvLBwul6hsBPG8OiNF8Mrxy6GExML9QTChFo4J4XBrRgWw2S/3ZSKiYjixuAeiy9UII7Z00wldrJ+IbXOiXUt7qOawfmTqPH5v530CL8b29sFr5dPqE3jYdw8871wYl7dea0Mac4Y5XHqAxZfdWgCE29J56+6Al7TsALUozctsD7B3KnVpz1H029ZM7AOMQ/FYORx94gO4J+Gc+2EsBnzxTvtY5W4u4j2nZpdmAX+stpzu+egB3lbWUNaCY4sEm6lu5FMlvHuqeWNoz89F5AuZVNvXEn7hnkbSUZShFCSHoMUHNMVGbUogD/KUrtD/++WJwbUfATe5L546vVzkG4hAYhQYNL9ev65ltJ7Obz1QP2AD9HPcR/yc5WAAAAABJRU5ErkJggg=="}}]);