(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d215fa9"],{c105:function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e._self._c;return t("van-pull-refresh",{attrs:{"success-text":"刷新成功"},on:{refresh:function(t){return e.getList(1)}},model:{value:e.refreshing,callback:function(t){e.refreshing=t},expression:"refreshing"}},[t("van-list",{attrs:{finished:e.finished,"finished-text":e.finishedText,error:e.error,"error-text":"请求失败，点击重新加载"},on:{"update:error":function(t){e.error=t},load:function(t){return e.getList()}},model:{value:e.loading,callback:function(t){e.loading=t},expression:"loading"}},[e.error||e.loading||e.list&&e.list.length?e._e():t("Empty"),e._t("default",null,null,e.list)],2)],1)},n=[],r=(i("14d9"),{name:"",props:{params:{type:Object,default:()=>({})},request:{type:Function},dealQueryFun:{type:Function,default:null},dealDataFun:{type:Function,default:null}},data(){return{loading:!1,query:{pageNum:1,pageSize:10,type:""},refreshing:!1,finished:!1,finishedText:"",error:!1,list:[]}},computed:{},watch:{},created(){},mounted(){},methods:{getList(e){this.loading=!0,this.finished=!1,e&&(this.query.pageNum=e),Object.assign(this.query,this.params),this.dealQueryFun&&this.dealQueryFun(this.query),this.query=Object.assign({pageNum:1,pageSize:10},this.query),this.request(this.query).then(({rows:e,total:t})=>{1==this.query.pageNum&&(this.list=[]),this.dealDataFun&&this.dealDataFun(e),this.list.push(...e),this.loading=!1,this.refreshing=!1,this.list.length>=+t?(0==this.list.length?(this.query.pageNum=1,this.finishedText=""):this.finishedText="没有更多了",this.finished=!0):this.query.pageNum++}).catch(()=>{this.error=!0,this.loading=!1,this.finished=!0})}}}),a=r,u=i("2877"),h=Object(u["a"])(a,s,n,!1,null,"41959c22",null);t["default"]=h.exports}}]);