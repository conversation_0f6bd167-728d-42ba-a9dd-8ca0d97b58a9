<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupMessageTaskMapper">



    <resultMap type="org.scrm.domain.WeGroupMessageTask" id="WeGroupMessageTaskResult">
        <result property="id"    column="id"    />
        <result property="msgTemplateId"    column="msg_template_id"    />
        <result property="msgId"    column="msg_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="toBeCustomerName"    column="to_be_customer_name"    />
        <result property="alreadyCustomerName"    column="already_customer_name"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
    </resultMap>

    <select id="groupMsgTaskList" resultMap="WeGroupMessageTaskResult">
        select
        wgmt.id,
        wgmt.msg_template_id,
        wgmt.msg_id,
        wgmt.user_id,
        (select su.user_name from sys_user su where su.we_user_id = wgmt.user_id limit 1) as user_name,
        wgmt.status,
        wgmt.send_time,
        (select count(1) from we_group_message_send_result wgmsr where wgmsr.user_id = wgmt.user_id and wgmsr.msg_template_id = wgmt.msg_template_id) as to_be_customer_name,
        (select count(1) from we_group_message_send_result wgmsr where wgmsr.user_id = wgmt.user_id and wgmsr.msg_template_id = wgmt.msg_template_id and wgmsr.status = 1) as already_customer_name
        from we_group_message_task wgmt
        <where>
            <if test="id != null">
                and wgmt.id = #{id}
            </if>
            <if test="msgId != null and msgId != ''">
                and wgmt.msg_id = #{msgId}
            </if>
            <if test="msgTemplateId != null and msgTemplateId != ''">
                and wgmt.msg_template_id = #{msgTemplateId}
            </if>
            <if test="userName != null and userName != ''">
                and exists
                (select 1 from sys_user su where su.we_user_id = wgmt.user_id  and su.user_name like concat('%', #{userName}, '%'))
            </if>
            <if test="status != null">
                and wgmt.status = #{status}
            </if>
            <if test="beginTime != null and beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(wgmt.send_time,'%y%m%d') &gt;= date_format(#{beginTime},'%y%m%d')
            </if>
            <if test="endTime != null and endTime != ''"><!-- 结束时间检索 -->
                AND date_format(wgmt.send_time,'%y%m%d') &lt;= date_format(#{endTime},'%y%m%d')
            </if>
        </where>
    </select>

</mapper>
