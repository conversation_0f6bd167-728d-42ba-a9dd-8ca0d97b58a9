<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLeadsAutoRecoveryMapper">
    <select id="findAutoRecoveryByDate" resultType="org.scrm.domain.leads.leads.entity.WeLeadsAutoRecovery">
        SELECT
          *
        FROM
        we_leads_auto_recovery
        WHERE executing_state=0 and recovery_time &lt; CURRENT_DATE
    </select>

</mapper>
