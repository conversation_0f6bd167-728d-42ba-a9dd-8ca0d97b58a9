<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMomentsCustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.moments.entity.WeMomentsCustomer">
        <id column="id" property="id"/>
        <result column="moment_task_id" property="momentsTaskId"/>
        <result column="moments_id" property="momentsId"/>
        <result column="user_id" property="userId"/>
        <result column="we_user_id" property="weUserId"/>
        <result column="user_name" property="userName"/>
        <result column="external_userid" property="externalUserid"/>
        <result column="customer_name" property="customerName"/>
        <result column="delivery_status" property="deliveryStatus"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        moment_task_id,
        moments_id,
        user_id,
        we_user_id,
        user_name,
        external_userid,
        customer_name,
        delivery_status,
        create_by,
        update_by,
        update_by_id,
        create_by_id,
        create_time,
        update_time,
        del_flag
    </sql>

</mapper>
