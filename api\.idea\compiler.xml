<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="scrm-system" />
        <module name="scrm-framework" />
        <module name="scrm-business" />
        <module name="scrm-wechat-api" />
        <module name="scrm-gateway" />
        <module name="scrm-callback" />
        <module name="scrm-mobile-api" />
        <module name="scrm-web-api" />
        <module name="scrm-base-core" />
        <module name="scrm-file" />
        <module name="scrm-openai" />
        <module name="scrm-task" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="scrm-callback" target="1.8" />
      <module name="scrm-file" target="1.8" />
      <module name="scrm-mobile-api" target="1.8" />
      <module name="scrm-openai" target="1.8" />
      <module name="scrm-system" target="1.8" />
      <module name="scrm-wechat-api" target="1.8" />
    </bytecodeTargetLevel>
  </component>
</project>