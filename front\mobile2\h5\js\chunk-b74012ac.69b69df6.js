(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b74012ac","chunk-1f87cc3a"],{"016c":function(e,t,r){var n=function(){var e=new Date,t=4,i=3,r=2,s=1,o=t,a={setLogLevel:function(e){o=e==this.debug?s:e==this.info?r:e==this.warn?i:(this.error,t)},debug:function(t,i){void 0===console.debug&&(console.debug=console.log),s>=o&&console.debug("["+n.getDurationString(new Date-e,1e3)+"]","["+t+"]",i)},log:function(e,t){this.debug(e.msg)},info:function(t,i){r>=o&&console.info("["+n.getDurationString(new Date-e,1e3)+"]","["+t+"]",i)},warn:function(t,r){i>=o&&console.warn("["+n.getDurationString(new Date-e,1e3)+"]","["+t+"]",r)},error:function(i,r){t>=o&&console.error("["+n.getDurationString(new Date-e,1e3)+"]","["+i+"]",r)}};return a}();n.getDurationString=function(e,t){var i;function r(e,t){var i=""+e,r=i.split(".");while(r[0].length<t)r[0]="0"+r[0];return r.join(".")}e<0?(i=!0,e=-e):i=!1;var n=t||1,s=e/n,o=Math.floor(s/3600);s-=3600*o;var a=Math.floor(s/60);s-=60*a;var l=1e3*s;return s=Math.floor(s),l-=1e3*s,l=Math.floor(l),(i?"-":"")+o+":"+r(a,2)+":"+r(s,2)+"."+r(l,3)},n.printRanges=function(e){var t=e.length;if(t>0){for(var i="",r=0;r<t;r++)r>0&&(i+=","),i+="["+n.getDurationString(e.start(r))+","+n.getDurationString(e.end(r))+"]";return i}return"(empty)"},t.Log=n;var s=function(e){if(!(e instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=e,this.dataview=new DataView(e),this.position=0};s.prototype.getPosition=function(){return this.position},s.prototype.getEndPosition=function(){return this.buffer.byteLength},s.prototype.getLength=function(){return this.buffer.byteLength},s.prototype.seek=function(e){var t=Math.max(0,Math.min(this.buffer.byteLength,e));return this.position=isNaN(t)||!isFinite(t)?0:t,!0},s.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},s.prototype.readAnyInt=function(e,t){var i=0;if(this.position+e<=this.buffer.byteLength){switch(e){case 1:i=t?this.dataview.getInt8(this.position):this.dataview.getUint8(this.position);break;case 2:i=t?this.dataview.getInt16(this.position):this.dataview.getUint16(this.position);break;case 3:if(t)throw"No method for reading signed 24 bits values";i=this.dataview.getUint8(this.position)<<16,i|=this.dataview.getUint8(this.position+1)<<8,i|=this.dataview.getUint8(this.position+2);break;case 4:i=t?this.dataview.getInt32(this.position):this.dataview.getUint32(this.position);break;case 8:if(t)throw"No method for reading signed 64 bits values";i=this.dataview.getUint32(this.position)<<32,i|=this.dataview.getUint32(this.position+4);break;default:throw"readInt method not implemented for size: "+e}return this.position+=e,i}throw"Not enough bytes in buffer"},s.prototype.readUint8=function(){return this.readAnyInt(1,!1)},s.prototype.readUint16=function(){return this.readAnyInt(2,!1)},s.prototype.readUint24=function(){return this.readAnyInt(3,!1)},s.prototype.readUint32=function(){return this.readAnyInt(4,!1)},s.prototype.readUint64=function(){return this.readAnyInt(8,!1)},s.prototype.readString=function(e){if(this.position+e<=this.buffer.byteLength){for(var t="",i=0;i<e;i++)t+=String.fromCharCode(this.readUint8());return t}throw"Not enough bytes in buffer"},s.prototype.readCString=function(){var e=[];while(1){var t=this.readUint8();if(0===t)break;e.push(t)}return String.fromCharCode.apply(null,e)},s.prototype.readInt8=function(){return this.readAnyInt(1,!0)},s.prototype.readInt16=function(){return this.readAnyInt(2,!0)},s.prototype.readInt32=function(){return this.readAnyInt(4,!0)},s.prototype.readInt64=function(){return this.readAnyInt(8,!1)},s.prototype.readUint8Array=function(e){for(var t=new Uint8Array(e),i=0;i<e;i++)t[i]=this.readUint8();return t},s.prototype.readInt16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readInt16();return t},s.prototype.readUint16Array=function(e){for(var t=new Int16Array(e),i=0;i<e;i++)t[i]=this.readUint16();return t},s.prototype.readUint32Array=function(e){for(var t=new Uint32Array(e),i=0;i<e;i++)t[i]=this.readUint32();return t},s.prototype.readInt32Array=function(e){for(var t=new Int32Array(e),i=0;i<e;i++)t[i]=this.readInt32();return t},t.MP4BoxStream=s;var o=function(e,t,i){this._byteOffset=t||0,e instanceof ArrayBuffer?this.buffer=e:"object"==typeof e?(this.dataView=e,t&&(this._byteOffset+=t)):this.buffer=new ArrayBuffer(e||0),this.position=0,this.endianness=null==i?o.LITTLE_ENDIAN:i};o.prototype={},o.prototype.getPosition=function(){return this.position},o.prototype._realloc=function(e){if(this._dynamicSize){var t=this._byteOffset+this.position+e,i=this._buffer.byteLength;if(t<=i)t>this._byteLength&&(this._byteLength=t);else{i<1&&(i=1);while(t>i)i*=2;var r=new ArrayBuffer(i),n=new Uint8Array(this._buffer),s=new Uint8Array(r,0,n.length);s.set(n),this.buffer=r,this._byteLength=t}}},o.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var e=new ArrayBuffer(this._byteLength),t=new Uint8Array(e),i=new Uint8Array(this._buffer,0,t.length);t.set(i),this.buffer=e}},o.BIG_ENDIAN=!1,o.LITTLE_ENDIAN=!0,o.prototype._byteLength=0,Object.defineProperty(o.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(o.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(e){this._buffer=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(o.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(e){this._byteOffset=e,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(o.prototype,"dataView",{get:function(){return this._dataView},set:function(e){this._byteOffset=e.byteOffset,this._buffer=e.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+e.byteLength}}),o.prototype.seek=function(e){var t=Math.max(0,Math.min(this.byteLength,e));this.position=isNaN(t)||!isFinite(t)?0:t},o.prototype.isEof=function(){return this.position>=this._byteLength},o.prototype.mapUint8Array=function(e){this._realloc(1*e);var t=new Uint8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},o.prototype.readInt32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Int32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Int16Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Int8Array(e);return o.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},o.prototype.readUint32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Uint32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readUint16Array=function(e,t){e=null==e?this.byteLength-this.position/2:e;var i=new Uint16Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readUint8Array=function(e){e=null==e?this.byteLength-this.position:e;var t=new Uint8Array(e);return o.memcpy(t.buffer,0,this.buffer,this.byteOffset+this.position,e*t.BYTES_PER_ELEMENT),this.position+=t.byteLength,t},o.prototype.readFloat64Array=function(e,t){e=null==e?this.byteLength-this.position/8:e;var i=new Float64Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readFloat32Array=function(e,t){e=null==e?this.byteLength-this.position/4:e;var i=new Float32Array(e);return o.memcpy(i.buffer,0,this.buffer,this.byteOffset+this.position,e*i.BYTES_PER_ELEMENT),o.arrayToNative(i,null==t?this.endianness:t),this.position+=i.byteLength,i},o.prototype.readInt32=function(e){var t=this._dataView.getInt32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readInt16=function(e){var t=this._dataView.getInt16(this.position,null==e?this.endianness:e);return this.position+=2,t},o.prototype.readInt8=function(){var e=this._dataView.getInt8(this.position);return this.position+=1,e},o.prototype.readUint32=function(e){var t=this._dataView.getUint32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readUint16=function(e){var t=this._dataView.getUint16(this.position,null==e?this.endianness:e);return this.position+=2,t},o.prototype.readUint8=function(){var e=this._dataView.getUint8(this.position);return this.position+=1,e},o.prototype.readFloat32=function(e){var t=this._dataView.getFloat32(this.position,null==e?this.endianness:e);return this.position+=4,t},o.prototype.readFloat64=function(e){var t=this._dataView.getFloat64(this.position,null==e?this.endianness:e);return this.position+=8,t},o.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,o.memcpy=function(e,t,i,r,n){var s=new Uint8Array(e,t,n),o=new Uint8Array(i,r,n);s.set(o)},o.arrayToNative=function(e,t){return t==this.endianness?e:this.flipArrayEndianness(e)},o.nativeToEndian=function(e,t){return this.endianness==t?e:this.flipArrayEndianness(e)},o.flipArrayEndianness=function(e){for(var t=new Uint8Array(e.buffer,e.byteOffset,e.byteLength),i=0;i<e.byteLength;i+=e.BYTES_PER_ELEMENT)for(var r=i+e.BYTES_PER_ELEMENT-1,n=i;r>n;r--,n++){var s=t[n];t[n]=t[r],t[r]=s}return e},o.prototype.failurePosition=0,String.fromCharCodeUint8=function(e){for(var t=[],i=0;i<e.length;i++)t[i]=e[i];return String.fromCharCode.apply(null,t)},o.prototype.readString=function(e,t){return null==t||"ASCII"==t?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==e?this.byteLength-this.position:e)]):new TextDecoder(t).decode(this.mapUint8Array(e))},o.prototype.readCString=function(e){var t=this.byteLength-this.position,i=new Uint8Array(this._buffer,this._byteOffset+this.position),r=t;null!=e&&(r=Math.min(e,t));for(var n=0;n<r&&0!==i[n];n++);var s=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(n)]);return null!=e?this.position+=r-n:n!=t&&(this.position+=1),s};var a=Math.pow(2,32);o.prototype.readInt64=function(){return this.readInt32()*a+this.readUint32()},o.prototype.readUint64=function(){return this.readUint32()*a+this.readUint32()},o.prototype.readInt64=function(){return this.readUint32()*a+this.readUint32()},o.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},t.DataStream=o,o.prototype.save=function(e){var t=new Blob([this.buffer]);if(!window.URL||!URL.createObjectURL)throw"DataStream.save: Can't create object URL.";var i=window.URL.createObjectURL(t),r=document.createElement("a");document.body.appendChild(r),r.setAttribute("href",i),r.setAttribute("download",e),r.setAttribute("target","_self"),r.click(),window.URL.revokeObjectURL(i)},o.prototype._dynamicSize=!0,Object.defineProperty(o.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(e){e||this._trimAlloc(),this._dynamicSize=e}}),o.prototype.shift=function(e){var t=new ArrayBuffer(this._byteLength-e),i=new Uint8Array(t),r=new Uint8Array(this._buffer,e,i.length);i.set(r),this.buffer=t,this.position-=e},o.prototype.writeInt32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Int32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt32(e[i],t)},o.prototype.writeInt16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Int16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeInt16(e[i],t)},o.prototype.writeInt8Array=function(e){if(this._realloc(1*e.length),e instanceof Int8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapInt8Array(e.length);else for(var t=0;t<e.length;t++)this.writeInt8(e[t])},o.prototype.writeUint32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Uint32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint32(e[i],t)},o.prototype.writeUint16Array=function(e,t){if(this._realloc(2*e.length),e instanceof Uint16Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint16Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeUint16(e[i],t)},o.prototype.writeUint8Array=function(e){if(this._realloc(1*e.length),e instanceof Uint8Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapUint8Array(e.length);else for(var t=0;t<e.length;t++)this.writeUint8(e[t])},o.prototype.writeFloat64Array=function(e,t){if(this._realloc(8*e.length),e instanceof Float64Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat64Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat64(e[i],t)},o.prototype.writeFloat32Array=function(e,t){if(this._realloc(4*e.length),e instanceof Float32Array&&this.byteOffset+this.position%e.BYTES_PER_ELEMENT===0)o.memcpy(this._buffer,this.byteOffset+this.position,e.buffer,0,e.byteLength),this.mapFloat32Array(e.length,t);else for(var i=0;i<e.length;i++)this.writeFloat32(e[i],t)},o.prototype.writeInt32=function(e,t){this._realloc(4),this._dataView.setInt32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeInt16=function(e,t){this._realloc(2),this._dataView.setInt16(this.position,e,null==t?this.endianness:t),this.position+=2},o.prototype.writeInt8=function(e){this._realloc(1),this._dataView.setInt8(this.position,e),this.position+=1},o.prototype.writeUint32=function(e,t){this._realloc(4),this._dataView.setUint32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeUint16=function(e,t){this._realloc(2),this._dataView.setUint16(this.position,e,null==t?this.endianness:t),this.position+=2},o.prototype.writeUint8=function(e){this._realloc(1),this._dataView.setUint8(this.position,e),this.position+=1},o.prototype.writeFloat32=function(e,t){this._realloc(4),this._dataView.setFloat32(this.position,e,null==t?this.endianness:t),this.position+=4},o.prototype.writeFloat64=function(e,t){this._realloc(8),this._dataView.setFloat64(this.position,e,null==t?this.endianness:t),this.position+=8},o.prototype.writeUCS2String=function(e,t,i){null==i&&(i=e.length);for(var r=0;r<e.length&&r<i;r++)this.writeUint16(e.charCodeAt(r),t);for(;r<i;r++)this.writeUint16(0)},o.prototype.writeString=function(e,t,i){var r=0;if(null==t||"ASCII"==t)if(null!=i){var n=Math.min(e.length,i);for(r=0;r<n;r++)this.writeUint8(e.charCodeAt(r));for(;r<i;r++)this.writeUint8(0)}else for(r=0;r<e.length;r++)this.writeUint8(e.charCodeAt(r));else this.writeUint8Array(new TextEncoder(t).encode(e.substring(0,i)))},o.prototype.writeCString=function(e,t){var i=0;if(null!=t){var r=Math.min(e.length,t);for(i=0;i<r;i++)this.writeUint8(e.charCodeAt(i));for(;i<t;i++)this.writeUint8(0)}else{for(i=0;i<e.length;i++)this.writeUint8(e.charCodeAt(i));this.writeUint8(0)}},o.prototype.writeStruct=function(e,t){for(var i=0;i<e.length;i+=2){var r=e[i+1];this.writeType(r,t[e[i]],t)}},o.prototype.writeType=function(e,t,i){var r;if("function"==typeof e)return e(this,t);if("object"==typeof e&&!(e instanceof Array))return e.set(this,t,i);var n=null,s="ASCII",a=this.position;switch("string"==typeof e&&/:/.test(e)&&(r=e.split(":"),e=r[0],n=parseInt(r[1])),"string"==typeof e&&/,/.test(e)&&(r=e.split(","),e=r[0],s=parseInt(r[1])),e){case"uint8":this.writeUint8(t);break;case"int8":this.writeInt8(t);break;case"uint16":this.writeUint16(t,this.endianness);break;case"int16":this.writeInt16(t,this.endianness);break;case"uint32":this.writeUint32(t,this.endianness);break;case"int32":this.writeInt32(t,this.endianness);break;case"float32":this.writeFloat32(t,this.endianness);break;case"float64":this.writeFloat64(t,this.endianness);break;case"uint16be":this.writeUint16(t,o.BIG_ENDIAN);break;case"int16be":this.writeInt16(t,o.BIG_ENDIAN);break;case"uint32be":this.writeUint32(t,o.BIG_ENDIAN);break;case"int32be":this.writeInt32(t,o.BIG_ENDIAN);break;case"float32be":this.writeFloat32(t,o.BIG_ENDIAN);break;case"float64be":this.writeFloat64(t,o.BIG_ENDIAN);break;case"uint16le":this.writeUint16(t,o.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(t,o.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(t,o.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(t,o.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(t,o.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(t,o.LITTLE_ENDIAN);break;case"cstring":this.writeCString(t,n);break;case"string":this.writeString(t,s,n);break;case"u16string":this.writeUCS2String(t,this.endianness,n);break;case"u16stringle":this.writeUCS2String(t,o.LITTLE_ENDIAN,n);break;case"u16stringbe":this.writeUCS2String(t,o.BIG_ENDIAN,n);break;default:if(3==e.length){for(var l=e[1],c=0;c<t.length;c++)this.writeType(l,t[c]);break}this.writeStruct(e,t);break}null!=n&&(this.position=a,this._realloc(n),this.position=a+n)},o.prototype.writeUint64=function(e){var t=Math.floor(e/a);this.writeUint32(t),this.writeUint32(4294967295&e)},o.prototype.writeUint24=function(e){this.writeUint8((16711680&e)>>16),this.writeUint8((65280&e)>>8),this.writeUint8(255&e)},o.prototype.adjustUint32=function(e,t){var i=this.position;this.seek(e),this.writeUint32(t),this.seek(i)},o.prototype.mapInt32Array=function(e,t){this._realloc(4*e);var i=new Int32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},o.prototype.mapInt16Array=function(e,t){this._realloc(2*e);var i=new Int16Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},o.prototype.mapInt8Array=function(e){this._realloc(1*e);var t=new Int8Array(this._buffer,this.byteOffset+this.position,e);return this.position+=1*e,t},o.prototype.mapUint32Array=function(e,t){this._realloc(4*e);var i=new Uint32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i},o.prototype.mapUint16Array=function(e,t){this._realloc(2*e);var i=new Uint16Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=2*e,i},o.prototype.mapFloat64Array=function(e,t){this._realloc(8*e);var i=new Float64Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=8*e,i},o.prototype.mapFloat32Array=function(e,t){this._realloc(4*e);var i=new Float32Array(this._buffer,this.byteOffset+this.position,e);return o.arrayToNative(i,null==t?this.endianness:t),this.position+=4*e,i};var l=function(e){this.buffers=[],this.bufferIndex=-1,e&&(this.insertBuffer(e),this.bufferIndex=0)};l.prototype=new o(new ArrayBuffer,0,o.BIG_ENDIAN),l.prototype.initialized=function(){var e;return this.bufferIndex>-1||(this.buffers.length>0?(e=this.buffers[0],0===e.fileStart?(this.buffer=e,this.bufferIndex=0,n.debug("MultiBufferStream","Stream ready for parsing"),!0):(n.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1)):(n.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1))},ArrayBuffer.concat=function(e,t){n.debug("ArrayBuffer","Trying to create a new buffer of size: "+(e.byteLength+t.byteLength));var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(new Uint8Array(e),0),i.set(new Uint8Array(t),e.byteLength),i.buffer},l.prototype.reduceBuffer=function(e,t,i){var r;return r=new Uint8Array(i),r.set(new Uint8Array(e,t,i)),r.buffer.fileStart=e.fileStart+t,r.buffer.usedBytes=0,r.buffer},l.prototype.insertBuffer=function(e){for(var t=!0,i=0;i<this.buffers.length;i++){var r=this.buffers[i];if(e.fileStart<=r.fileStart){if(e.fileStart===r.fileStart){if(e.byteLength>r.byteLength){this.buffers.splice(i,1),i--;continue}n.warn("MultiBufferStream","Buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+") already appended, ignoring")}else e.fileStart+e.byteLength<=r.fileStart||(e=this.reduceBuffer(e,0,r.fileStart-e.fileStart)),n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.splice(i,0,e),0===i&&(this.buffer=e);t=!1;break}if(e.fileStart<r.fileStart+r.byteLength){var s=r.fileStart+r.byteLength-e.fileStart,o=e.byteLength-s;if(!(o>0)){t=!1;break}e=this.reduceBuffer(e,s,o)}}t&&(n.debug("MultiBufferStream","Appending new buffer (fileStart: "+e.fileStart+" - Length: "+e.byteLength+")"),this.buffers.push(e),0===i&&(this.buffer=e))},l.prototype.logBufferLevel=function(e){var t,i,r,s,o,a=[],l="";for(r=0,s=0,t=0;t<this.buffers.length;t++)i=this.buffers[t],0===t?(o={},a.push(o),o.start=i.fileStart,o.end=i.fileStart+i.byteLength,l+="["+o.start+"-"):o.end===i.fileStart?o.end=i.fileStart+i.byteLength:(o={},o.start=i.fileStart,l+=a[a.length-1].end-1+"], ["+o.start+"-",o.end=i.fileStart+i.byteLength,a.push(o)),r+=i.usedBytes,s+=i.byteLength;a.length>0&&(l+=o.end-1+"]");var c=e?n.info:n.debug;0===this.buffers.length?c("MultiBufferStream","No more buffer in memory"):c("MultiBufferStream",this.buffers.length+" stored buffer(s) ("+r+"/"+s+" bytes), continuous ranges: "+l)},l.prototype.cleanBuffers=function(){var e,t;for(e=0;e<this.buffers.length;e++)t=this.buffers[e],t.usedBytes===t.byteLength&&(n.debug("MultiBufferStream","Removing buffer #"+e),this.buffers.splice(e,1),e--)},l.prototype.mergeNextBuffer=function(){var e;if(this.bufferIndex+1<this.buffers.length){if(e=this.buffers[this.bufferIndex+1],e.fileStart===this.buffer.fileStart+this.buffer.byteLength){var t=this.buffer.byteLength,i=this.buffer.usedBytes,r=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,e),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=i,this.buffer.fileStart=r,n.debug("ISOFile","Concatenating buffer for box parsing (length: "+t+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},l.prototype.findPosition=function(e,t,i){var r,s=null,o=-1;r=!0===e?0:this.bufferIndex;while(r<this.buffers.length){if(s=this.buffers[r],!(s.fileStart<=t))break;o=r,i&&(s.fileStart+s.byteLength<=t?s.usedBytes=s.byteLength:s.usedBytes=t-s.fileStart,this.logBufferLevel()),r++}return-1!==o?(s=this.buffers[o],s.fileStart+s.byteLength>=t?(n.debug("MultiBufferStream","Found position in existing buffer #"+o),o):-1):-1},l.prototype.findEndContiguousBuf=function(e){var t,i,r,n=void 0!==e?e:this.bufferIndex;if(i=this.buffers[n],this.buffers.length>n+1)for(t=n+1;t<this.buffers.length;t++){if(r=this.buffers[t],r.fileStart!==i.fileStart+i.byteLength)break;i=r}return i.fileStart+i.byteLength},l.prototype.getEndFilePositionAfter=function(e){var t=this.findPosition(!0,e,!1);return-1!==t?this.findEndContiguousBuf(t):e},l.prototype.addUsedBytes=function(e){this.buffer.usedBytes+=e,this.logBufferLevel()},l.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},l.prototype.seek=function(e,t,i){var r;return r=this.findPosition(t,e,i),-1!==r?(this.buffer=this.buffers[r],this.bufferIndex=r,this.position=e-this.buffer.fileStart,n.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(n.debug("MultiBufferStream","Position "+e+" not found in buffered data"),!1)},l.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},l.prototype.getLength=function(){return this.byteLength},l.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength},t.MultiBufferStream=l;var c=function(){var e=3,t=4,i=5,r=6,s=[];s[e]="ES_Descriptor",s[t]="DecoderConfigDescriptor",s[i]="DecoderSpecificInfo",s[r]="SLConfigDescriptor",this.getDescriptorName=function(e){return s[e]};var o=this,a={};return this.parseOneDescriptor=function(e){var t,i,r,o=0;t=e.readUint8(),r=e.readUint8();while(128&r)o=(o<<7)+(127&r),r=e.readUint8();return o=(o<<7)+(127&r),n.debug("MPEG4DescriptorParser","Found "+(s[t]||"Descriptor "+t)+", size "+o+" at position "+e.getPosition()),i=s[t]?new a[s[t]](o):new a.Descriptor(o),i.parse(e),i},a.Descriptor=function(e,t){this.tag=e,this.size=t,this.descs=[]},a.Descriptor.prototype.parse=function(e){this.data=e.readUint8Array(this.size)},a.Descriptor.prototype.findDescriptor=function(e){for(var t=0;t<this.descs.length;t++)if(this.descs[t].tag==e)return this.descs[t];return null},a.Descriptor.prototype.parseRemainingDescriptors=function(e){var t=e.position;while(e.position<t+this.size){var i=o.parseOneDescriptor(e);this.descs.push(i)}},a.ES_Descriptor=function(t){a.Descriptor.call(this,e,t)},a.ES_Descriptor.prototype=new a.Descriptor,a.ES_Descriptor.prototype.parse=function(e){if(this.ES_ID=e.readUint16(),this.flags=e.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=e.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var t=e.readUint8();this.URL=e.readString(t),this.size-=t+1}else this.URL="";32&this.flags?(this.OCR_ES_ID=e.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(e)},a.ES_Descriptor.prototype.getOTI=function(e){var i=this.findDescriptor(t);return i?i.oti:0},a.ES_Descriptor.prototype.getAudioConfig=function(e){var r=this.findDescriptor(t);if(!r)return null;var n=r.findDescriptor(i);if(n&&n.data){var s=(248&n.data[0])>>3;return 31===s&&n.data.length>=2&&(s=32+((7&n.data[0])<<3)+((224&n.data[1])>>5)),s}return null},a.DecoderConfigDescriptor=function(e){a.Descriptor.call(this,t,e)},a.DecoderConfigDescriptor.prototype=new a.Descriptor,a.DecoderConfigDescriptor.prototype.parse=function(e){this.oti=e.readUint8(),this.streamType=e.readUint8(),this.upStream=0!==(this.streamType>>1&1),this.streamType=this.streamType>>>2,this.bufferSize=e.readUint24(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32(),this.size-=13,this.parseRemainingDescriptors(e)},a.DecoderSpecificInfo=function(e){a.Descriptor.call(this,i,e)},a.DecoderSpecificInfo.prototype=new a.Descriptor,a.SLConfigDescriptor=function(e){a.Descriptor.call(this,r,e)},a.SLConfigDescriptor.prototype=new a.Descriptor,this};t.MPEG4DescriptorParser=c;var d={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,BASIC_BOXES:[{type:"mdat",name:"MediaDataBox"},{type:"idat",name:"ItemDataBox"},{type:"free",name:"FreeSpaceBox"},{type:"skip",name:"FreeSpaceBox"},{type:"meco",name:"AdditionalMetadataContainerBox"},{type:"strk",name:"SubTrackBox"}],FULL_BOXES:[{type:"hmhd",name:"HintMediaHeaderBox"},{type:"nmhd",name:"NullMediaHeaderBox"},{type:"iods",name:"ObjectDescriptorBox"},{type:"xml ",name:"XMLBox"},{type:"bxml",name:"BinaryXMLBox"},{type:"ipro",name:"ItemProtectionBox"},{type:"mere",name:"MetaboxRelationBox"}],CONTAINER_BOXES:[[{type:"moov",name:"CompressedMovieBox"},["trak","pssh"]],[{type:"trak",name:"TrackBox"}],[{type:"edts",name:"EditBox"}],[{type:"mdia",name:"MediaBox"}],[{type:"minf",name:"MediaInformationBox"}],[{type:"dinf",name:"DataInformationBox"}],[{type:"stbl",name:"SampleTableBox"},["sgpd","sbgp"]],[{type:"mvex",name:"MovieExtendsBox"},["trex"]],[{type:"moof",name:"CompressedMovieFragmentBox"},["traf"]],[{type:"traf",name:"TrackFragmentBox"},["trun","sgpd","sbgp"]],[{type:"vttc",name:"VTTCueBox"}],[{type:"tref",name:"TrackReferenceBox"}],[{type:"iref",name:"ItemReferenceBox"}],[{type:"mfra",name:"MovieFragmentRandomAccessBox"},["tfra"]],[{type:"meco",name:"AdditionalMetadataContainerBox"}],[{type:"hnti",name:"trackhintinformation"}],[{type:"hinf",name:"hintstatisticsbox"}],[{type:"strk",name:"SubTrackBox"}],[{type:"strd",name:"SubTrackDefinitionBox"}],[{type:"sinf",name:"ProtectionSchemeInfoBox"}],[{type:"rinf",name:"RestrictedSchemeInfoBox"}],[{type:"schi",name:"SchemeInformationBox"}],[{type:"trgr",name:"TrackGroupBox"}],[{type:"udta",name:"UserDataBox"},["kind"]],[{type:"iprp",name:"ItemPropertiesBox"},["ipma"]],[{type:"ipco",name:"ItemPropertyContainerBox"}],[{type:"grpl",name:"GroupsListBox"}],[{type:"j2kH",name:"J2KHeaderInfoBox"}],[{type:"etyp",name:"ExtendedTypeBox"},["tyco"]]],boxCodes:[],fullBoxCodes:[],containerBoxCodes:[],sampleEntryCodes:{},sampleGroupEntryCodes:[],trackGroupTypes:[],UUIDBoxes:{},UUIDs:[],initialize:function(){d.FullBox.prototype=new d.Box,d.ContainerBox.prototype=new d.Box,d.SampleEntry.prototype=new d.Box,d.TrackGroupTypeBox.prototype=new d.FullBox,d.BASIC_BOXES.forEach((function(e){d.createBoxCtor(e.type,e.name)})),d.FULL_BOXES.forEach((function(e){d.createFullBoxCtor(e.type,e.name)})),d.CONTAINER_BOXES.forEach((function(e){d.createContainerBoxCtor(e[0].type,e[0].name,null,e[1])}))},Box:function(e,t,i,r){this.type=e,this.box_name=i,this.size=t,this.uuid=r},FullBox:function(e,t,i,r){d.Box.call(this,e,t,i,r),this.flags=0,this.version=0},ContainerBox:function(e,t,i,r){d.Box.call(this,e,t,i,r),this.boxes=[]},SampleEntry:function(e,t,i,r){d.ContainerBox.call(this,e,t),this.hdr_size=i,this.start=r},SampleGroupEntry:function(e){this.grouping_type=e},TrackGroupTypeBox:function(e,t){d.FullBox.call(this,e,t)},createBoxCtor:function(e,t,i){d.boxCodes.push(e),d[e+"Box"]=function(i){d.Box.call(this,e,i,t)},d[e+"Box"].prototype=new d.Box,i&&(d[e+"Box"].prototype.parse=i)},createFullBoxCtor:function(e,t,i){d[e+"Box"]=function(i){d.FullBox.call(this,e,i,t)},d[e+"Box"].prototype=new d.FullBox,d[e+"Box"].prototype.parse=function(e){this.parseFullHeader(e),i&&i.call(this,e)}},addSubBoxArrays:function(e){if(e){this.subBoxNames=e;for(var t=e.length,i=0;i<t;i++)this[e[i]+"s"]=[]}},createContainerBoxCtor:function(e,t,i,r){d[e+"Box"]=function(i){d.ContainerBox.call(this,e,i,t),d.addSubBoxArrays.call(this,r)},d[e+"Box"].prototype=new d.ContainerBox,i&&(d[e+"Box"].prototype.parse=i)},createMediaSampleEntryCtor:function(e,t,i){d.sampleEntryCodes[e]=[],d[e+"SampleEntry"]=function(e,t){d.SampleEntry.call(this,e,t),d.addSubBoxArrays.call(this,i)},d[e+"SampleEntry"].prototype=new d.SampleEntry,t&&(d[e+"SampleEntry"].prototype.parse=t)},createSampleEntryCtor:function(e,t,i,r){d.sampleEntryCodes[e].push(t),d[t+"SampleEntry"]=function(i){d[e+"SampleEntry"].call(this,t,i),d.addSubBoxArrays.call(this,r)},d[t+"SampleEntry"].prototype=new d[e+"SampleEntry"],i&&(d[t+"SampleEntry"].prototype.parse=i)},createEncryptedSampleEntryCtor:function(e,t,i){d.createSampleEntryCtor.call(this,e,t,i,["sinf"])},createSampleGroupCtor:function(e,t){d[e+"SampleGroupEntry"]=function(t){d.SampleGroupEntry.call(this,e,t)},d[e+"SampleGroupEntry"].prototype=new d.SampleGroupEntry,t&&(d[e+"SampleGroupEntry"].prototype.parse=t)},createTrackGroupCtor:function(e,t){d[e+"TrackGroupTypeBox"]=function(t){d.TrackGroupTypeBox.call(this,e,t)},d[e+"TrackGroupTypeBox"].prototype=new d.TrackGroupTypeBox,t&&(d[e+"TrackGroupTypeBox"].prototype.parse=t)},createUUIDBox:function(e,t,i,r,n){d.UUIDs.push(e),d.UUIDBoxes[e]=function(n){i?d.FullBox.call(this,"uuid",n,t,e):r?d.ContainerBox.call(this,"uuid",n,t,e):d.Box.call(this,"uuid",n,t,e)},d.UUIDBoxes[e].prototype=i?new d.FullBox:r?new d.ContainerBox:new d.Box,n&&(d.UUIDBoxes[e].prototype.parse=i?function(e){this.parseFullHeader(e),n&&n.call(this,e)}:n)}};function u(e){var t="<table class='inner-table'>";t+="<thead><tr><th>length</th><th>nalu_data</th></tr></thead>",t+="<tbody>";for(var i=0;i<e.length;i++){var r=e[i];t+="<tr>",t+="<td>"+r.length+"</td>",t+="<td>",t+=r.nalu.reduce((function(e,t){return e+t.toString(16).padStart(2,"0")}),"0x"),t+="</td></tr>"}return t+="</tbody></table>",t}function h(e,t){this.x=e,this.y=t}function p(e,t){this.bad_pixel_row=e,this.bad_pixel_column=t}d.initialize(),d.TKHD_FLAG_ENABLED=1,d.TKHD_FLAG_IN_MOVIE=2,d.TKHD_FLAG_IN_PREVIEW=4,d.TFHD_FLAG_BASE_DATA_OFFSET=1,d.TFHD_FLAG_SAMPLE_DESC=2,d.TFHD_FLAG_SAMPLE_DUR=8,d.TFHD_FLAG_SAMPLE_SIZE=16,d.TFHD_FLAG_SAMPLE_FLAGS=32,d.TFHD_FLAG_DUR_EMPTY=65536,d.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,d.TRUN_FLAGS_DATA_OFFSET=1,d.TRUN_FLAGS_FIRST_FLAG=4,d.TRUN_FLAGS_DURATION=256,d.TRUN_FLAGS_SIZE=512,d.TRUN_FLAGS_FLAGS=1024,d.TRUN_FLAGS_CTS_OFFSET=2048,d.Box.prototype.add=function(e){return this.addBox(new d[e+"Box"])},d.Box.prototype.addBox=function(e){return this.boxes.push(e),this[e.type+"s"]?this[e.type+"s"].push(e):this[e.type]=e,e},d.Box.prototype.set=function(e,t){return this[e]=t,this},d.Box.prototype.addEntry=function(e,t){var i=t||"entries";return this[i]||(this[i]=[]),this[i].push(e),this},t.BoxParser=d,d.parseUUID=function(e){return d.parseHex16(e)},d.parseHex16=function(e){for(var t="",i=0;i<16;i++){var r=e.readUint8().toString(16);t+=1===r.length?"0"+r:r}return t},d.parseOneBox=function(e,t,i){var r,s,o,a=e.getPosition(),l=0;if(e.getEndPosition()-a<8)return n.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:d.ERR_NOT_ENOUGH_DATA};if(i&&i<8)return n.debug("BoxParser","Not enough bytes left in the parent box to parse a new box"),{code:d.ERR_NOT_ENOUGH_DATA};var c=e.readUint32(),u=e.readString(4),h=u;if(n.debug("BoxParser","Found box of type '"+u+"' and size "+c+" at position "+a),l=8,"uuid"==u){if(e.getEndPosition()-e.getPosition()<16||i-l<16)return e.seek(a),n.debug("BoxParser","Not enough bytes left in the parent box to parse a UUID box"),{code:d.ERR_NOT_ENOUGH_DATA};o=d.parseUUID(e),l+=16,h=o}if(1==c){if(e.getEndPosition()-e.getPosition()<8||i&&i-l<8)return e.seek(a),n.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+u+'" box'),{code:d.ERR_NOT_ENOUGH_DATA};c=e.readUint64(),l+=8}else if(0===c)if(i)c=i;else if("mdat"!==u)return n.error("BoxParser","Unlimited box size not supported for type: '"+u+"'"),r=new d.Box(u,c),{code:d.OK,box:r,size:r.size};return 0!==c&&c<l?(n.error("BoxParser","Box of type "+u+" has an invalid size "+c+" (too small to be a box)"),{code:d.ERR_NOT_ENOUGH_DATA,type:u,size:c,hdr_size:l,start:a}):0!==c&&i&&c>i?(n.error("BoxParser","Box of type '"+u+"' has a size "+c+" greater than its container size "+i),{code:d.ERR_NOT_ENOUGH_DATA,type:u,size:c,hdr_size:l,start:a}):0!==c&&a+c>e.getEndPosition()?(e.seek(a),n.info("BoxParser","Not enough data in stream to parse the entire '"+u+"' box"),{code:d.ERR_NOT_ENOUGH_DATA,type:u,size:c,hdr_size:l,start:a}):t?{code:d.OK,type:u,size:c,hdr_size:l,start:a}:(d[u+"Box"]?r=new d[u+"Box"](c):"uuid"!==u?(n.warn("BoxParser","Unknown box type: '"+u+"'"),r=new d.Box(u,c),r.has_unparsed_data=!0):d.UUIDBoxes[o]?r=new d.UUIDBoxes[o](c):(n.warn("BoxParser","Unknown uuid type: '"+o+"'"),r=new d.Box(u,c),r.uuid=o,r.has_unparsed_data=!0),r.hdr_size=l,r.start=a,r.write===d.Box.prototype.write&&"mdat"!==r.type&&(n.info("BoxParser","'"+h+"' box writing not yet implemented, keeping unparsed data in memory for later write"),r.parseDataAndRewind(e)),r.parse(e),s=e.getPosition()-(r.start+r.size),s<0?(n.warn("BoxParser","Parsing of box '"+h+"' did not read the entire indicated box data size (missing "+-s+" bytes), seeking forward"),e.seek(r.start+r.size)):s>0&&(n.error("BoxParser","Parsing of box '"+h+"' read "+s+" more bytes than the indicated box data size, seeking backwards"),0!==r.size&&e.seek(r.start+r.size)),{code:d.OK,box:r,size:r.size})},d.Box.prototype.parse=function(e){"mdat"!=this.type?this.data=e.readUint8Array(this.size-this.hdr_size):0===this.size?e.seek(e.getEndPosition()):e.seek(this.start+this.size)},d.Box.prototype.parseDataAndRewind=function(e){this.data=e.readUint8Array(this.size-this.hdr_size),e.position-=this.size-this.hdr_size},d.FullBox.prototype.parseDataAndRewind=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,e.position-=this.size-this.hdr_size},d.FullBox.prototype.parseFullHeader=function(e){this.version=e.readUint8(),this.flags=e.readUint24(),this.hdr_size+=4},d.FullBox.prototype.parse=function(e){this.parseFullHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},d.ContainerBox.prototype.parse=function(e){var t,i;while(e.getPosition()<this.start+this.size){if(t=d.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),t.code!==d.OK)return;if(i=t.box,this.boxes.push(i),this.subBoxNames&&-1!=this.subBoxNames.indexOf(i.type))this[this.subBoxNames[this.subBoxNames.indexOf(i.type)]+"s"].push(i);else{var r="uuid"!==i.type?i.type:i.uuid;this[r]?n.warn("Box of type "+r+" already stored in field of this type"):this[r]=i}}},d.Box.prototype.parseLanguage=function(e){this.language=e.readUint16();var t=[];t[0]=this.language>>10&31,t[1]=this.language>>5&31,t[2]=31&this.language,this.languageString=String.fromCharCode(t[0]+96,t[1]+96,t[2]+96)},d.SAMPLE_ENTRY_TYPE_VISUAL="Visual",d.SAMPLE_ENTRY_TYPE_AUDIO="Audio",d.SAMPLE_ENTRY_TYPE_HINT="Hint",d.SAMPLE_ENTRY_TYPE_METADATA="Metadata",d.SAMPLE_ENTRY_TYPE_SUBTITLE="Subtitle",d.SAMPLE_ENTRY_TYPE_SYSTEM="System",d.SAMPLE_ENTRY_TYPE_TEXT="Text",d.SampleEntry.prototype.parseHeader=function(e){e.readUint8Array(6),this.data_reference_index=e.readUint16(),this.hdr_size+=8},d.SampleEntry.prototype.parse=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size)},d.SampleEntry.prototype.parseDataAndRewind=function(e){this.parseHeader(e),this.data=e.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,e.position-=this.size-this.hdr_size},d.SampleEntry.prototype.parseFooter=function(e){d.ContainerBox.prototype.parse.call(this,e)},d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_HINT),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_METADATA),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SYSTEM),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_TEXT),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,(function(e){var t;this.parseHeader(e),e.readUint16(),e.readUint16(),e.readUint32Array(3),this.width=e.readUint16(),this.height=e.readUint16(),this.horizresolution=e.readUint32(),this.vertresolution=e.readUint32(),e.readUint32(),this.frame_count=e.readUint16(),t=Math.min(31,e.readUint8()),this.compressorname=e.readString(t),t<31&&e.readString(31-t),this.depth=e.readUint16(),e.readUint16(),this.parseFooter(e)})),d.createMediaSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,(function(e){this.parseHeader(e),e.readUint32Array(2),this.channel_count=e.readUint16(),this.samplesize=e.readUint16(),e.readUint16(),e.readUint16(),this.samplerate=e.readUint32()/65536,this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"avc1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"avc2"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"avc3"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"avc4"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"av01"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"dav1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"hvc1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"hev1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"hvt1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"lhe1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"dvh1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"dvhe"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vvc1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vvi1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vvs1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vvcN"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vp08"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"vp09"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"avs3"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"j2ki"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"mjp2"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"mjpg"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"uncv"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"mp4a"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"ac-3"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"ac-4"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"ec-3"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"Opus"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"mha1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"mha2"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"mhm1"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"mhm2"),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"fLaC"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_VISUAL,"encv"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_AUDIO,"enca"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE,"encu"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SYSTEM,"encs"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_TEXT,"enct"),d.createEncryptedSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_METADATA,"encm"),d.createBoxCtor("a1lx","AV1LayeredImageIndexingProperty",(function(e){var t=1&e.readUint8(),i=16*(1+(1&t));this.layer_size=[];for(var r=0;r<3;r++)this.layer_size[r]=16==i?e.readUint16():e.readUint32()})),d.createBoxCtor("a1op","OperatingPointSelectorProperty",(function(e){this.op_index=e.readUint8()})),d.createFullBoxCtor("auxC","AuxiliaryTypeProperty",(function(e){this.aux_type=e.readCString();var t=this.size-this.hdr_size-(this.aux_type.length+1);this.aux_subtype=e.readUint8Array(t)})),d.createBoxCtor("av1C","AV1CodecConfigurationBox",(function(e){var t=e.readUint8();if(1===(t>>7&1))if(this.version=127&t,1===this.version)if(t=e.readUint8(),this.seq_profile=t>>5&7,this.seq_level_idx_0=31&t,t=e.readUint8(),this.seq_tier_0=t>>7&1,this.high_bitdepth=t>>6&1,this.twelve_bit=t>>5&1,this.monochrome=t>>4&1,this.chroma_subsampling_x=t>>3&1,this.chroma_subsampling_y=t>>2&1,this.chroma_sample_position=3&t,t=e.readUint8(),this.reserved_1=t>>5&7,0===this.reserved_1){if(this.initial_presentation_delay_present=t>>4&1,1===this.initial_presentation_delay_present)this.initial_presentation_delay_minus_one=15&t;else if(this.reserved_2=15&t,0!==this.reserved_2)return void n.error("av1C reserved_2 parsing problem");var i=this.size-this.hdr_size-4;this.configOBUs=e.readUint8Array(i)}else n.error("av1C reserved_1 parsing problem");else n.error("av1C version "+this.version+" not supported");else n.error("av1C marker problem")})),d.createBoxCtor("avcC","AVCConfigurationBox",(function(e){var t,i;for(this.configurationVersion=e.readUint8(),this.AVCProfileIndication=e.readUint8(),this.profile_compatibility=e.readUint8(),this.AVCLevelIndication=e.readUint8(),this.lengthSizeMinusOne=3&e.readUint8(),this.nb_SPS_nalus=31&e.readUint8(),i=this.size-this.hdr_size-6,this.SPS=[],this.SPS.toString=function(){return u(this)},t=0;t<this.nb_SPS_nalus;t++)this.SPS[t]={},this.SPS[t].length=e.readUint16(),this.SPS[t].nalu=e.readUint8Array(this.SPS[t].length),i-=2+this.SPS[t].length;for(this.nb_PPS_nalus=e.readUint8(),i--,this.PPS=[],this.PPS.toString=function(){return u(this)},t=0;t<this.nb_PPS_nalus;t++)this.PPS[t]={},this.PPS[t].length=e.readUint16(),this.PPS[t].nalu=e.readUint8Array(this.PPS[t].length),i-=2+this.PPS[t].length;i>0&&(this.ext=e.readUint8Array(i))})),d.createBoxCtor("btrt","BitRateBox",(function(e){this.bufferSizeDB=e.readUint32(),this.maxBitrate=e.readUint32(),this.avgBitrate=e.readUint32()})),d.createFullBoxCtor("ccst","CodingConstraintsBox",(function(e){var t=e.readUint8();this.all_ref_pics_intra=128==(128&t),this.intra_pred_used=64==(64&t),this.max_ref_per_pic=(63&t)>>2,e.readUint24()})),d.createBoxCtor("cdef","ComponentDefinitionBox",(function(e){var t;for(this.channel_count=e.readUint16(),this.channel_indexes=[],this.channel_types=[],this.channel_associations=[],t=0;t<this.channel_count;t++)this.channel_indexes.push(e.readUint16()),this.channel_types.push(e.readUint16()),this.channel_associations.push(e.readUint16())})),d.createBoxCtor("clap","CleanApertureBox",(function(e){this.cleanApertureWidthN=e.readUint32(),this.cleanApertureWidthD=e.readUint32(),this.cleanApertureHeightN=e.readUint32(),this.cleanApertureHeightD=e.readUint32(),this.horizOffN=e.readUint32(),this.horizOffD=e.readUint32(),this.vertOffN=e.readUint32(),this.vertOffD=e.readUint32()})),d.createBoxCtor("clli","ContentLightLevelBox",(function(e){this.max_content_light_level=e.readUint16(),this.max_pic_average_light_level=e.readUint16()})),d.createFullBoxCtor("cmex","CameraExtrinsicMatrixProperty",(function(e){1&this.flags&&(this.pos_x=e.readInt32()),2&this.flags&&(this.pos_y=e.readInt32()),4&this.flags&&(this.pos_z=e.readInt32()),8&this.flags&&(0==this.version?16&this.flags?(this.quat_x=e.readInt32(),this.quat_y=e.readInt32(),this.quat_z=e.readInt32()):(this.quat_x=e.readInt16(),this.quat_y=e.readInt16(),this.quat_z=e.readInt16()):this.version),32&this.flags&&(this.id=e.readUint32())})),d.createFullBoxCtor("cmin","CameraIntrinsicMatrixProperty",(function(e){this.focal_length_x=e.readInt32(),this.principal_point_x=e.readInt32(),this.principal_point_y=e.readInt32(),1&this.flags&&(this.focal_length_y=e.readInt32(),this.skew_factor=e.readInt32())})),d.createBoxCtor("cmpd","ComponentDefinitionBox",(function(e){for(this.component_count=e.readUint32(),this.component_types=[],this.component_type_urls=[],i=0;i<this.component_count;i++){var t=e.readUint16();this.component_types.push(t),t>=32768&&this.component_type_urls.push(e.readCString())}})),d.createFullBoxCtor("co64","ChunkLargeOffsetBox",(function(e){var t,i;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(i=0;i<t;i++)this.chunk_offsets.push(e.readUint64())})),d.createFullBoxCtor("CoLL","ContentLightLevelBox",(function(e){this.maxCLL=e.readUint16(),this.maxFALL=e.readUint16()})),d.createBoxCtor("colr","ColourInformationBox",(function(e){if(this.colour_type=e.readString(4),"nclx"===this.colour_type){this.colour_primaries=e.readUint16(),this.transfer_characteristics=e.readUint16(),this.matrix_coefficients=e.readUint16();var t=e.readUint8();this.full_range_flag=t>>7}else("rICC"===this.colour_type||"prof"===this.colour_type)&&(this.ICC_profile=e.readUint8Array(this.size-4))})),d.createFullBoxCtor("cprt","CopyrightBox",(function(e){this.parseLanguage(e),this.notice=e.readCString()})),d.createFullBoxCtor("cslg","CompositionToDecodeBox",(function(e){0===this.version&&(this.compositionToDTSShift=e.readInt32(),this.leastDecodeToDisplayDelta=e.readInt32(),this.greatestDecodeToDisplayDelta=e.readInt32(),this.compositionStartTime=e.readInt32(),this.compositionEndTime=e.readInt32())})),d.createFullBoxCtor("ctts","CompositionOffsetBox",(function(e){var t,i;if(t=e.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(i=0;i<t;i++){this.sample_counts.push(e.readUint32());var r=e.readInt32();r<0&&n.warn("BoxParser","ctts box uses negative values without using version 1"),this.sample_offsets.push(r)}else if(1==this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),this.sample_offsets.push(e.readInt32())})),d.createBoxCtor("dac3","AC3SpecificBox",(function(e){var t=e.readUint8(),i=e.readUint8(),r=e.readUint8();this.fscod=t>>6,this.bsid=t>>1&31,this.bsmod=(1&t)<<2|i>>6&3,this.acmod=i>>3&7,this.lfeon=i>>2&1,this.bit_rate_code=3&i|r>>5&7})),d.createBoxCtor("dec3","EC3SpecificBox",(function(e){var t=e.readUint16();this.data_rate=t>>3,this.num_ind_sub=7&t,this.ind_subs=[];for(var i=0;i<this.num_ind_sub+1;i++){var r={};this.ind_subs.push(r);var n=e.readUint8(),s=e.readUint8(),o=e.readUint8();r.fscod=n>>6,r.bsid=n>>1&31,r.bsmod=(1&n)<<4|s>>4&15,r.acmod=s>>1&7,r.lfeon=1&s,r.num_dep_sub=o>>1&15,r.num_dep_sub>0&&(r.chan_loc=(1&o)<<8|e.readUint8())}})),d.createFullBoxCtor("dfLa","FLACSpecificBox",(function(e){var t=127,i=128,r=[],n=["STREAMINFO","PADDING","APPLICATION","SEEKTABLE","VORBIS_COMMENT","CUESHEET","PICTURE","RESERVED"];do{var s=e.readUint8(),o=Math.min(s&t,n.length-1);if(o?e.readUint8Array(e.readUint24()):(e.readUint8Array(13),this.samplerate=e.readUint32()>>12,e.readUint8Array(20)),r.push(n[o]),s&i)break}while(1);this.numMetadataBlocks=r.length+" ("+r.join(", ")+")"})),d.createBoxCtor("dimm","hintimmediateBytesSent",(function(e){this.bytessent=e.readUint64()})),d.createBoxCtor("dmax","hintlongestpacket",(function(e){this.time=e.readUint32()})),d.createBoxCtor("dmed","hintmediaBytesSent",(function(e){this.bytessent=e.readUint64()})),d.createBoxCtor("dOps","OpusSpecificBox",(function(e){if(this.Version=e.readUint8(),this.OutputChannelCount=e.readUint8(),this.PreSkip=e.readUint16(),this.InputSampleRate=e.readUint32(),this.OutputGain=e.readInt16(),this.ChannelMappingFamily=e.readUint8(),0!==this.ChannelMappingFamily){this.StreamCount=e.readUint8(),this.CoupledCount=e.readUint8(),this.ChannelMapping=[];for(var t=0;t<this.OutputChannelCount;t++)this.ChannelMapping[t]=e.readUint8()}})),d.createFullBoxCtor("dref","DataReferenceBox",(function(e){var t,i;this.entries=[];for(var r=e.readUint32(),n=0;n<r;n++){if(t=d.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),t.code!==d.OK)return;i=t.box,this.entries.push(i)}})),d.createBoxCtor("drep","hintrepeatedBytesSent",(function(e){this.bytessent=e.readUint64()})),d.createFullBoxCtor("elng","ExtendedLanguageBox",(function(e){this.extended_language=e.readString(this.size-this.hdr_size)})),d.createFullBoxCtor("elst","EditListBox",(function(e){this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),1===this.version?(r.segment_duration=e.readUint64(),r.media_time=e.readInt64()):(r.segment_duration=e.readUint32(),r.media_time=e.readInt32()),r.media_rate_integer=e.readInt16(),r.media_rate_fraction=e.readInt16()}})),d.createFullBoxCtor("emsg","EventMessageBox",(function(e){1==this.version?(this.timescale=e.readUint32(),this.presentation_time=e.readUint64(),this.event_duration=e.readUint32(),this.id=e.readUint32(),this.scheme_id_uri=e.readCString(),this.value=e.readCString()):(this.scheme_id_uri=e.readCString(),this.value=e.readCString(),this.timescale=e.readUint32(),this.presentation_time_delta=e.readUint32(),this.event_duration=e.readUint32(),this.id=e.readUint32());var t=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));1==this.version&&(t-=4),this.message_data=e.readUint8Array(t)})),d.createEntityToGroupCtor=function(e,t){d[e+"Box"]=function(t){d.FullBox.call(this,e,t)},d[e+"Box"].prototype=new d.FullBox,d[e+"Box"].prototype.parse=function(e){if(this.parseFullHeader(e),t)t.call(this,e);else for(this.group_id=e.readUint32(),this.num_entities_in_group=e.readUint32(),this.entity_ids=[],i=0;i<this.num_entities_in_group;i++){var r=e.readUint32();this.entity_ids.push(r)}}},d.createEntityToGroupCtor("aebr"),d.createEntityToGroupCtor("afbr"),d.createEntityToGroupCtor("albc"),d.createEntityToGroupCtor("altr"),d.createEntityToGroupCtor("brst"),d.createEntityToGroupCtor("dobr"),d.createEntityToGroupCtor("eqiv"),d.createEntityToGroupCtor("favc"),d.createEntityToGroupCtor("fobr"),d.createEntityToGroupCtor("iaug"),d.createEntityToGroupCtor("pano"),d.createEntityToGroupCtor("slid"),d.createEntityToGroupCtor("ster"),d.createEntityToGroupCtor("tsyn"),d.createEntityToGroupCtor("wbbr"),d.createEntityToGroupCtor("prgr"),d.createEntityToGroupCtor("pymd",(function(e){this.group_id=e.readUint32(),this.num_entities_in_group=e.readUint32(),this.entity_ids=[];for(var t=0;t<this.num_entities_in_group;t++){var i=e.readUint32();this.entity_ids.push(i)}for(this.tile_size_x=e.readUint16(),this.tile_size_y=e.readUint16(),this.layer_binning=[],this.tiles_in_layer_column_minus1=[],this.tiles_in_layer_row_minus1=[],t=0;t<this.num_entities_in_group;t++)this.layer_binning[t]=e.readUint16(),this.tiles_in_layer_row_minus1[t]=e.readUint16(),this.tiles_in_layer_column_minus1[t]=e.readUint16()})),d.createFullBoxCtor("esds","ElementaryStreamDescriptorBox",(function(e){var t=e.readUint8Array(this.size-this.hdr_size);if("undefined"!==typeof c){var i=new c;this.esd=i.parseOneDescriptor(new o(t.buffer,0,o.BIG_ENDIAN))}})),d.createBoxCtor("fiel","FieldHandlingBox",(function(e){this.fieldCount=e.readUint8(),this.fieldOrdering=e.readUint8()})),d.createBoxCtor("frma","OriginalFormatBox",(function(e){this.data_format=e.readString(4)})),d.createBoxCtor("ftyp","FileTypeBox",(function(e){var t=this.size-this.hdr_size;this.major_brand=e.readString(4),this.minor_version=e.readUint32(),t-=8,this.compatible_brands=[];var i=0;while(t>=4)this.compatible_brands[i]=e.readString(4),t-=4,i++})),d.createFullBoxCtor("hdlr","HandlerBox",(function(e){0===this.version&&(e.readUint32(),this.handler=e.readString(4),e.readUint32Array(3),this.name=e.readString(this.size-this.hdr_size-20),"\0"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))})),d.createBoxCtor("hvcC","HEVCConfigurationBox",(function(e){var t,i,r,n;this.configurationVersion=e.readUint8(),n=e.readUint8(),this.general_profile_space=n>>6,this.general_tier_flag=(32&n)>>5,this.general_profile_idc=31&n,this.general_profile_compatibility=e.readUint32(),this.general_constraint_indicator=e.readUint8Array(6),this.general_level_idc=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),this.chroma_format_idc=3&e.readUint8(),this.bit_depth_luma_minus8=7&e.readUint8(),this.bit_depth_chroma_minus8=7&e.readUint8(),this.avgFrameRate=e.readUint16(),n=e.readUint8(),this.constantFrameRate=n>>6,this.numTemporalLayers=(13&n)>>3,this.temporalIdNested=(4&n)>>2,this.lengthSizeMinusOne=3&n,this.nalu_arrays=[],this.nalu_arrays.toString=function(){var e="<table class='inner-table'>";e+="<thead><tr><th>completeness</th><th>nalu_type</th><th>nalu_data</th></tr></thead>",e+="<tbody>";for(var t=0;t<this.length;t++){var i=this[t];e+="<tr>",e+="<td rowspan='"+i.length+"'>"+i.completeness+"</td>",e+="<td rowspan='"+i.length+"'>"+i.nalu_type+"</td>";for(var r=0;r<i.length;r++){var n=i[r];0!==r&&(e+="<tr>"),e+="<td>",e+=n.data.reduce((function(e,t){return e+t.toString(16).padStart(2,"0")}),"0x"),e+="</td></tr>"}}return e+="</tbody></table>",e};var s=e.readUint8();for(t=0;t<s;t++){var o=[];this.nalu_arrays.push(o),n=e.readUint8(),o.completeness=(128&n)>>7,o.nalu_type=63&n;var a=e.readUint16();for(i=0;i<a;i++){var l={};o.push(l),r=e.readUint16(),l.data=e.readUint8Array(r)}}})),d.createFullBoxCtor("iinf","ItemInfoBox",(function(e){var t;0===this.version?this.entry_count=e.readUint16():this.entry_count=e.readUint32(),this.item_infos=[];for(var i=0;i<this.entry_count;i++){if(t=d.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),t.code!==d.OK)return;"infe"!==t.box.type&&n.error("BoxParser","Expected 'infe' box, got "+t.box.type),this.item_infos[i]=t.box}})),d.createFullBoxCtor("iloc","ItemLocationBox",(function(e){var t;t=e.readUint8(),this.offset_size=t>>4&15,this.length_size=15&t,t=e.readUint8(),this.base_offset_size=t>>4&15,1===this.version||2===this.version?this.index_size=15&t:this.index_size=0,this.items=[];var i=0;if(this.version<2)i=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";i=e.readUint32()}for(var r=0;r<i;r++){var n={};if(this.items.push(n),this.version<2)n.item_ID=e.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";n.item_ID=e.readUint32()}switch(1===this.version||2===this.version?n.construction_method=15&e.readUint16():n.construction_method=0,n.data_reference_index=e.readUint16(),this.base_offset_size){case 0:n.base_offset=0;break;case 4:n.base_offset=e.readUint32();break;case 8:n.base_offset=e.readUint64();break;default:throw"Error reading base offset size"}var s=e.readUint16();n.extents=[];for(var o=0;o<s;o++){var a={};if(n.extents.push(a),1===this.version||2===this.version)switch(this.index_size){case 0:a.extent_index=0;break;case 4:a.extent_index=e.readUint32();break;case 8:a.extent_index=e.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:a.extent_offset=0;break;case 4:a.extent_offset=e.readUint32();break;case 8:a.extent_offset=e.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:a.extent_length=0;break;case 4:a.extent_length=e.readUint32();break;case 8:a.extent_length=e.readUint64();break;default:throw"Error reading extent index"}}}})),d.createBoxCtor("imir","ImageMirror",(function(e){var t=e.readUint8();this.reserved=t>>7,this.axis=1&t})),d.createFullBoxCtor("infe","ItemInfoEntry",(function(e){if(0!==this.version&&1!==this.version||(this.item_ID=e.readUint16(),this.item_protection_index=e.readUint16(),this.item_name=e.readCString(),this.content_type=e.readCString(),this.content_encoding=e.readCString()),1===this.version)return this.extension_type=e.readString(4),n.warn("BoxParser","Cannot parse extension type"),void e.seek(this.start+this.size);this.version>=2&&(2===this.version?this.item_ID=e.readUint16():3===this.version&&(this.item_ID=e.readUint32()),this.item_protection_index=e.readUint16(),this.item_type=e.readString(4),this.item_name=e.readCString(),"mime"===this.item_type?(this.content_type=e.readCString(),this.content_encoding=e.readCString()):"uri "===this.item_type&&(this.item_uri_type=e.readCString()))})),d.createFullBoxCtor("ipma","ItemPropertyAssociationBox",(function(e){var t,i;for(entry_count=e.readUint32(),this.associations=[],t=0;t<entry_count;t++){var r={};this.associations.push(r),this.version<1?r.id=e.readUint16():r.id=e.readUint32();var n=e.readUint8();for(r.props=[],i=0;i<n;i++){var s=e.readUint8(),o={};r.props.push(o),o.essential=(128&s)>>7===1,1&this.flags?o.property_index=(127&s)<<8|e.readUint8():o.property_index=127&s}}})),d.createFullBoxCtor("iref","ItemReferenceBox",(function(e){var t,i;this.references=[];while(e.getPosition()<this.start+this.size){if(t=d.parseOneBox(e,!0,this.size-(e.getPosition()-this.start)),t.code!==d.OK)return;i=0===this.version?new d.SingleItemTypeReferenceBox(t.type,t.size,t.hdr_size,t.start):new d.SingleItemTypeReferenceBoxLarge(t.type,t.size,t.hdr_size,t.start),i.write===d.Box.prototype.write&&"mdat"!==i.type&&(n.warn("BoxParser",i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.references.push(i)}})),d.createBoxCtor("irot","ImageRotation",(function(e){this.angle=3&e.readUint8()})),d.createFullBoxCtor("ispe","ImageSpatialExtentsProperty",(function(e){this.image_width=e.readUint32(),this.image_height=e.readUint32()})),d.createFullBoxCtor("kind","KindBox",(function(e){this.schemeURI=e.readCString(),this.value=e.readCString()})),d.createFullBoxCtor("leva","LevelAssignmentBox",(function(e){var t=e.readUint8();this.levels=[];for(var i=0;i<t;i++){var r={};this.levels[i]=r,r.track_ID=e.readUint32();var s=e.readUint8();switch(r.padding_flag=s>>7,r.assignment_type=127&s,r.assignment_type){case 0:r.grouping_type=e.readString(4);break;case 1:r.grouping_type=e.readString(4),r.grouping_type_parameter=e.readUint32();break;case 2:break;case 3:break;case 4:r.sub_track_id=e.readUint32();break;default:n.warn("BoxParser","Unknown leva assignement type")}}})),d.createBoxCtor("lhvC","LHEVCConfigurationBox",(function(e){var t,i,r;this.configurationVersion=e.readUint8(),this.min_spatial_segmentation_idc=4095&e.readUint16(),this.parallelismType=3&e.readUint8(),r=e.readUint8(),this.numTemporalLayers=(13&r)>>3,this.temporalIdNested=(4&r)>>2,this.lengthSizeMinusOne=3&r,this.nalu_arrays=[],this.nalu_arrays.toString=function(){var e="<table class='inner-table'>";e+="<thead><tr><th>completeness</th><th>nalu_type</th><th>nalu_data</th></tr></thead>",e+="<tbody>";for(var t=0;t<this.length;t++){var i=this[t];e+="<tr>",e+="<td rowspan='"+i.length+"'>"+i.completeness+"</td>",e+="<td rowspan='"+i.length+"'>"+i.nalu_type+"</td>";for(var r=0;r<i.length;r++){var n=i[r];0!==r&&(e+="<tr>"),e+="<td>",e+=n.data.reduce((function(e,t){return e+t.toString(16).padStart(2,"0")}),"0x"),e+="</td></tr>"}}return e+="</tbody></table>",e};var n=e.readUint8();for(t=0;t<n;t++){var s=[];this.nalu_arrays.push(s),r=e.readUint8(),s.completeness=(128&r)>>7,s.nalu_type=63&r;var o=e.readUint16();for(i=0;i<o;i++){var a={};s.push(a);var l=e.readUint16();a.data=e.readUint8Array(l)}}})),d.createBoxCtor("lsel","LayerSelectorProperty",(function(e){this.layer_id=e.readUint16()})),d.createBoxCtor("maxr","hintmaxrate",(function(e){this.period=e.readUint32(),this.bytes=e.readUint32()})),h.prototype.toString=function(){return"("+this.x+","+this.y+")"},d.createBoxCtor("mdcv","MasteringDisplayColourVolumeBox",(function(e){this.display_primaries=[],this.display_primaries[0]=new h(e.readUint16(),e.readUint16()),this.display_primaries[1]=new h(e.readUint16(),e.readUint16()),this.display_primaries[2]=new h(e.readUint16(),e.readUint16()),this.white_point=new h(e.readUint16(),e.readUint16()),this.max_display_mastering_luminance=e.readUint32(),this.min_display_mastering_luminance=e.readUint32()})),d.createFullBoxCtor("mdhd","MediaHeaderBox",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.parseLanguage(e),e.readUint16()})),d.createFullBoxCtor("mehd","MovieExtendsHeaderBox",(function(e){1&this.flags&&(n.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=e.readUint64():this.fragment_duration=e.readUint32()})),d.createFullBoxCtor("meta","MetaBox",(function(e){this.boxes=[],d.ContainerBox.prototype.parse.call(this,e)})),d.createFullBoxCtor("mfhd","MovieFragmentHeaderBox",(function(e){this.sequence_number=e.readUint32()})),d.createFullBoxCtor("mfro","MovieFragmentRandomAccessOffsetBox",(function(e){this._size=e.readUint32()})),d.createFullBoxCtor("mskC","MaskConfigurationProperty",(function(e){this.bits_per_pixel=e.readUint8()})),d.createFullBoxCtor("mvhd","MovieHeaderBox",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.timescale=e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.timescale=e.readUint32(),this.duration=e.readUint32()),this.rate=e.readUint32(),this.volume=e.readUint16()>>8,e.readUint16(),e.readUint32Array(2),this.matrix=e.readUint32Array(9),e.readUint32Array(6),this.next_track_id=e.readUint32()})),d.createBoxCtor("npck","hintPacketsSent",(function(e){this.packetssent=e.readUint32()})),d.createBoxCtor("nump","hintPacketsSent",(function(e){this.packetssent=e.readUint64()})),d.createFullBoxCtor("padb","PaddingBitsBox",(function(e){var t=e.readUint32();this.padbits=[];for(var i=0;i<Math.floor((t+1)/2);i++)this.padbits=e.readUint8()})),d.createBoxCtor("pasp","PixelAspectRatioBox",(function(e){this.hSpacing=e.readUint32(),this.vSpacing=e.readUint32()})),d.createBoxCtor("payl","CuePayloadBox",(function(e){this.text=e.readString(this.size-this.hdr_size)})),d.createBoxCtor("payt","hintpayloadID",(function(e){this.payloadID=e.readUint32();var t=e.readUint8();this.rtpmap_string=e.readString(t)})),d.createFullBoxCtor("pdin","ProgressiveDownloadInfoBox",(function(e){var t=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var i=0;i<t;i++)this.rate[i]=e.readUint32(),this.initial_delay[i]=e.readUint32()})),d.createFullBoxCtor("pitm","PrimaryItemBox",(function(e){0===this.version?this.item_id=e.readUint16():this.item_id=e.readUint32()})),d.createFullBoxCtor("pixi","PixelInformationProperty",(function(e){var t;for(this.num_channels=e.readUint8(),this.bits_per_channels=[],t=0;t<this.num_channels;t++)this.bits_per_channels[t]=e.readUint8()})),d.createBoxCtor("pmax","hintlargestpacket",(function(e){this.bytes=e.readUint32()})),d.createFullBoxCtor("prdi","ProgressiveDerivedImageItemInformationProperty",(function(e){if(this.step_count=e.readUint16(),this.item_count=[],2&this.flags)for(var t=0;t<this.step_count;t++)this.item_count[t]=e.readUint16()})),d.createFullBoxCtor("prft","ProducerReferenceTimeBox",(function(e){this.ref_track_id=e.readUint32(),this.ntp_timestamp=e.readUint64(),0===this.version?this.media_time=e.readUint32():this.media_time=e.readUint64()})),d.createFullBoxCtor("pssh","ProtectionSystemSpecificHeaderBox",(function(e){if(this.system_id=d.parseHex16(e),this.version>0){var t=e.readUint32();this.kid=[];for(var i=0;i<t;i++)this.kid[i]=d.parseHex16(e)}var r=e.readUint32();r>0&&(this.data=e.readUint8Array(r))})),d.createFullBoxCtor("clef","TrackCleanApertureDimensionsBox",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),d.createFullBoxCtor("enof","TrackEncodedPixelsDimensionsBox",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),d.createFullBoxCtor("prof","TrackProductionApertureDimensionsBox",(function(e){this.width=e.readUint32(),this.height=e.readUint32()})),d.createContainerBoxCtor("tapt","TrackApertureModeDimensionsBox",null,["clef","prof","enof"]),d.createBoxCtor("rtp ","rtpmoviehintinformation",(function(e){this.descriptionformat=e.readString(4),this.sdptext=e.readString(this.size-this.hdr_size-4)})),d.createFullBoxCtor("saio","SampleAuxiliaryInformationOffsetsBox",(function(e){1&this.flags&&(this.aux_info_type=e.readString(4),this.aux_info_type_parameter=e.readUint32());var t=e.readUint32();this.offset=[];for(var i=0;i<t;i++)0===this.version?this.offset[i]=e.readUint32():this.offset[i]=e.readUint64()})),d.createFullBoxCtor("saiz","SampleAuxiliaryInformationSizesBox",(function(e){if(1&this.flags&&(this.aux_info_type=e.readString(4),this.aux_info_type_parameter=e.readUint32()),this.default_sample_info_size=e.readUint8(),this.sample_count=e.readUint32(),this.sample_info_size=[],0===this.default_sample_info_size)for(var t=0;t<this.sample_count;t++)this.sample_info_size[t]=e.readUint8()})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_METADATA,"mett",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_METADATA,"metx",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE,"sbtt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE,"stpp",(function(e){this.parseHeader(e),this.namespace=e.readCString(),this.schema_location=e.readCString(),this.auxiliary_mime_types=e.readCString(),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE,"stxt",(function(e){this.parseHeader(e),this.content_encoding=e.readCString(),this.mime_format=e.readCString(),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_SUBTITLE,"tx3g",(function(e){this.parseHeader(e),this.displayFlags=e.readUint32(),this.horizontal_justification=e.readInt8(),this.vertical_justification=e.readInt8(),this.bg_color_rgba=e.readUint8Array(4),this.box_record=e.readInt16Array(4),this.style_record=e.readUint8Array(12),this.parseFooter(e)})),d.createSampleEntryCtor(d.SAMPLE_ENTRY_TYPE_METADATA,"wvtt",(function(e){this.parseHeader(e),this.parseFooter(e)})),d.createSampleGroupCtor("alst",(function(e){var t,i=e.readUint16();for(this.first_output_sample=e.readUint16(),this.sample_offset=[],t=0;t<i;t++)this.sample_offset[t]=e.readUint32();var r=this.description_length-4-4*i;for(this.num_output_samples=[],this.num_total_samples=[],t=0;t<r/4;t++)this.num_output_samples[t]=e.readUint16(),this.num_total_samples[t]=e.readUint16()})),d.createSampleGroupCtor("avll",(function(e){this.layerNumber=e.readUint8(),this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()})),d.createSampleGroupCtor("avss",(function(e){this.subSequenceIdentifier=e.readUint16(),this.layerNumber=e.readUint8();var t=e.readUint8();this.durationFlag=t>>7,this.avgRateFlag=t>>6&1,this.durationFlag&&(this.duration=e.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=e.readUint8(),this.avgBitRate=e.readUint16(),this.avgFrameRate=e.readUint16()),this.dependency=[];for(var i=e.readUint8(),r=0;r<i;r++){var n={};this.dependency.push(n),n.subSeqDirectionFlag=e.readUint8(),n.layerNumber=e.readUint8(),n.subSequenceIdentifier=e.readUint16()}})),d.createSampleGroupCtor("dtrt",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("mvif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("prol",(function(e){this.roll_distance=e.readInt16()})),d.createSampleGroupCtor("rap ",(function(e){var t=e.readUint8();this.num_leading_samples_known=t>>7,this.num_leading_samples=127&t})),d.createSampleGroupCtor("rash",(function(e){if(this.operation_point_count=e.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)n.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=e.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=e.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var t=0;t<this.operation_point_count;t++)this.available_bitrate[t]=e.readUint32(),this.target_rate_share[t]=e.readUint16()}this.maximum_bitrate=e.readUint32(),this.minimum_bitrate=e.readUint32(),this.discard_priority=e.readUint8()}})),d.createSampleGroupCtor("roll",(function(e){this.roll_distance=e.readInt16()})),d.SampleGroupEntry.prototype.parse=function(e){n.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=e.readUint8Array(this.description_length)},d.createSampleGroupCtor("scif",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("scnm",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("seig",(function(e){this.reserved=e.readUint8();var t=e.readUint8();this.crypt_byte_block=t>>4,this.skip_byte_block=15&t,this.isProtected=e.readUint8(),this.Per_Sample_IV_Size=e.readUint8(),this.KID=d.parseHex16(e),this.constant_IV_size=0,this.constant_IV=0,1===this.isProtected&&0===this.Per_Sample_IV_Size&&(this.constant_IV_size=e.readUint8(),this.constant_IV=e.readUint8Array(this.constant_IV_size))})),d.createSampleGroupCtor("stsa",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("sync",(function(e){var t=e.readUint8();this.NAL_unit_type=63&t})),d.createSampleGroupCtor("tele",(function(e){var t=e.readUint8();this.level_independently_decodable=t>>7})),d.createSampleGroupCtor("tsas",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("tscl",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createSampleGroupCtor("vipr",(function(e){n.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")})),d.createFullBoxCtor("sbgp","SampleToGroupBox",(function(e){this.grouping_type=e.readString(4),1===this.version?this.grouping_type_parameter=e.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.entries.push(r),r.sample_count=e.readInt32(),r.group_description_index=e.readInt32()}})),p.prototype.toString=function(){return"[row: "+this.bad_pixel_row+", column: "+this.bad_pixel_column+"]"},d.createFullBoxCtor("sbpm","SensorBadPixelsMapBox",(function(e){var t;for(this.component_count=e.readUint16(),this.component_index=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16());var i=e.readUint8();for(this.correction_applied=128==(128&i),this.num_bad_rows=e.readUint32(),this.num_bad_cols=e.readUint32(),this.num_bad_pixels=e.readUint32(),this.bad_rows=[],this.bad_columns=[],this.bad_pixels=[],t=0;t<this.num_bad_rows;t++)this.bad_rows.push(e.readUint32());for(t=0;t<this.num_bad_cols;t++)this.bad_columns.push(e.readUint32());for(t=0;t<this.num_bad_pixels;t++){var r=e.readUint32(),n=e.readUint32();this.bad_pixels.push(new p(r,n))}})),d.createFullBoxCtor("schm","SchemeTypeBox",(function(e){this.scheme_type=e.readString(4),this.scheme_version=e.readUint32(),1&this.flags&&(this.scheme_uri=e.readString(this.size-this.hdr_size-8))})),d.createBoxCtor("sdp ","rtptracksdphintinformation",(function(e){this.sdptext=e.readString(this.size-this.hdr_size)})),d.createFullBoxCtor("sdtp","SampleDependencyTypeBox",(function(e){var t,i=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var r=0;r<i;r++)t=e.readUint8(),this.is_leading[r]=t>>6,this.sample_depends_on[r]=t>>4&3,this.sample_is_depended_on[r]=t>>2&3,this.sample_has_redundancy[r]=3&t})),d.createFullBoxCtor("senc","SampleEncryptionBox"),d.createFullBoxCtor("sgpd","SampleGroupDescriptionBox",(function(e){this.grouping_type=e.readString(4),n.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=e.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=e.readUint32()),this.entries=[];for(var t=e.readUint32(),i=0;i<t;i++){var r;r=d[this.grouping_type+"SampleGroupEntry"]?new d[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new d.SampleGroupEntry(this.grouping_type),this.entries.push(r),1===this.version&&0===this.default_length?r.description_length=e.readUint32():r.description_length=this.default_length,r.write===d.SampleGroupEntry.prototype.write&&(n.info("BoxParser","SampleGroup for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),r.data=e.readUint8Array(r.description_length),e.position-=r.description_length),r.parse(e)}})),d.createFullBoxCtor("sidx","CompressedSegmentIndexBox",(function(e){this.reference_ID=e.readUint32(),this.timescale=e.readUint32(),0===this.version?(this.earliest_presentation_time=e.readUint32(),this.first_offset=e.readUint32()):(this.earliest_presentation_time=e.readUint64(),this.first_offset=e.readUint64()),e.readUint16(),this.references=[];for(var t=e.readUint16(),i=0;i<t;i++){var r={};this.references.push(r);var n=e.readUint32();r.reference_type=n>>31&1,r.referenced_size=2147483647&n,r.subsegment_duration=e.readUint32(),n=e.readUint32(),r.starts_with_SAP=n>>31&1,r.SAP_type=n>>28&7,r.SAP_delta_time=268435455&n}})),d.SingleItemTypeReferenceBox=function(e,t,i,r){d.Box.call(this,e,t),this.hdr_size=i,this.start=r},d.SingleItemTypeReferenceBox.prototype=new d.Box,d.SingleItemTypeReferenceBox.prototype.parse=function(e){this.from_item_ID=e.readUint16();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]={},this.references[i].to_item_ID=e.readUint16()},d.SingleItemTypeReferenceBoxLarge=function(e,t,i,r){d.Box.call(this,e,t),this.hdr_size=i,this.start=r},d.SingleItemTypeReferenceBoxLarge.prototype=new d.Box,d.SingleItemTypeReferenceBoxLarge.prototype.parse=function(e){this.from_item_ID=e.readUint32();var t=e.readUint16();this.references=[];for(var i=0;i<t;i++)this.references[i]={},this.references[i].to_item_ID=e.readUint32()},d.createFullBoxCtor("SmDm","SMPTE2086MasteringDisplayMetadataBox",(function(e){this.primaryRChromaticity_x=e.readUint16(),this.primaryRChromaticity_y=e.readUint16(),this.primaryGChromaticity_x=e.readUint16(),this.primaryGChromaticity_y=e.readUint16(),this.primaryBChromaticity_x=e.readUint16(),this.primaryBChromaticity_y=e.readUint16(),this.whitePointChromaticity_x=e.readUint16(),this.whitePointChromaticity_y=e.readUint16(),this.luminanceMax=e.readUint32(),this.luminanceMin=e.readUint32()})),d.createFullBoxCtor("smhd","SoundMediaHeaderBox",(function(e){this.balance=e.readUint16(),e.readUint16()})),d.createFullBoxCtor("ssix","CompressedSubsegmentIndexBox",(function(e){this.subsegments=[];for(var t=e.readUint32(),i=0;i<t;i++){var r={};this.subsegments.push(r),r.ranges=[];for(var n=e.readUint32(),s=0;s<n;s++){var o={};r.ranges.push(o),o.level=e.readUint8(),o.range_size=e.readUint24()}}})),d.createFullBoxCtor("stco","ChunkOffsetBox",(function(e){var t;if(t=e.readUint32(),this.chunk_offsets=[],0===this.version)for(var i=0;i<t;i++)this.chunk_offsets.push(e.readUint32())})),d.createFullBoxCtor("stdp","DegradationPriorityBox",(function(e){var t=(this.size-this.hdr_size)/2;this.priority=[];for(var i=0;i<t;i++)this.priority[i]=e.readUint16()})),d.createFullBoxCtor("sthd","SubtitleMediaHeaderBox"),d.createFullBoxCtor("stri","SubTrackInformationBox",(function(e){this.switch_group=e.readUint16(),this.alternate_group=e.readUint16(),this.sub_track_id=e.readUint32();var t=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),d.createFullBoxCtor("stsc","SampleToChunkBox",(function(e){var t,i;if(t=e.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(i=0;i<t;i++)this.first_chunk.push(e.readUint32()),this.samples_per_chunk.push(e.readUint32()),this.sample_description_index.push(e.readUint32())})),d.createFullBoxCtor("stsd","SampleDescriptionBox",(function(e){var t,i,r,s;for(this.entries=[],r=e.readUint32(),t=1;t<=r;t++){if(i=d.parseOneBox(e,!0,this.size-(e.getPosition()-this.start)),i.code!==d.OK)return;d[i.type+"SampleEntry"]?(s=new d[i.type+"SampleEntry"](i.size),s.hdr_size=i.hdr_size,s.start=i.start):(n.warn("BoxParser","Unknown sample entry type: "+i.type),s=new d.SampleEntry(i.type,i.size,i.hdr_size,i.start)),s.write===d.SampleEntry.prototype.write&&(n.info("BoxParser","SampleEntry "+s.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),s.parseDataAndRewind(e)),s.parse(e),this.entries.push(s)}})),d.createFullBoxCtor("stsg","SubTrackSampleGroupBox",(function(e){this.grouping_type=e.readUint32();var t=e.readUint16();this.group_description_index=[];for(var i=0;i<t;i++)this.group_description_index[i]=e.readUint32()})),d.createFullBoxCtor("stsh","ShadowSyncSampleBox",(function(e){var t,i;if(t=e.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(i=0;i<t;i++)this.shadowed_sample_numbers.push(e.readUint32()),this.sync_sample_numbers.push(e.readUint32())})),d.createFullBoxCtor("stss","SyncSampleBox",(function(e){var t,i;if(i=e.readUint32(),0===this.version)for(this.sample_numbers=[],t=0;t<i;t++)this.sample_numbers.push(e.readUint32())})),d.createFullBoxCtor("stsz","SampleSizeBox",(function(e){var t;if(this.sample_sizes=[],0===this.version)for(this.sample_size=e.readUint32(),this.sample_count=e.readUint32(),t=0;t<this.sample_count;t++)0===this.sample_size?this.sample_sizes.push(e.readUint32()):this.sample_sizes[t]=this.sample_size})),d.createFullBoxCtor("stts","TimeToSampleBox",(function(e){var t,i,r;if(t=e.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(i=0;i<t;i++)this.sample_counts.push(e.readUint32()),r=e.readInt32(),r<0&&(n.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),r=1),this.sample_deltas.push(r)})),d.createFullBoxCtor("stvi","StereoVideoBox",(function(e){var t=e.readUint32();this.single_view_allowed=3&t,this.stereo_scheme=e.readUint32();var i,r,n=e.readUint32();this.stereo_indication_type=e.readString(n),this.boxes=[];while(e.getPosition()<this.start+this.size){if(i=d.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),i.code!==d.OK)return;r=i.box,this.boxes.push(r),this[r.type]=r}})),d.createBoxCtor("styp","SegmentTypeBox",(function(e){d.ftypBox.prototype.parse.call(this,e)})),d.createFullBoxCtor("stz2","CompactSampleSizeBox",(function(e){var t,i;if(this.sample_sizes=[],0===this.version)if(this.reserved=e.readUint24(),this.field_size=e.readUint8(),i=e.readUint32(),4===this.field_size)for(t=0;t<i;t+=2){var r=e.readUint8();this.sample_sizes[t]=r>>4&15,this.sample_sizes[t+1]=15&r}else if(8===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint8();else if(16===this.field_size)for(t=0;t<i;t++)this.sample_sizes[t]=e.readUint16();else n.error("BoxParser","Error in length field in stz2 box")})),d.createFullBoxCtor("subs","SubSampleInformationBox",(function(e){var t,i,r,n;for(r=e.readUint32(),this.entries=[],t=0;t<r;t++){var s={};if(this.entries[t]=s,s.sample_delta=e.readUint32(),s.subsamples=[],n=e.readUint16(),n>0)for(i=0;i<n;i++){var o={};s.subsamples.push(o),1==this.version?o.size=e.readUint32():o.size=e.readUint16(),o.priority=e.readUint8(),o.discardable=e.readUint8(),o.codec_specific_parameters=e.readUint32()}}})),d.createFullBoxCtor("tenc","TrackEncryptionBox",(function(e){if(e.readUint8(),0===this.version)e.readUint8();else{var t=e.readUint8();this.default_crypt_byte_block=t>>4&15,this.default_skip_byte_block=15&t}this.default_isProtected=e.readUint8(),this.default_Per_Sample_IV_Size=e.readUint8(),this.default_KID=d.parseHex16(e),1===this.default_isProtected&&0===this.default_Per_Sample_IV_Size&&(this.default_constant_IV_size=e.readUint8(),this.default_constant_IV=e.readUint8Array(this.default_constant_IV_size))})),d.createFullBoxCtor("tfdt","TrackFragmentBaseMediaDecodeTimeBox",(function(e){1==this.version?this.baseMediaDecodeTime=e.readUint64():this.baseMediaDecodeTime=e.readUint32()})),d.createFullBoxCtor("tfhd","TrackFragmentHeaderBox",(function(e){var t=0;this.track_id=e.readUint32(),this.size-this.hdr_size>t&&this.flags&d.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=e.readUint64(),t+=8):this.base_data_offset=0,this.size-this.hdr_size>t&&this.flags&d.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=e.readUint32(),t+=4):this.default_sample_description_index=0,this.size-this.hdr_size>t&&this.flags&d.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=e.readUint32(),t+=4):this.default_sample_duration=0,this.size-this.hdr_size>t&&this.flags&d.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=e.readUint32(),t+=4):this.default_sample_size=0,this.size-this.hdr_size>t&&this.flags&d.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=e.readUint32(),t+=4):this.default_sample_flags=0})),d.createFullBoxCtor("tfra","TrackFragmentRandomAccessBox",(function(e){this.track_ID=e.readUint32(),e.readUint24();var t=e.readUint8();this.length_size_of_traf_num=t>>4&3,this.length_size_of_trun_num=t>>2&3,this.length_size_of_sample_num=3&t,this.entries=[];for(var i=e.readUint32(),r=0;r<i;r++)1===this.version?(this.time=e.readUint64(),this.moof_offset=e.readUint64()):(this.time=e.readUint32(),this.moof_offset=e.readUint32()),this.traf_number=e["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=e["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=e["readUint"+8*(this.length_size_of_sample_num+1)]()})),d.createFullBoxCtor("tkhd","TrackHeaderBox",(function(e){1==this.version?(this.creation_time=e.readUint64(),this.modification_time=e.readUint64(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint64()):(this.creation_time=e.readUint32(),this.modification_time=e.readUint32(),this.track_id=e.readUint32(),e.readUint32(),this.duration=e.readUint32()),e.readUint32Array(2),this.layer=e.readInt16(),this.alternate_group=e.readInt16(),this.volume=e.readInt16()>>8,e.readUint16(),this.matrix=e.readInt32Array(9),this.width=e.readUint32(),this.height=e.readUint32()})),d.createBoxCtor("tmax","hintmaxrelativetime",(function(e){this.time=e.readUint32()})),d.createBoxCtor("tmin","hintminrelativetime",(function(e){this.time=e.readUint32()})),d.createBoxCtor("totl","hintBytesSent",(function(e){this.bytessent=e.readUint32()})),d.createBoxCtor("tpay","hintBytesSent",(function(e){this.bytessent=e.readUint32()})),d.createBoxCtor("tpyl","hintBytesSent",(function(e){this.bytessent=e.readUint64()})),d.TrackGroupTypeBox.prototype.parse=function(e){this.parseFullHeader(e),this.track_group_id=e.readUint32()},d.createTrackGroupCtor("msrc"),d.TrackReferenceTypeBox=function(e,t,i,r){d.Box.call(this,e,t),this.hdr_size=i,this.start=r},d.TrackReferenceTypeBox.prototype=new d.Box,d.TrackReferenceTypeBox.prototype.parse=function(e){this.track_ids=e.readUint32Array((this.size-this.hdr_size)/4)},d.trefBox.prototype.parse=function(e){var t,i;while(e.getPosition()<this.start+this.size){if(t=d.parseOneBox(e,!0,this.size-(e.getPosition()-this.start)),t.code!==d.OK)return;i=new d.TrackReferenceTypeBox(t.type,t.size,t.hdr_size,t.start),i.write===d.Box.prototype.write&&"mdat"!==i.type&&(n.info("BoxParser","TrackReference "+i.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),i.parseDataAndRewind(e)),i.parse(e),this.boxes.push(i)}},d.createFullBoxCtor("trep","TrackExtensionPropertiesBox",(function(e){this.track_ID=e.readUint32(),this.boxes=[];while(e.getPosition()<this.start+this.size){if(ret=d.parseOneBox(e,!1,this.size-(e.getPosition()-this.start)),ret.code!==d.OK)return;box=ret.box,this.boxes.push(box)}})),d.createFullBoxCtor("trex","TrackExtendsBox",(function(e){this.track_id=e.readUint32(),this.default_sample_description_index=e.readUint32(),this.default_sample_duration=e.readUint32(),this.default_sample_size=e.readUint32(),this.default_sample_flags=e.readUint32()})),d.createBoxCtor("trpy","hintBytesSent",(function(e){this.bytessent=e.readUint64()})),d.createFullBoxCtor("trun","TrackRunBox",(function(e){var t=0;if(this.sample_count=e.readUint32(),t+=4,this.size-this.hdr_size>t&&this.flags&d.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=e.readInt32(),t+=4):this.data_offset=0,this.size-this.hdr_size>t&&this.flags&d.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=e.readUint32(),t+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>t)for(var i=0;i<this.sample_count;i++)this.flags&d.TRUN_FLAGS_DURATION&&(this.sample_duration[i]=e.readUint32()),this.flags&d.TRUN_FLAGS_SIZE&&(this.sample_size[i]=e.readUint32()),this.flags&d.TRUN_FLAGS_FLAGS&&(this.sample_flags[i]=e.readUint32()),this.flags&d.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[i]=e.readUint32():this.sample_composition_time_offset[i]=e.readInt32())})),d.createFullBoxCtor("tsel","TrackSelectionBox",(function(e){this.switch_group=e.readUint32();var t=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var i=0;i<t;i++)this.attribute_list[i]=e.readUint32()})),d.createFullBoxCtor("txtC","TextConfigBox",(function(e){this.config=e.readCString()})),d.createBoxCtor("tyco","TypeCombinationBox",(function(e){var t=(this.size-this.hdr_size)/4;this.compatible_brands=[];for(var i=0;i<t;i++)this.compatible_brands[i]=e.readString(4)})),d.createFullBoxCtor("udes","UserDescriptionProperty",(function(e){this.lang=e.readCString(),this.name=e.readCString(),this.description=e.readCString(),this.tags=e.readCString()})),d.createFullBoxCtor("uncC","UncompressedFrameConfigBox",(function(e){var t;if(this.profile=e.readString(4),1==this.version);else if(0==this.version){for(this.component_count=e.readUint32(),this.component_index=[],this.component_bit_depth_minus_one=[],this.component_format=[],this.component_align_size=[],t=0;t<this.component_count;t++)this.component_index.push(e.readUint16()),this.component_bit_depth_minus_one.push(e.readUint8()),this.component_format.push(e.readUint8()),this.component_align_size.push(e.readUint8());this.sampling_type=e.readUint8(),this.interleave_type=e.readUint8(),this.block_size=e.readUint8();var i=e.readUint8();this.component_little_endian=i>>7&1,this.block_pad_lsb=i>>6&1,this.block_little_endian=i>>5&1,this.block_reversed=i>>4&1,this.pad_unknown=i>>3&1,this.pixel_size=e.readUint32(),this.row_align_size=e.readUint32(),this.tile_align_size=e.readUint32(),this.num_tile_cols_minus_one=e.readUint32(),this.num_tile_rows_minus_one=e.readUint32()}})),d.createFullBoxCtor("url ","DataEntryUrlBox",(function(e){1!==this.flags&&(this.location=e.readCString())})),d.createFullBoxCtor("urn ","DataEntryUrnBox",(function(e){this.name=e.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=e.readCString())})),d.createUUIDBox("********************************","LiveServerManifestBox",!0,!1,(function(e){this.LiveServerManifest=e.readString(this.size-this.hdr_size).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")})),d.createUUIDBox("********************************","PiffProtectionSystemSpecificHeaderBox",!0,!1,(function(e){this.system_id=d.parseHex16(e);var t=e.readUint32();t>0&&(this.data=e.readUint8Array(t))})),d.createUUIDBox("********************************","PiffSampleEncryptionBox",!0,!1),d.createUUIDBox("********************************","PiffTrackEncryptionBox",!0,!1,(function(e){this.default_AlgorithmID=e.readUint24(),this.default_IV_size=e.readUint8(),this.default_KID=d.parseHex16(e)})),d.createUUIDBox("********************************","TfrfBox",!0,!1,(function(e){this.fragment_count=e.readUint8(),this.entries=[];for(var t=0;t<this.fragment_count;t++){var i={},r=0,n=0;1===this.version?(r=e.readUint64(),n=e.readUint64()):(r=e.readUint32(),n=e.readUint32()),i.absolute_time=r,i.absolute_duration=n,this.entries.push(i)}})),d.createUUIDBox("********************************","TfxdBox",!0,!1,(function(e){1===this.version?(this.absolute_time=e.readUint64(),this.duration=e.readUint64()):(this.absolute_time=e.readUint32(),this.duration=e.readUint32())})),d.createFullBoxCtor("vmhd","VideoMediaHeaderBox",(function(e){this.graphicsmode=e.readUint16(),this.opcolor=e.readUint16Array(3)})),d.createFullBoxCtor("vpcC","VPCodecConfigurationRecord",(function(e){var t;1===this.version?(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4,this.chromaSubsampling=t>>1&7,this.videoFullRangeFlag=1&t,this.colourPrimaries=e.readUint8(),this.transferCharacteristics=e.readUint8(),this.matrixCoefficients=e.readUint8(),this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize)):(this.profile=e.readUint8(),this.level=e.readUint8(),t=e.readUint8(),this.bitDepth=t>>4&15,this.colorSpace=15&t,t=e.readUint8(),this.chromaSubsampling=t>>4&15,this.transferFunction=t>>1&7,this.videoFullRangeFlag=1&t,this.codecIntializationDataSize=e.readUint16(),this.codecIntializationData=e.readUint8Array(this.codecIntializationDataSize))})),d.createBoxCtor("vttC","WebVTTConfigurationBox",(function(e){this.text=e.readString(this.size-this.hdr_size)})),d.createFullBoxCtor("vvcC","VvcConfigurationBox",(function(e){var t,i,r={held_bits:void 0,num_held_bits:0,stream_read_1_bytes:function(e){this.held_bits=e.readUint8(),this.num_held_bits=8},stream_read_2_bytes:function(e){this.held_bits=e.readUint16(),this.num_held_bits=16},extract_bits:function(e){var t=this.held_bits>>this.num_held_bits-e&(1<<e)-1;return this.num_held_bits-=e,t}};if(r.stream_read_1_bytes(e),r.extract_bits(5),this.lengthSizeMinusOne=r.extract_bits(2),this.ptl_present_flag=r.extract_bits(1),this.ptl_present_flag){if(r.stream_read_2_bytes(e),this.ols_idx=r.extract_bits(9),this.num_sublayers=r.extract_bits(3),this.constant_frame_rate=r.extract_bits(2),this.chroma_format_idc=r.extract_bits(2),r.stream_read_1_bytes(e),this.bit_depth_minus8=r.extract_bits(3),r.extract_bits(5),r.stream_read_2_bytes(e),r.extract_bits(2),this.num_bytes_constraint_info=r.extract_bits(6),this.general_profile_idc=r.extract_bits(7),this.general_tier_flag=r.extract_bits(1),this.general_level_idc=e.readUint8(),r.stream_read_1_bytes(e),this.ptl_frame_only_constraint_flag=r.extract_bits(1),this.ptl_multilayer_enabled_flag=r.extract_bits(1),this.general_constraint_info=new Uint8Array(this.num_bytes_constraint_info),this.num_bytes_constraint_info){for(t=0;t<this.num_bytes_constraint_info-1;t++){var n=r.extract_bits(6);r.stream_read_1_bytes(e);var s=r.extract_bits(2);this.general_constraint_info[t]=n<<2|s}this.general_constraint_info[this.num_bytes_constraint_info-1]=r.extract_bits(6)}else r.extract_bits(6);if(this.num_sublayers>1){for(r.stream_read_1_bytes(e),this.ptl_sublayer_present_mask=0,i=this.num_sublayers-2;i>=0;--i){var o=r.extract_bits(1);this.ptl_sublayer_present_mask|=o<<i}for(i=this.num_sublayers;i<=8&&this.num_sublayers>1;++i)r.extract_bits(1);for(this.sublayer_level_idc=[],i=this.num_sublayers-2;i>=0;--i)this.ptl_sublayer_present_mask&1<<i&&(this.sublayer_level_idc[i]=e.readUint8())}if(this.ptl_num_sub_profiles=e.readUint8(),this.general_sub_profile_idc=[],this.ptl_num_sub_profiles)for(t=0;t<this.ptl_num_sub_profiles;t++)this.general_sub_profile_idc.push(e.readUint32());this.max_picture_width=e.readUint16(),this.max_picture_height=e.readUint16(),this.avg_frame_rate=e.readUint16()}var a=12,l=13;this.nalu_arrays=[];var c=e.readUint8();for(t=0;t<c;t++){var d=[];this.nalu_arrays.push(d),r.stream_read_1_bytes(e),d.completeness=r.extract_bits(1),r.extract_bits(2),d.nalu_type=r.extract_bits(5);var u=1;for(d.nalu_type!=l&&d.nalu_type!=a&&(u=e.readUint16()),i=0;i<u;i++){var h=e.readUint16();d.push({data:e.readUint8Array(h),length:h})}}})),d.createFullBoxCtor("vvnC","VvcNALUConfigBox",(function(e){var t=strm.readUint8();this.lengthSizeMinusOne=3&t})),d.SampleEntry.prototype.isVideo=function(){return!1},d.SampleEntry.prototype.isAudio=function(){return!1},d.SampleEntry.prototype.isSubtitle=function(){return!1},d.SampleEntry.prototype.isMetadata=function(){return!1},d.SampleEntry.prototype.isHint=function(){return!1},d.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},d.SampleEntry.prototype.getWidth=function(){return""},d.SampleEntry.prototype.getHeight=function(){return""},d.SampleEntry.prototype.getChannelCount=function(){return""},d.SampleEntry.prototype.getSampleRate=function(){return""},d.SampleEntry.prototype.getSampleSize=function(){return""},d.VisualSampleEntry.prototype.isVideo=function(){return!0},d.VisualSampleEntry.prototype.getWidth=function(){return this.width},d.VisualSampleEntry.prototype.getHeight=function(){return this.height},d.AudioSampleEntry.prototype.isAudio=function(){return!0},d.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},d.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},d.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},d.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},d.MetadataSampleEntry.prototype.isMetadata=function(){return!0},d.decimalToHex=function(e,t){var i=Number(e).toString(16);t="undefined"===typeof t||null===t?t=2:t;while(i.length<t)i="0"+i;return i},d.avc1SampleEntry.prototype.getCodec=d.avc2SampleEntry.prototype.getCodec=d.avc3SampleEntry.prototype.getCodec=d.avc4SampleEntry.prototype.getCodec=function(){var e=d.SampleEntry.prototype.getCodec.call(this);return this.avcC?e+"."+d.decimalToHex(this.avcC.AVCProfileIndication)+d.decimalToHex(this.avcC.profile_compatibility)+d.decimalToHex(this.avcC.AVCLevelIndication):e},d.hev1SampleEntry.prototype.getCodec=d.hvc1SampleEntry.prototype.getCodec=function(){var e,t=d.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(t+=".",this.hvcC.general_profile_space){case 0:t+="";break;case 1:t+="A";break;case 2:t+="B";break;case 3:t+="C";break}t+=this.hvcC.general_profile_idc,t+=".";var i=this.hvcC.general_profile_compatibility,r=0;for(e=0;e<32;e++){if(r|=1&i,31==e)break;r<<=1,i>>=1}t+=d.decimalToHex(r,0),t+=".",0===this.hvcC.general_tier_flag?t+="L":t+="H",t+=this.hvcC.general_level_idc;var n=!1,s="";for(e=5;e>=0;e--)(this.hvcC.general_constraint_indicator[e]||n)&&(s="."+d.decimalToHex(this.hvcC.general_constraint_indicator[e],0)+s,n=!0);t+=s}return t},d.vvc1SampleEntry.prototype.getCodec=d.vvi1SampleEntry.prototype.getCodec=function(){var e,t=d.SampleEntry.prototype.getCodec.call(this);if(this.vvcC){t+="."+this.vvcC.general_profile_idc,this.vvcC.general_tier_flag?t+=".H":t+=".L",t+=this.vvcC.general_level_idc;var i="";if(this.vvcC.general_constraint_info){var r,n=[],s=0;for(s|=this.vvcC.ptl_frame_only_constraint<<7,s|=this.vvcC.ptl_multilayer_enabled<<6,e=0;e<this.vvcC.general_constraint_info.length;++e)s|=this.vvcC.general_constraint_info[e]>>2&63,n.push(s),s&&(r=e),s=this.vvcC.general_constraint_info[e]>>2&3;if(void 0===r)i=".CA";else{i=".C";var o="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567",a=0,l=0;for(e=0;e<=r;++e){a=a<<8|n[e],l+=8;while(l>=5){var c=a>>l-5&31;i+=o[c],l-=5,a&=(1<<l)-1}}l&&(a<<=5-l,i+=o[31&a])}}t+=i}return t},d.mp4aSampleEntry.prototype.getCodec=function(){var e=d.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var t=this.esds.esd.getOTI(),i=this.esds.esd.getAudioConfig();return e+"."+d.decimalToHex(t)+(i?"."+i:"")}return e},d.stxtSampleEntry.prototype.getCodec=function(){var e=d.SampleEntry.prototype.getCodec.call(this);return this.mime_format?e+"."+this.mime_format:e},d.vp08SampleEntry.prototype.getCodec=d.vp09SampleEntry.prototype.getCodec=function(){var e=d.SampleEntry.prototype.getCodec.call(this),t=this.vpcC.level;0==t&&(t="00");var i=this.vpcC.bitDepth;return 8==i&&(i="08"),e+".0"+this.vpcC.profile+"."+t+"."+i},d.av01SampleEntry.prototype.getCodec=function(){var e,t=d.SampleEntry.prototype.getCodec.call(this),i=this.av1C.seq_level_idx_0;return i<10&&(i="0"+i),2===this.av1C.seq_profile&&1===this.av1C.high_bitdepth?e=1===this.av1C.twelve_bit?"12":"10":this.av1C.seq_profile<=2&&(e=1===this.av1C.high_bitdepth?"10":"08"),t+"."+this.av1C.seq_profile+"."+i+(this.av1C.seq_tier_0?"H":"M")+"."+e},d.Box.prototype.writeHeader=function(e,t){this.size+=8,this.size>a&&(this.size+=8),"uuid"===this.type&&(this.size+=16),n.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+e.getPosition()+(t||"")),this.size>a?e.writeUint32(1):(this.sizePosition=e.getPosition(),e.writeUint32(this.size)),e.writeString(this.type,null,4),"uuid"===this.type&&e.writeUint8Array(this.uuid),this.size>a&&e.writeUint64(this.size)},d.FullBox.prototype.writeHeader=function(e){this.size+=4,d.Box.prototype.writeHeader.call(this,e," v="+this.version+" f="+this.flags),e.writeUint8(this.version),e.writeUint24(this.flags)},d.Box.prototype.write=function(e){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(e),e.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(e),this.data&&e.writeUint8Array(this.data))},d.ContainerBox.prototype.write=function(e){this.size=0,this.writeHeader(e);for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&(this.boxes[t].write(e),this.size+=this.boxes[t].size);n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},d.TrackReferenceTypeBox.prototype.write=function(e){this.size=4*this.track_ids.length,this.writeHeader(e),e.writeUint32Array(this.track_ids)},d.avcCBox.prototype.write=function(e){var t;for(this.size=7,t=0;t<this.SPS.length;t++)this.size+=2+this.SPS[t].length;for(t=0;t<this.PPS.length;t++)this.size+=2+this.PPS[t].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8(this.AVCProfileIndication),e.writeUint8(this.profile_compatibility),e.writeUint8(this.AVCLevelIndication),e.writeUint8(this.lengthSizeMinusOne+252),e.writeUint8(this.SPS.length+224),t=0;t<this.SPS.length;t++)e.writeUint16(this.SPS[t].length),e.writeUint8Array(this.SPS[t].nalu);for(e.writeUint8(this.PPS.length),t=0;t<this.PPS.length;t++)e.writeUint16(this.PPS[t].length),e.writeUint8Array(this.PPS[t].nalu);this.ext&&e.writeUint8Array(this.ext)},d.co64Box.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),t=0;t<this.chunk_offsets.length;t++)e.writeUint64(this.chunk_offsets[t])},d.cslgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeInt32(this.compositionToDTSShift),e.writeInt32(this.leastDecodeToDisplayDelta),e.writeInt32(this.greatestDecodeToDisplayDelta),e.writeInt32(this.compositionStartTime),e.writeInt32(this.compositionEndTime)},d.cttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),1===this.version?e.writeInt32(this.sample_offsets[t]):e.writeUint32(this.sample_offsets[t])},d.drefBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},d.elngBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(e),e.writeString(this.extended_language)},d.elstBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(e),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeUint32(i.segment_duration),e.writeInt32(i.media_time),e.writeInt16(i.media_rate_integer),e.writeInt16(i.media_rate_fraction)}},d.emsgBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(e),e.writeCString(this.scheme_id_uri),e.writeCString(this.value),e.writeUint32(this.timescale),e.writeUint32(this.presentation_time_delta),e.writeUint32(this.event_duration),e.writeUint32(this.id),e.writeUint8Array(this.message_data)},d.ftypBox.prototype.write=function(e){this.size=8+4*this.compatible_brands.length,this.writeHeader(e),e.writeString(this.major_brand,null,4),e.writeUint32(this.minor_version);for(var t=0;t<this.compatible_brands.length;t++)e.writeString(this.compatible_brands[t],null,4)},d.hdlrBox.prototype.write=function(e){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(e),e.writeUint32(0),e.writeString(this.handler,null,4),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeCString(this.name)},d.hvcCBox.prototype.write=function(e){var t,i;for(this.size=23,t=0;t<this.nalu_arrays.length;t++)for(this.size+=3,i=0;i<this.nalu_arrays[t].length;i++)this.size+=2+this.nalu_arrays[t][i].data.length;for(this.writeHeader(e),e.writeUint8(this.configurationVersion),e.writeUint8((this.general_profile_space<<6)+(this.general_tier_flag<<5)+this.general_profile_idc),e.writeUint32(this.general_profile_compatibility),e.writeUint8Array(this.general_constraint_indicator),e.writeUint8(this.general_level_idc),e.writeUint16(this.min_spatial_segmentation_idc+(15<<24)),e.writeUint8(this.parallelismType+252),e.writeUint8(this.chroma_format_idc+252),e.writeUint8(this.bit_depth_luma_minus8+248),e.writeUint8(this.bit_depth_chroma_minus8+248),e.writeUint16(this.avgFrameRate),e.writeUint8((this.constantFrameRate<<6)+(this.numTemporalLayers<<3)+(this.temporalIdNested<<2)+this.lengthSizeMinusOne),e.writeUint8(this.nalu_arrays.length),t=0;t<this.nalu_arrays.length;t++)for(e.writeUint8((this.nalu_arrays[t].completeness<<7)+this.nalu_arrays[t].nalu_type),e.writeUint16(this.nalu_arrays[t].length),i=0;i<this.nalu_arrays[t].length;i++)e.writeUint16(this.nalu_arrays[t][i].data.length),e.writeUint8Array(this.nalu_arrays[t][i].data)},d.kindBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(e),e.writeCString(this.schemeURI),e.writeCString(this.value)},d.mdhdBox.prototype.write=function(e){this.size=20,this.flags=0,this.version=0,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint16(this.language),e.writeUint16(0)},d.mehdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.fragment_duration)},d.mfhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4,this.writeHeader(e),e.writeUint32(this.sequence_number)},d.mvhdBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=96,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.timescale),e.writeUint32(this.duration),e.writeUint32(this.rate),e.writeUint16(this.volume<<8),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32Array(this.matrix),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(this.next_track_id)},d.SampleEntry.prototype.writeHeader=function(e){this.size=8,d.Box.prototype.writeHeader.call(this,e),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint8(0),e.writeUint16(this.data_reference_index)},d.SampleEntry.prototype.writeFooter=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e),this.size+=this.boxes[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},d.SampleEntry.prototype.write=function(e){this.writeHeader(e),e.writeUint8Array(this.data),this.size+=this.data.length,n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},d.VisualSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=70,e.writeUint16(0),e.writeUint16(0),e.writeUint32(0),e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.width),e.writeUint16(this.height),e.writeUint32(this.horizresolution),e.writeUint32(this.vertresolution),e.writeUint32(0),e.writeUint16(this.frame_count),e.writeUint8(Math.min(31,this.compressorname.length)),e.writeString(this.compressorname,null,31),e.writeUint16(this.depth),e.writeInt16(-1),this.writeFooter(e)},d.AudioSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=20,e.writeUint32(0),e.writeUint32(0),e.writeUint16(this.channel_count),e.writeUint16(this.samplesize),e.writeUint16(0),e.writeUint16(0),e.writeUint32(this.samplerate<<16),this.writeFooter(e)},d.stppSampleEntry.prototype.write=function(e){this.writeHeader(e),this.size+=this.namespace.length+1+this.schema_location.length+1+this.auxiliary_mime_types.length+1,e.writeCString(this.namespace),e.writeCString(this.schema_location),e.writeCString(this.auxiliary_mime_types),this.writeFooter(e)},d.SampleGroupEntry.prototype.write=function(e){e.writeUint8Array(this.data)},d.sbgpBox.prototype.write=function(e){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(e),e.writeString(this.grouping_type,null,4),e.writeUint32(this.grouping_type_parameter),e.writeUint32(this.entries.length);for(var t=0;t<this.entries.length;t++){var i=this.entries[t];e.writeInt32(i.sample_count),e.writeInt32(i.group_description_index)}},d.sgpdBox.prototype.write=function(e){var t,i;for(this.flags=0,this.size=12,t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=i.data.length);for(this.writeHeader(e),e.writeString(this.grouping_type,null,4),1===this.version&&e.writeUint32(this.default_length),this.version>=2&&e.writeUint32(this.default_sample_description_index),e.writeUint32(this.entries.length),t=0;t<this.entries.length;t++)i=this.entries[t],1===this.version&&0===this.default_length&&e.writeUint32(i.description_length),i.write(e)},d.sidxBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(e),e.writeUint32(this.reference_ID),e.writeUint32(this.timescale),e.writeUint32(this.earliest_presentation_time),e.writeUint32(this.first_offset),e.writeUint16(0),e.writeUint16(this.references.length);for(var t=0;t<this.references.length;t++){var i=this.references[t];e.writeUint32(i.reference_type<<31|i.referenced_size),e.writeUint32(i.subsegment_duration),e.writeUint32(i.starts_with_SAP<<31|i.SAP_type<<28|i.SAP_delta_time)}},d.smhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=4,this.writeHeader(e),e.writeUint16(this.balance),e.writeUint16(0)},d.stcoBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(e),e.writeUint32(this.chunk_offsets.length),e.writeUint32Array(this.chunk_offsets)},d.stscBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(e),e.writeUint32(this.first_chunk.length),t=0;t<this.first_chunk.length;t++)e.writeUint32(this.first_chunk[t]),e.writeUint32(this.samples_per_chunk[t]),e.writeUint32(this.sample_description_index[t])},d.stsdBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(e),e.writeUint32(this.entries.length),this.size+=4,t=0;t<this.entries.length;t++)this.entries[t].write(e),this.size+=this.entries[t].size;n.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),e.adjustUint32(this.sizePosition,this.size)},d.stshBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(e),e.writeUint32(this.shadowed_sample_numbers.length),t=0;t<this.shadowed_sample_numbers.length;t++)e.writeUint32(this.shadowed_sample_numbers[t]),e.writeUint32(this.sync_sample_numbers[t])},d.stssBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(e),e.writeUint32(this.sample_numbers.length),e.writeUint32Array(this.sample_numbers)},d.stszBox.prototype.write=function(e){var t,i=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0){t=0;while(t+1<this.sample_sizes.length){if(this.sample_sizes[t+1]!==this.sample_sizes[0]){i=!1;break}t++}}else i=!1;this.size=8,i||(this.size+=4*this.sample_sizes.length),this.writeHeader(e),i?e.writeUint32(this.sample_sizes[0]):e.writeUint32(0),e.writeUint32(this.sample_sizes.length),i||e.writeUint32Array(this.sample_sizes)},d.sttsBox.prototype.write=function(e){var t;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(e),e.writeUint32(this.sample_counts.length),t=0;t<this.sample_counts.length;t++)e.writeUint32(this.sample_counts[t]),e.writeUint32(this.sample_deltas[t])},d.tfdtBox.prototype.write=function(e){var t=Math.pow(2,32)-1;this.version=this.baseMediaDecodeTime>t?1:0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(e),1===this.version?e.writeUint64(this.baseMediaDecodeTime):e.writeUint32(this.baseMediaDecodeTime)},d.tfhdBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&d.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&d.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&d.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&d.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&d.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(e),e.writeUint32(this.track_id),this.flags&d.TFHD_FLAG_BASE_DATA_OFFSET&&e.writeUint64(this.base_data_offset),this.flags&d.TFHD_FLAG_SAMPLE_DESC&&e.writeUint32(this.default_sample_description_index),this.flags&d.TFHD_FLAG_SAMPLE_DUR&&e.writeUint32(this.default_sample_duration),this.flags&d.TFHD_FLAG_SAMPLE_SIZE&&e.writeUint32(this.default_sample_size),this.flags&d.TFHD_FLAG_SAMPLE_FLAGS&&e.writeUint32(this.default_sample_flags)},d.tkhdBox.prototype.write=function(e){this.version=0,this.size=80,this.writeHeader(e),e.writeUint32(this.creation_time),e.writeUint32(this.modification_time),e.writeUint32(this.track_id),e.writeUint32(0),e.writeUint32(this.duration),e.writeUint32(0),e.writeUint32(0),e.writeInt16(this.layer),e.writeInt16(this.alternate_group),e.writeInt16(this.volume<<8),e.writeUint16(0),e.writeInt32Array(this.matrix),e.writeUint32(this.width),e.writeUint32(this.height)},d.trexBox.prototype.write=function(e){this.version=0,this.flags=0,this.size=20,this.writeHeader(e),e.writeUint32(this.track_id),e.writeUint32(this.default_sample_description_index),e.writeUint32(this.default_sample_duration),e.writeUint32(this.default_sample_size),e.writeUint32(this.default_sample_flags)},d.trunBox.prototype.write=function(e){this.version=0,this.size=4,this.flags&d.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&d.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&d.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&d.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&d.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&d.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(e),e.writeUint32(this.sample_count),this.flags&d.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=e.getPosition(),e.writeInt32(this.data_offset)),this.flags&d.TRUN_FLAGS_FIRST_FLAG&&e.writeUint32(this.first_sample_flags);for(var t=0;t<this.sample_count;t++)this.flags&d.TRUN_FLAGS_DURATION&&e.writeUint32(this.sample_duration[t]),this.flags&d.TRUN_FLAGS_SIZE&&e.writeUint32(this.sample_size[t]),this.flags&d.TRUN_FLAGS_FLAGS&&e.writeUint32(this.sample_flags[t]),this.flags&d.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?e.writeUint32(this.sample_composition_time_offset[t]):e.writeInt32(this.sample_composition_time_offset[t]))},d["url Box"].prototype.write=function(e){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(e),this.location&&e.writeCString(this.location)},d["urn Box"].prototype.write=function(e){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(e),e.writeCString(this.name),this.location&&e.writeCString(this.location)},d.vmhdBox.prototype.write=function(e){this.version=0,this.flags=1,this.size=8,this.writeHeader(e),e.writeUint16(this.graphicsmode),e.writeUint16Array(this.opcolor)},d.cttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].pts=e[r].dts+this.sample_offsets[t],r++},d.sttsBox.prototype.unpack=function(e){var t,i,r;for(r=0,t=0;t<this.sample_counts.length;t++)for(i=0;i<this.sample_counts[t];i++)e[r].dts=0===r?0:e[r-1].dts+this.sample_deltas[t],r++},d.stcoBox.prototype.unpack=function(e){var t;for(t=0;t<this.chunk_offsets.length;t++)e[t].offset=this.chunk_offsets[t]},d.stscBox.prototype.unpack=function(e){var t,i,r,n,s;for(n=0,s=0,t=0;t<this.first_chunk.length;t++)for(i=0;i<(t+1<this.first_chunk.length?this.first_chunk[t+1]:1/0);i++)for(s++,r=0;r<this.samples_per_chunk[t];r++){if(!e[n])return;e[n].description_index=this.sample_description_index[t],e[n].chunk_index=s,n++}},d.stszBox.prototype.unpack=function(e){var t;for(t=0;t<this.sample_sizes.length;t++)e[t].size=this.sample_sizes[t]},d.DIFF_BOXES_PROP_NAMES=["boxes","entries","references","subsamples","items","item_infos","extents","associations","subsegments","ranges","seekLists","seekPoints","esd","levels"],d.DIFF_PRIMITIVE_ARRAY_PROP_NAMES=["compatible_brands","matrix","opcolor","sample_counts","sample_deltas","first_chunk","samples_per_chunk","sample_sizes","chunk_offsets","sample_offsets","sample_description_index","sample_duration"],d.boxEqualFields=function(e,t){if(e&&!t)return!1;var i;for(i in e)if(!(d.DIFF_BOXES_PROP_NAMES.indexOf(i)>-1)&&!(e[i]instanceof d.Box||t[i]instanceof d.Box)&&"undefined"!==typeof e[i]&&"undefined"!==typeof t[i]&&"function"!==typeof e[i]&&"function"!==typeof t[i]&&!(e.subBoxNames&&e.subBoxNames.indexOf(i.slice(0,4))>-1||t.subBoxNames&&t.subBoxNames.indexOf(i.slice(0,4))>-1)&&"data"!==i&&"start"!==i&&"size"!==i&&"creation_time"!==i&&"modification_time"!==i&&!(d.DIFF_PRIMITIVE_ARRAY_PROP_NAMES.indexOf(i)>-1)&&e[i]!==t[i])return!1;return!0},d.boxEqual=function(e,t){if(!d.boxEqualFields(e,t))return!1;for(var i=0;i<d.DIFF_BOXES_PROP_NAMES.length;i++){var r=d.DIFF_BOXES_PROP_NAMES[i];if(e[r]&&t[r]&&!d.boxEqual(e[r],t[r]))return!1}return!0};var f=function(){};f.prototype.parseSample=function(e){var t,i,r=new s(e.buffer);t=[];while(!r.isEos())i=d.parseOneBox(r,!1),i.code===d.OK&&"vttc"===i.box.type&&t.push(i.box);return t},f.prototype.getText=function(e,t,i){function r(e,t,i){return i=i||"0",e+="",e.length>=t?e:new Array(t-e.length+1).join(i)+e}function n(e){var t=Math.floor(e/3600),i=Math.floor((e-3600*t)/60),n=Math.floor(e-3600*t-60*i),s=Math.floor(1e3*(e-3600*t-60*i-n));return r(t,2)+":"+r(i,2)+":"+r(n,2)+"."+r(s,3)}for(var s=this.parseSample(i),o="",a=0;a<s.length;a++){var l=s[a];o+=n(e)+" --\x3e "+n(t)+"\r\n",o+=l.payl.text}return o};var m=function(){};m.prototype.parseSample=function(e){var t,i={};i.resources=[];var r=new s(e.data.buffer);if(e.subsamples&&0!==e.subsamples.length){if(i.documentString=r.readString(e.subsamples[0].size),e.subsamples.length>1)for(t=1;t<e.subsamples.length;t++)i.resources[t]=r.readUint8Array(e.subsamples[t].size)}else i.documentString=r.readString(e.data.length);return"undefined"!==typeof DOMParser&&(i.document=(new DOMParser).parseFromString(i.documentString,"application/xml")),i};var g=function(){};g.prototype.parseSample=function(e){var t,i=new s(e.data.buffer);return t=i.readString(e.data.length),t},g.prototype.parseConfig=function(e){var t,i=new s(e.buffer);return i.readUint32(),t=i.readCString(),t},t.VTTin4Parser=f,t.XMLSubtitlein4Parser=m,t.Textin4Parser=g;var y=function(e){this.stream=e||new l,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1,this.items=[],this.entity_groups=[],this.onSidx=null,this.sidxSent=!1};y.prototype.setSegmentOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var n={};this.fragmentedTracks.push(n),n.id=e,n.user=t,n.trak=r,r.nextSample=0,n.segmentStream=null,n.nb_samples=1e3,n.rapAlignement=!0,i&&(i.nbSamples&&(n.nb_samples=i.nbSamples),i.rapAlignement&&(n.rapAlignement=i.rapAlignement))}},y.prototype.unsetSegmentOptions=function(e){for(var t=-1,i=0;i<this.fragmentedTracks.length;i++){var r=this.fragmentedTracks[i];r.id==e&&(t=i)}t>-1&&this.fragmentedTracks.splice(t,1)},y.prototype.setExtractionOptions=function(e,t,i){var r=this.getTrackById(e);if(r){var n={};this.extractedTracks.push(n),n.id=e,n.user=t,n.trak=r,r.nextSample=0,n.nb_samples=1e3,n.samples=[],i&&i.nbSamples&&(n.nb_samples=i.nbSamples)}},y.prototype.unsetExtractionOptions=function(e){for(var t=-1,i=0;i<this.extractedTracks.length;i++){var r=this.extractedTracks[i];r.id==e&&(t=i)}t>-1&&this.extractedTracks.splice(t,1)},y.prototype.parse=function(){var e,t,i=!1;if(!this.restoreParsePosition||this.restoreParsePosition())while(1){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),e=d.parseOneBox(this.stream,i),e.code===d.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(e))continue;return}return}var r;switch(t=e.box,r="uuid"!==t.type?t.type:t.uuid,this.boxes.push(t),r){case"mdat":this.mdats.push(t);break;case"moof":this.moofs.push(t);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[r]&&n.warn("ISOFile","Duplicate Box of type: "+r+", overriding previous occurrence"),this[r]=t;break}this.updateUsedBytes&&this.updateUsedBytes(t,e)}},y.prototype.checkBuffer=function(e){if(null===e||void 0===e)throw"Buffer must be defined and non empty";if(void 0===e.fileStart)throw"Buffer must have a fileStart property";return 0===e.byteLength?(n.warn("ISOFile","Ignoring empty buffer (fileStart: "+e.fileStart+")"),this.stream.logBufferLevel(),!1):(n.info("ISOFile","Processing buffer (fileStart: "+e.fileStart+")"),e.usedBytes=0,this.stream.insertBuffer(e),this.stream.logBufferLevel(),!!this.stream.initialized()||(n.warn("ISOFile","Not ready to start parsing"),!1))},y.prototype.appendBuffer=function(e,t){var i;if(this.checkBuffer(e))return this.parse(),this.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.moov?(this.sampleListBuilt||(this.buildSampleLists(),this.sampleListBuilt=!0),this.updateSampleLists(),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples(t),this.nextSeekPosition?(i=this.nextSeekPosition,this.nextSeekPosition=void 0):i=this.nextParsePosition,this.stream.getEndFilePositionAfter&&(i=this.stream.getEndFilePositionAfter(i))):i=this.nextParsePosition?this.nextParsePosition:0,this.sidx&&this.onSidx&&!this.sidxSent&&(this.onSidx(this.sidx),this.sidxSent=!0),this.meta&&(this.flattenItemInfo&&!this.itemListBuilt&&(this.flattenItemInfo(),this.itemListBuilt=!0),this.processItems&&this.processItems(this.onItem)),this.stream.cleanBuffers&&(n.info("ISOFile","Done processing buffer (fileStart: "+e.fileStart+") - next buffer to fetch should have a fileStart position of "+i),this.stream.logBufferLevel(),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize())),i},y.prototype.getInfo=function(){var e,t,i,r,n,s,o={},a=new Date("1904-01-01T00:00:00Z").getTime();if(this.moov)for(o.hasMoov=!0,o.duration=this.moov.mvhd.duration,o.timescale=this.moov.mvhd.timescale,o.isFragmented=null!=this.moov.mvex,o.isFragmented&&this.moov.mvex.mehd&&(o.fragment_duration=this.moov.mvex.mehd.fragment_duration),o.isProgressive=this.isProgressive,o.hasIOD=null!=this.moov.iods,o.brands=[],o.brands.push(this.ftyp.major_brand),o.brands=o.brands.concat(this.ftyp.compatible_brands),o.created=new Date(a+1e3*this.moov.mvhd.creation_time),o.modified=new Date(a+1e3*this.moov.mvhd.modification_time),o.tracks=[],o.audioTracks=[],o.videoTracks=[],o.subtitleTracks=[],o.metadataTracks=[],o.hintTracks=[],o.otherTracks=[],e=0;e<this.moov.traks.length;e++){if(i=this.moov.traks[e],s=i.mdia.minf.stbl.stsd.entries[0],r={},o.tracks.push(r),r.id=i.tkhd.track_id,r.name=i.mdia.hdlr.name,r.references=[],i.tref)for(t=0;t<i.tref.boxes.length;t++)n={},r.references.push(n),n.type=i.tref.boxes[t].type,n.track_ids=i.tref.boxes[t].track_ids;i.edts&&(r.edits=i.edts.elst.entries),r.created=new Date(a+1e3*i.tkhd.creation_time),r.modified=new Date(a+1e3*i.tkhd.modification_time),r.movie_duration=i.tkhd.duration,r.movie_timescale=o.timescale,r.layer=i.tkhd.layer,r.alternate_group=i.tkhd.alternate_group,r.volume=i.tkhd.volume,r.matrix=i.tkhd.matrix,r.track_width=i.tkhd.width/65536,r.track_height=i.tkhd.height/65536,r.timescale=i.mdia.mdhd.timescale,r.cts_shift=i.mdia.minf.stbl.cslg,r.duration=i.mdia.mdhd.duration,r.samples_duration=i.samples_duration,r.codec=s.getCodec(),r.kind=i.udta&&i.udta.kinds.length?i.udta.kinds[0]:{schemeURI:"",value:""},r.language=i.mdia.elng?i.mdia.elng.extended_language:i.mdia.mdhd.languageString,r.nb_samples=i.samples.length,r.size=i.samples_size,r.bitrate=8*r.size*r.timescale/r.samples_duration,s.isAudio()?(r.type="audio",o.audioTracks.push(r),r.audio={},r.audio.sample_rate=s.getSampleRate(),r.audio.channel_count=s.getChannelCount(),r.audio.sample_size=s.getSampleSize()):s.isVideo()?(r.type="video",o.videoTracks.push(r),r.video={},r.video.width=s.getWidth(),r.video.height=s.getHeight()):s.isSubtitle()?(r.type="subtitles",o.subtitleTracks.push(r)):s.isHint()?(r.type="metadata",o.hintTracks.push(r)):s.isMetadata()?(r.type="metadata",o.metadataTracks.push(r)):(r.type="metadata",o.otherTracks.push(r))}else o.hasMoov=!1;if(o.mime="",o.hasMoov&&o.tracks){for(o.videoTracks&&o.videoTracks.length>0?o.mime+='video/mp4; codecs="':o.audioTracks&&o.audioTracks.length>0?o.mime+='audio/mp4; codecs="':o.mime+='application/mp4; codecs="',e=0;e<o.tracks.length;e++)0!==e&&(o.mime+=","),o.mime+=o.tracks[e].codec;o.mime+='"; profiles="',o.mime+=this.ftyp.compatible_brands.join(),o.mime+='"'}return o},y.prototype.setNextSeekPositionFromSample=function(e){e&&(this.nextSeekPosition?this.nextSeekPosition=Math.min(e.offset+e.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=e.offset+e.alreadyRead)},y.prototype.processSamples=function(e){var t,i;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(t=0;t<this.fragmentedTracks.length;t++){var r=this.fragmentedTracks[t];i=r.trak;while(i.nextSample<i.samples.length&&this.sampleProcessingStarted){n.debug("ISOFile","Creating media fragment on track #"+r.id+" for sample "+i.nextSample);var s=this.createFragment(r.id,i.nextSample,r.segmentStream);if(!s)break;if(r.segmentStream=s,i.nextSample++,(i.nextSample%r.nb_samples===0||e||i.nextSample>=i.samples.length)&&(n.info("ISOFile","Sending fragmented data on track #"+r.id+" for samples ["+Math.max(0,i.nextSample-r.nb_samples)+","+(i.nextSample-1)+"]"),n.info("ISOFile","Sample data size in memory: "+this.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(r.id,r.user,r.segmentStream.buffer,i.nextSample,e||i.nextSample>=i.samples.length),r.segmentStream=null,r!==this.fragmentedTracks[t]))break}}if(null!==this.onSamples)for(t=0;t<this.extractedTracks.length;t++){var o=this.extractedTracks[t];i=o.trak;while(i.nextSample<i.samples.length&&this.sampleProcessingStarted){n.debug("ISOFile","Exporting on track #"+o.id+" sample #"+i.nextSample);var a=this.getSample(i,i.nextSample);if(!a){this.setNextSeekPositionFromSample(i.samples[i.nextSample]);break}if(i.nextSample++,o.samples.push(a),(i.nextSample%o.nb_samples===0||i.nextSample>=i.samples.length)&&(n.debug("ISOFile","Sending samples on track #"+o.id+" for sample "+i.nextSample),this.onSamples&&this.onSamples(o.id,o.user,o.samples),o.samples=[],o!==this.extractedTracks[t]))break}}}},y.prototype.getBox=function(e){var t=this.getBoxes(e,!0);return t.length?t[0]:null},y.prototype.getBoxes=function(e,t){var i=[];return y._sweep.call(this,e,i,t),i},y._sweep=function(e,t,i){for(var r in this.type&&this.type==e&&t.push(this),this.boxes){if(t.length&&i)return;y._sweep.call(this.boxes[r],e,t,i)}},y.prototype.getTrackSamplesInfo=function(e){var t=this.getTrackById(e);return t?t.samples:void 0},y.prototype.getTrackSample=function(e,t){var i=this.getTrackById(e),r=this.getSample(i,t);return r},y.prototype.releaseUsedSamples=function(e,t){var i=0,r=this.getTrackById(e);r.lastValidSample||(r.lastValidSample=0);for(var s=r.lastValidSample;s<t;s++)i+=this.releaseSample(r,s);n.info("ISOFile","Track #"+e+" released samples up to "+t+" (released size: "+i+", remaining: "+this.samplesDataSize+")"),r.lastValidSample=t},y.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples(!1)},y.prototype.stop=function(){this.sampleProcessingStarted=!1},y.prototype.flush=function(){n.info("ISOFile","Flushing remaining samples"),this.updateSampleLists(),this.processSamples(!0),this.stream.cleanBuffers(),this.stream.logBufferLevel(!0)},y.prototype.seekTrack=function(e,t,i){var r,s,o,a=1/0,l=0,c=0;if(0===i.samples.length)return n.info("ISOFile","No sample in track, cannot seek! Using time "+n.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(r=0;r<i.samples.length;r++){if(s=i.samples[r],0===r)c=0,o=s.timescale;else if(s.cts>e*s.timescale){c=r-1;break}t&&s.is_sync&&(l=r)}t&&(c=l),e=i.samples[c].cts,i.nextSample=c;while(i.samples[c].alreadyRead===i.samples[c].size){if(!i.samples[c+1])break;c++}return a=i.samples[c].offset+i.samples[c].alreadyRead,n.info("ISOFile","Seeking to "+(t?"RAP":"")+" sample #"+i.nextSample+" on track "+i.tkhd.track_id+", time "+n.getDurationString(e,o)+" and offset: "+a),{offset:a,time:e/o}},y.prototype.getTrackDuration=function(e){var t;return e.samples?(t=e.samples[e.samples.length-1],(t.cts+t.duration)/t.timescale):1/0},y.prototype.seek=function(e,t){var i,r,s,o=this.moov,a={offset:1/0,time:1/0};if(this.moov){for(s=0;s<o.traks.length;s++)i=o.traks[s],e>this.getTrackDuration(i)||(r=this.seekTrack(e,t,i),r.offset<a.offset&&(a.offset=r.offset),r.time<a.time&&(a.time=r.time));return n.info("ISOFile","Seeking at time "+n.getDurationString(a.time,1)+" needs a buffer with a fileStart position of "+a.offset),a.offset===1/0?a={offset:this.nextParsePosition,time:0}:a.offset=this.stream.getEndFilePositionAfter(a.offset),n.info("ISOFile","Adjusted seek position (after checking data already in buffer): "+a.offset),a}throw"Cannot seek: moov not received!"},y.prototype.equal=function(e){var t=0;while(t<this.boxes.length&&t<e.boxes.length){var i=this.boxes[t],r=e.boxes[t];if(!d.boxEqual(i,r))return!1;t++}return!0},t.ISOFile=y,y.prototype.lastBoxStartPosition=0,y.prototype.parsingMdat=null,y.prototype.nextParsePosition=0,y.prototype.discardMdatData=!1,y.prototype.processIncompleteBox=function(e){var t,i,r;return"mdat"===e.type?(t=new d[e.type+"Box"](e.size),this.parsingMdat=t,this.boxes.push(t),this.mdats.push(t),t.start=e.start,t.hdr_size=e.hdr_size,this.stream.addUsedBytes(t.hdr_size),this.lastBoxStartPosition=t.start+t.size,r=this.stream.seek(t.start+t.size,!1,this.discardMdatData),r?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=t.start+t.size,!1)):("moov"===e.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),i=!!this.stream.mergeNextBuffer&&this.stream.mergeNextBuffer(),i?(this.nextParsePosition=this.stream.getEndPosition(),!0):(e.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+e.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},y.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},y.prototype.processIncompleteMdat=function(){var e,t;return e=this.parsingMdat,t=this.stream.seek(e.start+e.size,!1,this.discardMdatData),t?(n.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},y.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},y.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},y.prototype.updateUsedBytes=function(e,t){this.stream.addUsedBytes&&("mdat"===e.type?(this.stream.addUsedBytes(e.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(e.size-e.hdr_size)):this.stream.addUsedBytes(e.size))},y.prototype.add=d.Box.prototype.add,y.prototype.addBox=d.Box.prototype.addBox,y.prototype.init=function(e){var t=e||{},i=(this.add("ftyp").set("major_brand",t.brands&&t.brands[0]||"iso4").set("minor_version",0).set("compatible_brands",t.brands||["iso4"]),this.add("moov"));return i.add("mvhd").set("timescale",t.timescale||600).set("rate",t.rate||65536).set("creation_time",0).set("modification_time",0).set("duration",t.duration||0).set("volume",t.width?0:256).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("next_track_id",1),i.add("mvex"),this},y.prototype.addTrack=function(e){this.moov||this.init(e);var t=e||{};t.width=t.width||320,t.height=t.height||320,t.id=t.id||this.moov.mvhd.next_track_id,t.type=t.type||"avc1";var i=this.moov.add("trak");this.moov.mvhd.next_track_id=t.id+1,i.add("tkhd").set("flags",d.TKHD_FLAG_ENABLED|d.TKHD_FLAG_IN_MOVIE|d.TKHD_FLAG_IN_PREVIEW).set("creation_time",0).set("modification_time",0).set("track_id",t.id).set("duration",t.duration||0).set("layer",t.layer||0).set("alternate_group",0).set("volume",1).set("matrix",[65536,0,0,0,65536,0,0,0,1073741824]).set("width",t.width<<16).set("height",t.height<<16);var r=i.add("mdia");r.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",t.timescale||1).set("duration",t.media_duration||0).set("language",t.language||"und"),r.add("hdlr").set("handler",t.hdlr||"vide").set("name",t.name||"Track created with MP4Box.js"),r.add("elng").set("extended_language",t.language||"fr-FR");var n=r.add("minf");if(void 0!==d[t.type+"SampleEntry"]){var o=new d[t.type+"SampleEntry"];o.data_reference_index=1;var a="";for(var l in d.sampleEntryCodes)for(var c=d.sampleEntryCodes[l],u=0;u<c.length;u++)if(c.indexOf(t.type)>-1){a=l;break}switch(a){case"Visual":if(n.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),o.set("width",t.width).set("height",t.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",t.type+" Compressor").set("depth",24),t.avcDecoderConfigRecord){var h=new d.avcCBox;h.parse(new s(t.avcDecoderConfigRecord)),o.addBox(h)}else if(t.hevcDecoderConfigRecord){var p=new d.hvcCBox;p.parse(new s(t.hevcDecoderConfigRecord)),o.addBox(p)}break;case"Audio":n.add("smhd").set("balance",t.balance||0),o.set("channel_count",t.channel_count||2).set("samplesize",t.samplesize||16).set("samplerate",t.samplerate||65536);break;case"Hint":n.add("hmhd");break;case"Subtitle":switch(n.add("sthd"),t.type){case"stpp":o.set("namespace",t.namespace||"nonamespace").set("schema_location",t.schema_location||"").set("auxiliary_mime_types",t.auxiliary_mime_types||"");break}break;case"Metadata":n.add("nmhd");break;case"System":n.add("nmhd");break;default:n.add("nmhd");break}t.description&&o.addBox(t.description),t.description_boxes&&t.description_boxes.forEach((function(e){o.addBox(e)})),n.add("dinf").add("dref").addEntry((new d["url Box"]).set("flags",1));var f=n.add("stbl");return f.add("stsd").addEntry(o),f.add("stts").set("sample_counts",[]).set("sample_deltas",[]),f.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),f.add("stco").set("chunk_offsets",[]),f.add("stsz").set("sample_sizes",[]),this.moov.mvex.add("trex").set("track_id",t.id).set("default_sample_description_index",t.default_sample_description_index||1).set("default_sample_duration",t.default_sample_duration||0).set("default_sample_size",t.default_sample_size||0).set("default_sample_flags",t.default_sample_flags||0),this.buildTrakSampleLists(i),t.id}},d.Box.prototype.computeSize=function(e){var t=e||new o;t.endianness=o.BIG_ENDIAN,this.write(t)},y.prototype.addSample=function(e,t,i){var r=i||{},n={},s=this.getTrackById(e);if(null!==s){n.number=s.samples.length,n.track_id=s.tkhd.track_id,n.timescale=s.mdia.mdhd.timescale,n.description_index=r.sample_description_index?r.sample_description_index-1:0,n.description=s.mdia.minf.stbl.stsd.entries[n.description_index],n.data=t,n.size=t.byteLength,n.alreadyRead=n.size,n.duration=r.duration||1,n.cts=r.cts||0,n.dts=r.dts||0,n.is_sync=r.is_sync||!1,n.is_leading=r.is_leading||0,n.depends_on=r.depends_on||0,n.is_depended_on=r.is_depended_on||0,n.has_redundancy=r.has_redundancy||0,n.degradation_priority=r.degradation_priority||0,n.offset=0,n.subsamples=r.subsamples,s.samples.push(n),s.samples_size+=n.size,s.samples_duration+=n.duration,void 0===s.first_dts&&(s.first_dts=r.dts),this.processSamples();var o=this.createSingleSampleMoof(n);return this.addBox(o),o.computeSize(),o.trafs[0].truns[0].data_offset=o.size+8,this.add("mdat").data=new Uint8Array(t),n}},y.prototype.createSingleSampleMoof=function(e){var t=0;t=e.is_sync?1<<25:65536;var i=new d.moofBox;i.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var r=i.add("traf"),n=this.getTrackById(e.track_id);return r.add("tfhd").set("track_id",e.track_id).set("flags",d.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),r.add("tfdt").set("baseMediaDecodeTime",e.dts-(n.first_dts||0)),r.add("trun").set("flags",d.TRUN_FLAGS_DATA_OFFSET|d.TRUN_FLAGS_DURATION|d.TRUN_FLAGS_SIZE|d.TRUN_FLAGS_FLAGS|d.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[e.duration]).set("sample_size",[e.size]).set("sample_flags",[t]).set("sample_composition_time_offset",[e.cts-e.dts]),i},y.prototype.lastMoofIndex=0,y.prototype.samplesDataSize=0,y.prototype.resetTables=function(){var e,t,i,r,n,s,o,a;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,e=0;e<this.moov.traks.length;e++){t=this.moov.traks[e],t.tkhd.duration=0,t.mdia.mdhd.duration=0,i=t.mdia.minf.stbl.stco||t.mdia.minf.stbl.co64,i.chunk_offsets=[],r=t.mdia.minf.stbl.stsc,r.first_chunk=[],r.samples_per_chunk=[],r.sample_description_index=[],n=t.mdia.minf.stbl.stsz||t.mdia.minf.stbl.stz2,n.sample_sizes=[],s=t.mdia.minf.stbl.stts,s.sample_counts=[],s.sample_deltas=[],o=t.mdia.minf.stbl.ctts,o&&(o.sample_counts=[],o.sample_offsets=[]),a=t.mdia.minf.stbl.stss;var l=t.mdia.minf.stbl.boxes.indexOf(a);-1!=l&&(t.mdia.minf.stbl.boxes[l]=null)}},y.initSampleGroups=function(e,t,i,r,n){var s,o,a,l;function c(e,t,i){this.grouping_type=e,this.grouping_type_parameter=t,this.sbgp=i,this.last_sample_in_run=-1,this.entry_index=-1}for(t&&(t.sample_groups_info=[]),e.sample_groups_info||(e.sample_groups_info=[]),o=0;o<i.length;o++){for(l=i[o].grouping_type+"/"+i[o].grouping_type_parameter,a=new c(i[o].grouping_type,i[o].grouping_type_parameter,i[o]),t&&(t.sample_groups_info[l]=a),e.sample_groups_info[l]||(e.sample_groups_info[l]=a),s=0;s<r.length;s++)r[s].grouping_type===i[o].grouping_type&&(a.description=r[s],a.description.used=!0);if(n)for(s=0;s<n.length;s++)n[s].grouping_type===i[o].grouping_type&&(a.fragment_description=n[s],a.fragment_description.used=!0,a.is_fragment=!0)}if(t){if(n)for(o=0;o<n.length;o++)!n[o].used&&n[o].version>=2&&(l=n[o].grouping_type+"/0",a=new c(n[o].grouping_type,0),a.is_fragment=!0,t.sample_groups_info[l]||(t.sample_groups_info[l]=a))}else for(o=0;o<r.length;o++)!r[o].used&&r[o].version>=2&&(l=r[o].grouping_type+"/0",a=new c(r[o].grouping_type,0),e.sample_groups_info[l]||(e.sample_groups_info[l]=a))},y.setSampleGroupProperties=function(e,t,i,r){var n,s;for(n in t.sample_groups=[],r){var o;if(t.sample_groups[n]={},t.sample_groups[n].grouping_type=r[n].grouping_type,t.sample_groups[n].grouping_type_parameter=r[n].grouping_type_parameter,i>=r[n].last_sample_in_run&&(r[n].last_sample_in_run<0&&(r[n].last_sample_in_run=0),r[n].entry_index++,r[n].entry_index<=r[n].sbgp.entries.length-1&&(r[n].last_sample_in_run+=r[n].sbgp.entries[r[n].entry_index].sample_count)),r[n].entry_index<=r[n].sbgp.entries.length-1?t.sample_groups[n].group_description_index=r[n].sbgp.entries[r[n].entry_index].group_description_index:t.sample_groups[n].group_description_index=-1,0!==t.sample_groups[n].group_description_index)o=r[n].fragment_description?r[n].fragment_description:r[n].description,t.sample_groups[n].group_description_index>0?(s=t.sample_groups[n].group_description_index>65535?(t.sample_groups[n].group_description_index>>16)-1:t.sample_groups[n].group_description_index-1,o&&s>=0&&(t.sample_groups[n].description=o.entries[s])):o&&o.version>=2&&o.default_group_description_index>0&&(t.sample_groups[n].description=o.entries[o.default_group_description_index-1])}},y.process_sdtp=function(e,t,i){t&&(e?(t.is_leading=e.is_leading[i],t.depends_on=e.sample_depends_on[i],t.is_depended_on=e.sample_is_depended_on[i],t.has_redundancy=e.sample_has_redundancy[i]):(t.is_leading=0,t.depends_on=0,t.is_depended_on=0,t.has_redundancy=0))},y.prototype.buildSampleLists=function(){var e,t;for(e=0;e<this.moov.traks.length;e++)t=this.moov.traks[e],this.buildTrakSampleLists(t)},y.prototype.buildTrakSampleLists=function(e){var t,i,r,n,s,o,a,l,c,d,u,h,p,f,m,g,_,v,b,S,x,k,C,U;if(e.samples=[],e.samples_duration=0,e.samples_size=0,i=e.mdia.minf.stbl.stco||e.mdia.minf.stbl.co64,r=e.mdia.minf.stbl.stsc,n=e.mdia.minf.stbl.stsz||e.mdia.minf.stbl.stz2,s=e.mdia.minf.stbl.stts,o=e.mdia.minf.stbl.ctts,a=e.mdia.minf.stbl.stss,l=e.mdia.minf.stbl.stsd,c=e.mdia.minf.stbl.subs,h=e.mdia.minf.stbl.stdp,d=e.mdia.minf.stbl.sbgps,u=e.mdia.minf.stbl.sgpds,v=-1,b=-1,S=-1,x=-1,k=0,C=0,U=0,y.initSampleGroups(e,null,d,u),"undefined"!==typeof n){for(t=0;t<n.sample_sizes.length;t++){var B={};B.number=t,B.track_id=e.tkhd.track_id,B.timescale=e.mdia.mdhd.timescale,B.alreadyRead=0,e.samples[t]=B,B.size=n.sample_sizes[t],e.samples_size+=B.size,0===t?(f=1,p=0,B.chunk_index=f,B.chunk_run_index=p,_=r.samples_per_chunk[p],g=0,m=p+1<r.first_chunk.length?r.first_chunk[p+1]-1:1/0):t<_?(B.chunk_index=f,B.chunk_run_index=p):(f++,B.chunk_index=f,g=0,f<=m||(p++,m=p+1<r.first_chunk.length?r.first_chunk[p+1]-1:1/0),B.chunk_run_index=p,_+=r.samples_per_chunk[p]),B.description_index=r.sample_description_index[B.chunk_run_index]-1,B.description=l.entries[B.description_index],B.offset=i.chunk_offsets[B.chunk_index-1]+g,g+=B.size,t>v&&(b++,v<0&&(v=0),v+=s.sample_counts[b]),t>0?(e.samples[t-1].duration=s.sample_deltas[b],e.samples_duration+=e.samples[t-1].duration,B.dts=e.samples[t-1].dts+e.samples[t-1].duration):B.dts=0,o?(t>=S&&(x++,S<0&&(S=0),S+=o.sample_counts[x]),B.cts=e.samples[t].dts+o.sample_offsets[x]):B.cts=B.dts,a?(t==a.sample_numbers[k]-1?(B.is_sync=!0,k++):(B.is_sync=!1,B.degradation_priority=0),c&&c.entries[C].sample_delta+U==t+1&&(B.subsamples=c.entries[C].subsamples,U+=c.entries[C].sample_delta,C++)):B.is_sync=!0,y.process_sdtp(e.mdia.minf.stbl.sdtp,B,B.number),B.degradation_priority=h?h.priority[t]:0,c&&c.entries[C].sample_delta+U==t&&(B.subsamples=c.entries[C].subsamples,U+=c.entries[C].sample_delta),(d.length>0||u.length>0)&&y.setSampleGroupProperties(e,B,t,e.sample_groups_info)}t>0&&(e.samples[t-1].duration=Math.max(e.mdia.mdhd.duration-e.samples[t-1].dts,0),e.samples_duration+=e.samples[t-1].duration)}},y.prototype.updateSampleLists=function(){var e,t,i,r,n,s,o,a,l,c,u,h,p,f,m;if(void 0!==this.moov)while(this.lastMoofIndex<this.moofs.length)if(l=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==l.type)for(c=l,e=0;e<c.trafs.length;e++){for(u=c.trafs[e],h=this.getTrackById(u.tfhd.track_id),p=this.getTrexById(u.tfhd.track_id),r=u.tfhd.flags&d.TFHD_FLAG_SAMPLE_DESC?u.tfhd.default_sample_description_index:p?p.default_sample_description_index:1,n=u.tfhd.flags&d.TFHD_FLAG_SAMPLE_DUR?u.tfhd.default_sample_duration:p?p.default_sample_duration:0,s=u.tfhd.flags&d.TFHD_FLAG_SAMPLE_SIZE?u.tfhd.default_sample_size:p?p.default_sample_size:0,o=u.tfhd.flags&d.TFHD_FLAG_SAMPLE_FLAGS?u.tfhd.default_sample_flags:p?p.default_sample_flags:0,u.sample_number=0,u.sbgps.length>0&&y.initSampleGroups(h,u,u.sbgps,h.mdia.minf.stbl.sgpds,u.sgpds),t=0;t<u.truns.length;t++){var g=u.truns[t];for(i=0;i<g.sample_count;i++){f={},f.moof_number=this.lastMoofIndex,f.number_in_traf=u.sample_number,u.sample_number++,f.number=h.samples.length,u.first_sample_index=h.samples.length,h.samples.push(f),f.track_id=h.tkhd.track_id,f.timescale=h.mdia.mdhd.timescale,f.description_index=r-1,f.description=h.mdia.minf.stbl.stsd.entries[f.description_index],f.size=s,g.flags&d.TRUN_FLAGS_SIZE&&(f.size=g.sample_size[i]),h.samples_size+=f.size,f.duration=n,g.flags&d.TRUN_FLAGS_DURATION&&(f.duration=g.sample_duration[i]),h.samples_duration+=f.duration,h.first_traf_merged||i>0?f.dts=h.samples[h.samples.length-2].dts+h.samples[h.samples.length-2].duration:(u.tfdt?f.dts=u.tfdt.baseMediaDecodeTime:f.dts=0,h.first_traf_merged=!0),f.cts=f.dts,g.flags&d.TRUN_FLAGS_CTS_OFFSET&&(f.cts=f.dts+g.sample_composition_time_offset[i]),m=o,g.flags&d.TRUN_FLAGS_FLAGS?m=g.sample_flags[i]:0===i&&g.flags&d.TRUN_FLAGS_FIRST_FLAG&&(m=g.first_sample_flags),f.is_sync=!(m>>16&1),f.is_leading=m>>26&3,f.depends_on=m>>24&3,f.is_depended_on=m>>22&3,f.has_redundancy=m>>20&3,f.degradation_priority=65535&m;var _=!!(u.tfhd.flags&d.TFHD_FLAG_BASE_DATA_OFFSET),v=!!(u.tfhd.flags&d.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),b=!!(g.flags&d.TRUN_FLAGS_DATA_OFFSET),S=0;S=_?u.tfhd.base_data_offset:v||0===t?c.start:a,f.offset=0===t&&0===i?b?S+g.data_offset:S:a,a=f.offset+f.size,(u.sbgps.length>0||u.sgpds.length>0||h.mdia.minf.stbl.sbgps.length>0||h.mdia.minf.stbl.sgpds.length>0)&&y.setSampleGroupProperties(h,f,f.number_in_traf,u.sample_groups_info)}}if(u.subs){h.has_fragment_subsamples=!0;var x=u.first_sample_index;for(t=0;t<u.subs.entries.length;t++)x+=u.subs.entries[t].sample_delta,f=h.samples[x-1],f.subsamples=u.subs.entries[t].subsamples}}},y.prototype.getSample=function(e,t){var i,r=e.samples[t];if(!this.moov)return null;if(r.data){if(r.alreadyRead==r.size)return r}else r.data=new Uint8Array(r.size),r.alreadyRead=0,this.samplesDataSize+=r.size,n.debug("ISOFile","Allocating sample #"+t+" on track #"+e.tkhd.track_id+" of size "+r.size+" (total: "+this.samplesDataSize+")");while(1){var s=this.stream.findPosition(!0,r.offset+r.alreadyRead,!1);if(!(s>-1))return null;i=this.stream.buffers[s];var a=i.byteLength-(r.offset+r.alreadyRead-i.fileStart);if(r.size-r.alreadyRead<=a)return n.debug("ISOFile","Getting sample #"+t+" data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+(r.size-r.alreadyRead)+" full size: "+r.size+")"),o.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,r.size-r.alreadyRead),i.usedBytes+=r.size-r.alreadyRead,this.stream.logBufferLevel(),r.alreadyRead=r.size,r;if(0===a)return null;n.debug("ISOFile","Getting sample #"+t+" partial data (alreadyRead: "+r.alreadyRead+" offset: "+(r.offset+r.alreadyRead-i.fileStart)+" read size: "+a+" full size: "+r.size+")"),o.memcpy(r.data.buffer,r.alreadyRead,i,r.offset+r.alreadyRead-i.fileStart,a),r.alreadyRead+=a,i.usedBytes+=a,this.stream.logBufferLevel()}},y.prototype.releaseSample=function(e,t){var i=e.samples[t];return i.data?(this.samplesDataSize-=i.size,i.data=null,i.alreadyRead=0,i.size):0},y.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},y.prototype.getCodecs=function(){var e,t="";for(e=0;e<this.moov.traks.length;e++){var i=this.moov.traks[e];e>0&&(t+=","),t+=i.mdia.minf.stbl.stsd.entries[0].getCodec()}return t},y.prototype.getTrexById=function(e){var t;if(!this.moov||!this.moov.mvex)return null;for(t=0;t<this.moov.mvex.trexs.length;t++){var i=this.moov.mvex.trexs[t];if(i.track_id==e)return i}return null},y.prototype.getTrackById=function(e){if(void 0===this.moov)return null;for(var t=0;t<this.moov.traks.length;t++){var i=this.moov.traks[t];if(i.tkhd.track_id==e)return i}return null},y.prototype.itemsDataSize=0,y.prototype.flattenItemInfo=function(){var e,t,i,r=this.items,s=this.entity_groups,o=this.meta;if(null!==o&&void 0!==o&&void 0!==o.hdlr&&void 0!==o.iinf){for(e=0;e<o.iinf.item_infos.length;e++)i={},i.id=o.iinf.item_infos[e].item_ID,r[i.id]=i,i.ref_to=[],i.name=o.iinf.item_infos[e].item_name,o.iinf.item_infos[e].protection_index>0&&(i.protection=o.ipro.protections[o.iinf.item_infos[e].protection_index-1]),o.iinf.item_infos[e].item_type?i.type=o.iinf.item_infos[e].item_type:i.type="mime",i.content_type=o.iinf.item_infos[e].content_type,i.content_encoding=o.iinf.item_infos[e].content_encoding,i.item_uri_type=o.iinf.item_infos[e].item_uri_type;if(o.grpl)for(e=0;e<o.grpl.boxes.length;e++)entity_group={},entity_group.id=o.grpl.boxes[e].group_id,entity_group.entity_ids=o.grpl.boxes[e].entity_ids,entity_group.type=o.grpl.boxes[e].type,s[entity_group.id]=entity_group;if(o.iloc)for(e=0;e<o.iloc.items.length;e++){var a=o.iloc.items[e];switch(i=r[a.item_ID],0!==a.data_reference_index&&(n.warn("Item storage with reference to other files: not supported"),i.source=o.dinf.boxes[a.data_reference_index-1]),a.construction_method){case 0:break;case 1:break;case 2:n.warn("Item storage with construction_method : not supported");break}for(i.extents=[],i.size=0,t=0;t<a.extents.length;t++)i.extents[t]={},i.extents[t].offset=a.extents[t].extent_offset+a.base_offset,1==a.construction_method&&(i.extents[t].offset+=o.idat.start+o.idat.hdr_size),i.extents[t].length=a.extents[t].extent_length,i.extents[t].alreadyRead=0,i.size+=i.extents[t].length}if(o.pitm&&(r[o.pitm.item_id].primary=!0),o.iref)for(e=0;e<o.iref.references.length;e++){var l=o.iref.references[e];for(t=0;t<l.references.length;t++)r[l.from_item_ID].ref_to.push({type:l.type,id:l.references[t]})}if(o.iprp)for(var c=0;c<o.iprp.ipmas.length;c++){var d=o.iprp.ipmas[c];for(e=0;e<d.associations.length;e++){var u=d.associations[e];if(i=r[u.id],i||(i=s[u.id]),i)for(void 0===i.properties&&(i.properties={},i.properties.boxes=[]),t=0;t<u.props.length;t++){var h=u.props[t];if(h.property_index>0&&h.property_index-1<o.iprp.ipco.boxes.length){var p=o.iprp.ipco.boxes[h.property_index-1];i.properties[p.type]=p,i.properties.boxes.push(p)}}}}}},y.prototype.getItem=function(e){var t,i;if(!this.meta)return null;if(i=this.items[e],!i.data&&i.size)i.data=new Uint8Array(i.size),i.alreadyRead=0,this.itemsDataSize+=i.size,n.debug("ISOFile","Allocating item #"+e+" of size "+i.size+" (total: "+this.itemsDataSize+")");else if(i.alreadyRead===i.size)return i;for(var r=0;r<i.extents.length;r++){var s=i.extents[r];if(s.alreadyRead!==s.length){var a=this.stream.findPosition(!0,s.offset+s.alreadyRead,!1);if(!(a>-1))return null;t=this.stream.buffers[a];var l=t.byteLength-(s.offset+s.alreadyRead-t.fileStart);if(!(s.length-s.alreadyRead<=l))return n.debug("ISOFile","Getting item #"+e+" extent #"+r+" partial data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+l+" full extent size: "+s.length+" full item size: "+i.size+")"),o.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,l),s.alreadyRead+=l,i.alreadyRead+=l,t.usedBytes+=l,this.stream.logBufferLevel(),null;n.debug("ISOFile","Getting item #"+e+" extent #"+r+" data (alreadyRead: "+s.alreadyRead+" offset: "+(s.offset+s.alreadyRead-t.fileStart)+" read size: "+(s.length-s.alreadyRead)+" full extent size: "+s.length+" full item size: "+i.size+")"),o.memcpy(i.data.buffer,i.alreadyRead,t,s.offset+s.alreadyRead-t.fileStart,s.length-s.alreadyRead),t.usedBytes+=s.length-s.alreadyRead,this.stream.logBufferLevel(),i.alreadyRead+=s.length-s.alreadyRead,s.alreadyRead=s.length}}return i.alreadyRead===i.size?i:null},y.prototype.releaseItem=function(e){var t=this.items[e];if(t.data){this.itemsDataSize-=t.size,t.data=null,t.alreadyRead=0;for(var i=0;i<t.extents.length;i++){var r=t.extents[i];r.alreadyRead=0}return t.size}return 0},y.prototype.processItems=function(e){for(var t in this.items){var i=this.items[t];this.getItem(i.id),e&&!i.sent&&(e(i),i.sent=!0,i.data=null)}},y.prototype.hasItem=function(e){for(var t in this.items){var i=this.items[t];if(i.name===e)return i.id}return-1},y.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},y.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},y.prototype.itemToFragmentedTrackFile=function(e){var t=e||{},i=null;if(i=t.itemId?this.getItem(t.itemId):this.getPrimaryItem(),null==i)return null;var r=new y;r.discardMdatData=!1;var n={type:i.type,description_boxes:i.properties.boxes};i.properties.ispe&&(n.width=i.properties.ispe.image_width,n.height=i.properties.ispe.image_height);var s=r.addTrack(n);return s?(r.addSample(s,i.data),r):null},y.prototype.write=function(e){for(var t=0;t<this.boxes.length;t++)this.boxes[t].write(e)},y.prototype.createFragment=function(e,t,i){var r=this.getTrackById(e),s=this.getSample(r,t);if(null==s)return this.setNextSeekPositionFromSample(r.samples[t]),null;var a=i||new o;a.endianness=o.BIG_ENDIAN;var l=this.createSingleSampleMoof(s);l.write(a),l.trafs[0].truns[0].data_offset=l.size+8,n.debug("MP4Box","Adjusting data_offset with new value "+l.trafs[0].truns[0].data_offset),a.adjustUint32(l.trafs[0].truns[0].data_offset_position,l.trafs[0].truns[0].data_offset);var c=new d.mdatBox;return c.data=s.data,c.write(a),a},y.writeInitializationSegment=function(e,t,i,r){var s;n.debug("ISOFile","Generating initialization segment");var a=new o;a.endianness=o.BIG_ENDIAN,e.write(a);var l=t.add("mvex");for(i&&l.add("mehd").set("fragment_duration",i),s=0;s<t.traks.length;s++)l.add("trex").set("track_id",t.traks[s].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",r).set("default_sample_size",0).set("default_sample_flags",65536);return t.write(a),a.buffer},y.prototype.save=function(e){var t=new o;t.endianness=o.BIG_ENDIAN,this.write(t),t.save(e)},y.prototype.getBuffer=function(){var e=new o;return e.endianness=o.BIG_ENDIAN,this.write(e),e.buffer},y.prototype.initializeSegmentation=function(){var e,t,i,r;for(null===this.onSegment&&n.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.resetTables()),t=[],e=0;e<this.fragmentedTracks.length;e++){var s=new d.moovBox;s.mvhd=this.moov.mvhd,s.boxes.push(s.mvhd),i=this.getTrackById(this.fragmentedTracks[e].id),s.boxes.push(i),s.traks.push(i),r={},r.id=i.tkhd.track_id,r.user=this.fragmentedTracks[e].user,r.buffer=y.writeInitializationSegment(this.ftyp,s,this.moov.mvex&&this.moov.mvex.mehd?this.moov.mvex.mehd.fragment_duration:void 0,this.moov.traks[e].samples.length>0?this.moov.traks[e].samples[0].duration:0),t.push(r)}return t},d.Box.prototype.printHeader=function(e){this.size+=8,this.size>a&&(this.size+=8),"uuid"===this.type&&(this.size+=16),e.log(e.indent+"size:"+this.size),e.log(e.indent+"type:"+this.type)},d.FullBox.prototype.printHeader=function(e){this.size+=4,d.Box.prototype.printHeader.call(this,e),e.log(e.indent+"version:"+this.version),e.log(e.indent+"flags:"+this.flags)},d.Box.prototype.print=function(e){this.printHeader(e)},d.ContainerBox.prototype.print=function(e){this.printHeader(e);for(var t=0;t<this.boxes.length;t++)if(this.boxes[t]){var i=e.indent;e.indent+=" ",this.boxes[t].print(e),e.indent=i}},y.prototype.print=function(e){e.indent="";for(var t=0;t<this.boxes.length;t++)this.boxes[t]&&this.boxes[t].print(e)},d.mvhdBox.prototype.print=function(e){d.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"timescale: "+this.timescale),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"rate: "+this.rate),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"next_track_id: "+this.next_track_id)},d.tkhdBox.prototype.print=function(e){d.FullBox.prototype.printHeader.call(this,e),e.log(e.indent+"creation_time: "+this.creation_time),e.log(e.indent+"modification_time: "+this.modification_time),e.log(e.indent+"track_id: "+this.track_id),e.log(e.indent+"duration: "+this.duration),e.log(e.indent+"volume: "+(this.volume>>8)),e.log(e.indent+"matrix: "+this.matrix.join(", ")),e.log(e.indent+"layer: "+this.layer),e.log(e.indent+"alternate_group: "+this.alternate_group),e.log(e.indent+"width: "+this.width),e.log(e.indent+"height: "+this.height)};var _={createFile:function(e,t){var i=void 0===e||e,r=new y(t);return r.discardMdatData=!i,r}};t.createFile=_.createFile},"1e5a":function(e,t,i){"use strict";var r=i("23e7"),n=i("9961"),s=i("5320"),o=i("dad2"),a=!o("symmetricDifference")||!s("symmetricDifference");r({target:"Set",proto:!0,real:!0,forced:a},{symmetricDifference:n})},"1e70":function(e,t,i){"use strict";var r=i("23e7"),n=i("a5f7"),s=i("d039"),o=i("dad2"),a=!o("difference",(function(e){return 0===e.size})),l=a||s((function(){var e={size:1,has:function(){return!0},keys:function(){var e=0;return{next:function(){var i=e++>1;return t.has(1)&&t.clear(),{done:i,value:2}}}}},t=new Set([1,2,3,4]);return 3!==t.difference(e).size}));r({target:"Set",proto:!0,real:!0,forced:l},{difference:n})},"384f":function(e,t,i){"use strict";var r=i("e330"),n=i("5388"),s=i("cb27"),o=s.Set,a=s.proto,l=r(a.forEach),c=r(a.keys),d=c(new o).next;e.exports=function(e,t,i){return i?n({iterator:c(e),next:d},t):l(e,t)}},"395e":function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27").has,s=i("8e16"),o=i("7f65"),a=i("5388"),l=i("2a62");e.exports=function(e){var t=r(this),i=o(e);if(s(t)<i.size)return!1;var c=i.getIterator();return!1!==a(c,(function(e){if(!n(t,e))return l(c,"normal",!1)}))}},"40b3":function(e,t,i){(function(t,i){e.exports=i()})(0,(function(){return function(e){var t={};function i(r){if(t[r])return t[r].exports;var n=t[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=e,i.c=t,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="/dist/",i(i.s="./index.js")}({"./index.js":
/*!******************!*\
  !*** ./index.js ***!
  \******************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./src/cos */"./src/cos.js");e.exports=r},"./lib/base64.js":
/*!***********************!*\
  !*** ./lib/base64.js ***!
  \***********************/
/*! no static exports found */function(e,t){var i=function(e){e=e||{};var t,i=e.Base64,r="2.1.9",n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=function(e){for(var t={},i=0,r=e.length;i<r;i++)t[e.charAt(i)]=i;return t}(n),o=String.fromCharCode,a=function(e){if(e.length<2){var t=e.charCodeAt(0);return t<128?e:t<2048?o(192|t>>>6)+o(128|63&t):o(224|t>>>12&15)+o(128|t>>>6&63)+o(128|63&t)}t=65536+1024*(e.charCodeAt(0)-55296)+(e.charCodeAt(1)-56320);return o(240|t>>>18&7)+o(128|t>>>12&63)+o(128|t>>>6&63)+o(128|63&t)},l=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,c=function(e){return e.replace(l,a)},d=function(e){var t=[0,2,1][e.length%3],i=e.charCodeAt(0)<<16|(e.length>1?e.charCodeAt(1):0)<<8|(e.length>2?e.charCodeAt(2):0),r=[n.charAt(i>>>18),n.charAt(i>>>12&63),t>=2?"=":n.charAt(i>>>6&63),t>=1?"=":n.charAt(63&i)];return r.join("")},u=e.btoa?function(t){return e.btoa(t)}:function(e){return e.replace(/[\s\S]{1,3}/g,d)},h=t?function(e){return(e.constructor===t.constructor?e:new t(e)).toString("base64")}:function(e){return u(c(e))},p=function(e,t){return t?h(String(e)).replace(/[+\/]/g,(function(e){return"+"==e?"-":"_"})).replace(/=/g,""):h(String(e))},f=function(e){return p(e,!0)},m=new RegExp(["[À-ß][-¿]","[à-ï][-¿]{2}","[ð-÷][-¿]{3}"].join("|"),"g"),g=function(e){switch(e.length){case 4:var t=(7&e.charCodeAt(0))<<18|(63&e.charCodeAt(1))<<12|(63&e.charCodeAt(2))<<6|63&e.charCodeAt(3),i=t-65536;return o(55296+(i>>>10))+o(56320+(1023&i));case 3:return o((15&e.charCodeAt(0))<<12|(63&e.charCodeAt(1))<<6|63&e.charCodeAt(2));default:return o((31&e.charCodeAt(0))<<6|63&e.charCodeAt(1))}},y=function(e){return e.replace(m,g)},_=function(e){var t=e.length,i=t%4,r=(t>0?s[e.charAt(0)]<<18:0)|(t>1?s[e.charAt(1)]<<12:0)|(t>2?s[e.charAt(2)]<<6:0)|(t>3?s[e.charAt(3)]:0),n=[o(r>>>16),o(r>>>8&255),o(255&r)];return n.length-=[0,0,2,1][i],n.join("")},v=e.atob?function(t){return e.atob(t)}:function(e){return e.replace(/[\s\S]{1,4}/g,_)},b=t?function(e){return(e.constructor===t.constructor?e:new t(e,"base64")).toString()}:function(e){return y(v(e))},S=function(e){return b(String(e).replace(/[-_]/g,(function(e){return"-"==e?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,""))},x=function(){var t=e.Base64;return e.Base64=i,t},k={VERSION:r,atob:v,btoa:u,fromBase64:S,toBase64:p,utob:c,encode:p,encodeURI:f,btou:y,decode:S,noConflict:x};return k}();e.exports=i},"./lib/crypto.js":
/*!***********************!*\
  !*** ./lib/crypto.js ***!
  \***********************/
/*! no static exports found */function(e,t,i){(function(e){var t=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js"),r=r||function(e,t){var i={},r=i.lib={},n=function(){},s=r.Base={extend:function(e){n.prototype=this;var t=new n;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},o=r.WordArray=s.extend({init:function(e,i){e=this.words=e||[],this.sigBytes=i!=t?i:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,i=e.words,r=this.sigBytes;if(e=e.sigBytes,this.clamp(),r%4)for(var n=0;n<e;n++)t[r+n>>>2]|=(i[n>>>2]>>>24-n%4*8&255)<<24-(r+n)%4*8;else if(65535<i.length)for(n=0;n<e;n+=4)t[r+n>>>2]=i[n>>>2];else t.push.apply(t,i);return this.sigBytes+=e,this},clamp:function(){var t=this.words,i=this.sigBytes;t[i>>>2]&=4294967295<<32-i%4*8,t.length=e.ceil(i/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var i=[],r=0;r<t;r+=4)i.push(4294967296*e.random()|0);return new o.init(i,t)}}),a=i.enc={},l=a.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var i=[],r=0;r<e;r++){var n=t[r>>>2]>>>24-r%4*8&255;i.push((n>>>4).toString(16)),i.push((15&n).toString(16))}return i.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r+=2)i[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new o.init(i,t/2)}},c=a.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var i=[],r=0;r<e;r++)i.push(String.fromCharCode(t[r>>>2]>>>24-r%4*8&255));return i.join("")},parse:function(e){for(var t=e.length,i=[],r=0;r<t;r++)i[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new o.init(i,t)}},d=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(t){throw Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},u=r.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new o.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=d.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var i=this._data,r=i.words,n=i.sigBytes,s=this.blockSize,a=n/(4*s);a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0);if(t=a*s,n=e.min(4*t,n),t){for(var l=0;l<t;l+=s)this._doProcessBlock(r,l);l=r.splice(0,t),i.sigBytes-=n}return new o.init(l,n)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=u.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){u.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,i){return new e.init(i).finalize(t)}},_createHmacHelper:function(e){return function(t,i){return new h.HMAC.init(e,i).finalize(t)}}});var h=i.algo={};return i}(Math);(function(){var e=r,t=e.lib,i=t.WordArray,n=t.Hasher,s=[];t=e.algo.SHA1=n.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var i=this._hash.words,r=i[0],n=i[1],o=i[2],a=i[3],l=i[4],c=0;80>c;c++){if(16>c)s[c]=0|e[t+c];else{var d=s[c-3]^s[c-8]^s[c-14]^s[c-16];s[c]=d<<1|d>>>31}d=(r<<5|r>>>27)+l+s[c],d=20>c?d+(1518500249+(n&o|~n&a)):40>c?d+(1859775393+(n^o^a)):60>c?d+((n&o|n&a|o&a)-1894007588):d+((n^o^a)-899497514),l=a,a=o,o=n<<30|n>>>2,n=r,r=d}i[0]=i[0]+r|0,i[1]=i[1]+n|0,i[2]=i[2]+o|0,i[3]=i[3]+a|0,i[4]=i[4]+l|0},_doFinalize:function(){var e=this._data,t=e.words,i=8*this._nDataBytes,r=8*e.sigBytes;return t[r>>>5]|=128<<24-r%32,t[14+(r+64>>>9<<4)]=Math.floor(i/4294967296),t[15+(r+64>>>9<<4)]=i,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=n.clone.call(this);return e._hash=this._hash.clone(),e}});e.SHA1=n._createHelper(t),e.HmacSHA1=n._createHmacHelper(t)})(),function(){var e=r,t=e.enc.Utf8;e.algo.HMAC=e.lib.Base.extend({init:function(e,i){e=this._hasher=new e.init,"string"==typeof i&&(i=t.parse(i));var r=e.blockSize,n=4*r;i.sigBytes>n&&(i=e.finalize(i)),i.clamp();for(var s=this._oKey=i.clone(),o=this._iKey=i.clone(),a=s.words,l=o.words,c=0;c<r;c++)a[c]^=1549556828,l[c]^=909522486;s.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}})}(),function(){var e=r,t=e.lib,i=t.WordArray,n=e.enc;n.Base64={stringify:function(e){var t=e.words,i=e.sigBytes,r=this._map;e.clamp();for(var n=[],s=0;s<i;s+=3)for(var o=t[s>>>2]>>>24-s%4*8&255,a=t[s+1>>>2]>>>24-(s+1)%4*8&255,l=t[s+2>>>2]>>>24-(s+2)%4*8&255,c=o<<16|a<<8|l,d=0;d<4&&s+.75*d<i;d++)n.push(r.charAt(c>>>6*(3-d)&63));var u=r.charAt(64);if(u)while(n.length%4)n.push(u);return n.join("")},parse:function(e){var t=e.length,r=this._map,n=r.charAt(64);if(n){var s=e.indexOf(n);-1!=s&&(t=s)}for(var o=[],a=0,l=0;l<t;l++)if(l%4){var c=r.indexOf(e.charAt(l-1))<<l%4*2,d=r.indexOf(e.charAt(l))>>>6-l%4*2;o[a>>>2]|=(c|d)<<24-a%4*8,a++}return i.create(o,a)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),"object"===t(e)?e.exports=r:window.CryptoJS=r}).call(this,i(/*! ./../node_modules/webpack/buildin/module.js */"./node_modules/webpack/buildin/module.js")(e))},"./lib/md5.js":
/*!********************!*\
  !*** ./lib/md5.js ***!
  \********************/
/*! no static exports found */function(e,t,i){(function(e){var t,r=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js");(function(){"use strict";var n="object"===("undefined"===typeof window?"undefined":r(window)),s=n?window:{};s.JS_MD5_NO_WINDOW&&(n=!1);var o=!n&&"object"===("undefined"===typeof self?"undefined":r(self));o&&(s=self);var a,l=!s.JS_MD5_NO_COMMON_JS&&"object"===r(e)&&e.exports,c=i(/*! !webpack amd options */"./node_modules/webpack/buildin/amd-options.js"),d=!s.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!==typeof ArrayBuffer,u="0123456789abcdef".split(""),h=[128,32768,8388608,-2147483648],p=[0,8,16,24],f=["hex","array","digest","buffer","arrayBuffer","base64"],m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),g=[];if(d){var y=new ArrayBuffer(68);a=new Uint8Array(y),g=new Uint32Array(y)}!s.JS_MD5_NO_NODE_JS&&Array.isArray||(Array.isArray=function(e){return"[object Array]"===Object.prototype.toString.call(e)}),!d||!s.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&ArrayBuffer.isView||(ArrayBuffer.isView=function(e){return"object"===r(e)&&e.buffer&&e.buffer.constructor===ArrayBuffer});var _=function(e){return function(t,i){return new b(!0).update(t,i)[e]()}},v=function(){var e=_("hex");e.getCtx=e.create=function(){return new b},e.update=function(t){return e.create().update(t)};for(var t=0;t<f.length;++t){var i=f[t];e[i]=_(i)}return e};function b(e){if(e)g[0]=g[16]=g[1]=g[2]=g[3]=g[4]=g[5]=g[6]=g[7]=g[8]=g[9]=g[10]=g[11]=g[12]=g[13]=g[14]=g[15]=0,this.blocks=g,this.buffer8=a;else if(d){var t=new ArrayBuffer(68);this.buffer8=new Uint8Array(t),this.blocks=new Uint32Array(t)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}b.prototype.update=function(e,t){if(!this.finalized){var i,r,n=0,s=e.length,o=this.blocks,a=this.buffer8;while(n<s){if(this.hashed&&(this.hashed=!1,o[0]=o[16],o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),d)for(r=this.start;n<s&&r<64;++n)i=e.charCodeAt(n),t||i<128?a[r++]=i:i<2048?(a[r++]=192|i>>6,a[r++]=128|63&i):i<55296||i>=57344?(a[r++]=224|i>>12,a[r++]=128|i>>6&63,a[r++]=128|63&i):(i=65536+((1023&i)<<10|1023&e.charCodeAt(++n)),a[r++]=240|i>>18,a[r++]=128|i>>12&63,a[r++]=128|i>>6&63,a[r++]=128|63&i);else for(r=this.start;n<s&&r<64;++n)i=e.charCodeAt(n),t||i<128?o[r>>2]|=i<<p[3&r++]:i<2048?(o[r>>2]|=(192|i>>6)<<p[3&r++],o[r>>2]|=(128|63&i)<<p[3&r++]):i<55296||i>=57344?(o[r>>2]|=(224|i>>12)<<p[3&r++],o[r>>2]|=(128|i>>6&63)<<p[3&r++],o[r>>2]|=(128|63&i)<<p[3&r++]):(i=65536+((1023&i)<<10|1023&e.charCodeAt(++n)),o[r>>2]|=(240|i>>18)<<p[3&r++],o[r>>2]|=(128|i>>12&63)<<p[3&r++],o[r>>2]|=(128|i>>6&63)<<p[3&r++],o[r>>2]|=(128|63&i)<<p[3&r++]);this.lastByteIndex=r,this.bytes+=r-this.start,r>=64?(this.start=r-64,this.hash(),this.hashed=!0):this.start=r}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296<<0,this.bytes=this.bytes%4294967296),this}},b.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[t>>2]|=h[3&t],t>=56&&(this.hashed||this.hash(),e[0]=e[16],e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.bytes<<3,e[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},b.prototype.hash=function(){var e,t,i,r,n,s,o=this.blocks;this.first?(e=o[0]-680876937,e=(e<<7|e>>>25)-271733879<<0,r=(-1732584194^2004318071&e)+o[1]-117830708,r=(r<<12|r>>>20)+e<<0,i=(-271733879^r&(-271733879^e))+o[2]-1126478375,i=(i<<17|i>>>15)+r<<0,t=(e^i&(r^e))+o[3]-1316259209,t=(t<<22|t>>>10)+i<<0):(e=this.h0,t=this.h1,i=this.h2,r=this.h3,e+=(r^t&(i^r))+o[0]-680876936,e=(e<<7|e>>>25)+t<<0,r+=(i^e&(t^i))+o[1]-389564586,r=(r<<12|r>>>20)+e<<0,i+=(t^r&(e^t))+o[2]+606105819,i=(i<<17|i>>>15)+r<<0,t+=(e^i&(r^e))+o[3]-1044525330,t=(t<<22|t>>>10)+i<<0),e+=(r^t&(i^r))+o[4]-176418897,e=(e<<7|e>>>25)+t<<0,r+=(i^e&(t^i))+o[5]+1200080426,r=(r<<12|r>>>20)+e<<0,i+=(t^r&(e^t))+o[6]-1473231341,i=(i<<17|i>>>15)+r<<0,t+=(e^i&(r^e))+o[7]-45705983,t=(t<<22|t>>>10)+i<<0,e+=(r^t&(i^r))+o[8]+1770035416,e=(e<<7|e>>>25)+t<<0,r+=(i^e&(t^i))+o[9]-1958414417,r=(r<<12|r>>>20)+e<<0,i+=(t^r&(e^t))+o[10]-42063,i=(i<<17|i>>>15)+r<<0,t+=(e^i&(r^e))+o[11]-1990404162,t=(t<<22|t>>>10)+i<<0,e+=(r^t&(i^r))+o[12]+1804603682,e=(e<<7|e>>>25)+t<<0,r+=(i^e&(t^i))+o[13]-40341101,r=(r<<12|r>>>20)+e<<0,i+=(t^r&(e^t))+o[14]-1502002290,i=(i<<17|i>>>15)+r<<0,t+=(e^i&(r^e))+o[15]+1236535329,t=(t<<22|t>>>10)+i<<0,e+=(i^r&(t^i))+o[1]-165796510,e=(e<<5|e>>>27)+t<<0,r+=(t^i&(e^t))+o[6]-1069501632,r=(r<<9|r>>>23)+e<<0,i+=(e^t&(r^e))+o[11]+643717713,i=(i<<14|i>>>18)+r<<0,t+=(r^e&(i^r))+o[0]-373897302,t=(t<<20|t>>>12)+i<<0,e+=(i^r&(t^i))+o[5]-701558691,e=(e<<5|e>>>27)+t<<0,r+=(t^i&(e^t))+o[10]+38016083,r=(r<<9|r>>>23)+e<<0,i+=(e^t&(r^e))+o[15]-660478335,i=(i<<14|i>>>18)+r<<0,t+=(r^e&(i^r))+o[4]-405537848,t=(t<<20|t>>>12)+i<<0,e+=(i^r&(t^i))+o[9]+568446438,e=(e<<5|e>>>27)+t<<0,r+=(t^i&(e^t))+o[14]-1019803690,r=(r<<9|r>>>23)+e<<0,i+=(e^t&(r^e))+o[3]-187363961,i=(i<<14|i>>>18)+r<<0,t+=(r^e&(i^r))+o[8]+1163531501,t=(t<<20|t>>>12)+i<<0,e+=(i^r&(t^i))+o[13]-1444681467,e=(e<<5|e>>>27)+t<<0,r+=(t^i&(e^t))+o[2]-51403784,r=(r<<9|r>>>23)+e<<0,i+=(e^t&(r^e))+o[7]+1735328473,i=(i<<14|i>>>18)+r<<0,t+=(r^e&(i^r))+o[12]-1926607734,t=(t<<20|t>>>12)+i<<0,n=t^i,e+=(n^r)+o[5]-378558,e=(e<<4|e>>>28)+t<<0,r+=(n^e)+o[8]-2022574463,r=(r<<11|r>>>21)+e<<0,s=r^e,i+=(s^t)+o[11]+1839030562,i=(i<<16|i>>>16)+r<<0,t+=(s^i)+o[14]-35309556,t=(t<<23|t>>>9)+i<<0,n=t^i,e+=(n^r)+o[1]-1530992060,e=(e<<4|e>>>28)+t<<0,r+=(n^e)+o[4]+1272893353,r=(r<<11|r>>>21)+e<<0,s=r^e,i+=(s^t)+o[7]-155497632,i=(i<<16|i>>>16)+r<<0,t+=(s^i)+o[10]-1094730640,t=(t<<23|t>>>9)+i<<0,n=t^i,e+=(n^r)+o[13]+681279174,e=(e<<4|e>>>28)+t<<0,r+=(n^e)+o[0]-358537222,r=(r<<11|r>>>21)+e<<0,s=r^e,i+=(s^t)+o[3]-722521979,i=(i<<16|i>>>16)+r<<0,t+=(s^i)+o[6]+76029189,t=(t<<23|t>>>9)+i<<0,n=t^i,e+=(n^r)+o[9]-640364487,e=(e<<4|e>>>28)+t<<0,r+=(n^e)+o[12]-421815835,r=(r<<11|r>>>21)+e<<0,s=r^e,i+=(s^t)+o[15]+530742520,i=(i<<16|i>>>16)+r<<0,t+=(s^i)+o[2]-995338651,t=(t<<23|t>>>9)+i<<0,e+=(i^(t|~r))+o[0]-198630844,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~i))+o[7]+1126891415,r=(r<<10|r>>>22)+e<<0,i+=(e^(r|~t))+o[14]-1416354905,i=(i<<15|i>>>17)+r<<0,t+=(r^(i|~e))+o[5]-57434055,t=(t<<21|t>>>11)+i<<0,e+=(i^(t|~r))+o[12]+1700485571,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~i))+o[3]-1894986606,r=(r<<10|r>>>22)+e<<0,i+=(e^(r|~t))+o[10]-1051523,i=(i<<15|i>>>17)+r<<0,t+=(r^(i|~e))+o[1]-2054922799,t=(t<<21|t>>>11)+i<<0,e+=(i^(t|~r))+o[8]+1873313359,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~i))+o[15]-30611744,r=(r<<10|r>>>22)+e<<0,i+=(e^(r|~t))+o[6]-1560198380,i=(i<<15|i>>>17)+r<<0,t+=(r^(i|~e))+o[13]+1309151649,t=(t<<21|t>>>11)+i<<0,e+=(i^(t|~r))+o[4]-145523070,e=(e<<6|e>>>26)+t<<0,r+=(t^(e|~i))+o[11]-1120210379,r=(r<<10|r>>>22)+e<<0,i+=(e^(r|~t))+o[2]+718787259,i=(i<<15|i>>>17)+r<<0,t+=(r^(i|~e))+o[9]-343485551,t=(t<<21|t>>>11)+i<<0,this.first?(this.h0=e+1732584193<<0,this.h1=t-271733879<<0,this.h2=i-1732584194<<0,this.h3=r+271733878<<0,this.first=!1):(this.h0=this.h0+e<<0,this.h1=this.h1+t<<0,this.h2=this.h2+i<<0,this.h3=this.h3+r<<0)},b.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,i=this.h2,r=this.h3;return u[e>>4&15]+u[15&e]+u[e>>12&15]+u[e>>8&15]+u[e>>20&15]+u[e>>16&15]+u[e>>28&15]+u[e>>24&15]+u[t>>4&15]+u[15&t]+u[t>>12&15]+u[t>>8&15]+u[t>>20&15]+u[t>>16&15]+u[t>>28&15]+u[t>>24&15]+u[i>>4&15]+u[15&i]+u[i>>12&15]+u[i>>8&15]+u[i>>20&15]+u[i>>16&15]+u[i>>28&15]+u[i>>24&15]+u[r>>4&15]+u[15&r]+u[r>>12&15]+u[r>>8&15]+u[r>>20&15]+u[r>>16&15]+u[r>>28&15]+u[r>>24&15]},b.prototype.toString=b.prototype.hex,b.prototype.digest=function(e){if("hex"===e)return this.hex();this.finalize();var t=this.h0,i=this.h1,r=this.h2,n=this.h3,s=[255&t,t>>8&255,t>>16&255,t>>24&255,255&i,i>>8&255,i>>16&255,i>>24&255,255&r,r>>8&255,r>>16&255,r>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255];return s},b.prototype.array=b.prototype.digest,b.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(16),t=new Uint32Array(e);return t[0]=this.h0,t[1]=this.h1,t[2]=this.h2,t[3]=this.h3,e},b.prototype.buffer=b.prototype.arrayBuffer,b.prototype.base64=function(){for(var e,t,i,r="",n=this.array(),s=0;s<15;)e=n[s++],t=n[s++],i=n[s++],r+=m[e>>>2]+m[63&(e<<4|t>>>4)]+m[63&(t<<2|i>>>6)]+m[63&i];return e=n[s],r+=m[e>>>2]+m[e<<4&63]+"==",r};var S=v();l?e.exports=S:(s.md5=S,c&&(t=function(){return S}.call(S,i,S,e),void 0===t||(e.exports=t)))})()}).call(this,i(/*! ./../node_modules/webpack/buildin/module.js */"./node_modules/webpack/buildin/module.js")(e))},"./lib/request.js":
/*!************************!*\
  !*** ./lib/request.js ***!
  \************************/
/*! no static exports found */function(e,t,i){var r=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js"),n=function(e){switch(r(e)){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}},s=function(e,t,i,s){return t=t||"&",i=i||"=",null===e&&(e=void 0),"object"===r(e)?Object.keys(e).map((function(r){var s=encodeURIComponent(n(r))+i;return Array.isArray(e[r])?e[r].map((function(e){return s+encodeURIComponent(n(e))})).join(t):s+encodeURIComponent(n(e[r]))})).filter(Boolean).join(t):s?encodeURIComponent(n(s))+i+encodeURIComponent(n(e)):""},o=function(e,t,i){var r={},n=t.getAllResponseHeaders();return n&&n.length>0&&n.trim().split("\n").forEach((function(e){if(e){var t=e.indexOf(":"),i=e.substr(0,t).trim().toLowerCase(),n=e.substr(t+1).trim();r[i]=n}})),{error:e,statusCode:t.status,statusMessage:t.statusText,headers:r,body:i}},a=function(e,t){return t||"text"!==t?e.response:e.responseText},l=function(e,t){var i=(e.method||"GET").toUpperCase(),r=e.url;if(e.qs){var n=s(e.qs);n&&(r+=(-1===r.indexOf("?")?"?":"&")+n)}var l=new XMLHttpRequest;if(l.open(i,r,!0),l.responseType=e.dataType||"text",e.xhrFields)for(var c in e.xhrFields)l[c]=e.xhrFields[c];var d=e.headers;if(d)for(var u in d)d.hasOwnProperty(u)&&"content-length"!==u.toLowerCase()&&"user-agent"!==u.toLowerCase()&&"origin"!==u.toLowerCase()&&"host"!==u.toLowerCase()&&l.setRequestHeader(u,d[u]);return e.onProgress&&l.upload&&(l.upload.onprogress=e.onProgress),e.onDownloadProgress&&(l.onprogress=e.onDownloadProgress),e.timeout&&(l.timeout=e.timeout),l.ontimeout=function(e){var i=new Error("timeout");t(o(i,l))},l.onload=function(){t(o(null,l,a(l,e.dataType)))},l.onerror=function(i){var r=a(l,e.dataType);if(r)t(o(null,l,r));else{var n=l.statusText;n||0!==l.status||(n=new Error("CORS blocked or network error")),t(o(n,l,r))}},l.send(e.body||""),l};e.exports=l},"./node_modules/@babel/runtime/helpers/classCallCheck.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/classCallCheck.js ***!
  \***************************************************************/
/*! no static exports found */function(e,t){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/@babel/runtime/helpers/createClass.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/createClass.js ***!
  \************************************************************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./toPropertyKey.js */"./node_modules/@babel/runtime/helpers/toPropertyKey.js");function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}function s(e,t,i){return t&&n(e.prototype,t),i&&n(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/@babel/runtime/helpers/defineProperty.js":
/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/defineProperty.js ***!
  \***************************************************************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./toPropertyKey.js */"./node_modules/@babel/runtime/helpers/toPropertyKey.js");function n(e,t,i){return(t=r(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/@babel/runtime/helpers/toPrimitive.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPrimitive.js ***!
  \************************************************************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./typeof.js */"./node_modules/@babel/runtime/helpers/typeof.js")["default"];function n(e,t){if("object"!=r(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0!==i){var n=i.call(e,t||"default");if("object"!=r(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}e.exports=n,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/@babel/runtime/helpers/toPropertyKey.js":
/*!**************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/toPropertyKey.js ***!
  \**************************************************************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./typeof.js */"./node_modules/@babel/runtime/helpers/typeof.js")["default"],n=i(/*! ./toPrimitive.js */"./node_modules/@babel/runtime/helpers/toPrimitive.js");function s(e){var t=n(e,"string");return"symbol"==r(t)?t:t+""}e.exports=s,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/@babel/runtime/helpers/typeof.js":
/*!*******************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/typeof.js ***!
  \*******************************************************/
/*! no static exports found */function(e,t){function i(t){return e.exports=i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports["default"]=e.exports,i(t)}e.exports=i,e.exports.__esModule=!0,e.exports["default"]=e.exports},"./node_modules/fast-xml-parser/src/fxp.js":
/*!*************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/fxp.js ***!
  \*************************************************/
/*! no static exports found */function(e,t,i){"use strict";const r=i(/*! ./validator */"./node_modules/fast-xml-parser/src/validator.js"),n=i(/*! ./xmlparser/XMLParser */"./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js"),s=i(/*! ./xmlbuilder/json2xml */"./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js");e.exports={XMLParser:n,XMLValidator:r,XMLBuilder:s}},"./node_modules/fast-xml-parser/src/ignoreAttributes.js":
/*!**************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/ignoreAttributes.js ***!
  \**************************************************************/
/*! no static exports found */function(e,t){function i(e){return"function"===typeof e?e:Array.isArray(e)?t=>{for(const i of e){if("string"===typeof i&&t===i)return!0;if(i instanceof RegExp&&i.test(t))return!0}}:()=>!1}e.exports=i},"./node_modules/fast-xml-parser/src/util.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/util.js ***!
  \**************************************************/
/*! no static exports found */function(e,t,i){"use strict";const r=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",n=r+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040",s="["+r+"]["+n+"]*",o=new RegExp("^"+s+"$"),a=function(e,t){const i=[];let r=t.exec(e);while(r){const n=[];n.startIndex=t.lastIndex-r[0].length;const s=r.length;for(let e=0;e<s;e++)n.push(r[e]);i.push(n),r=t.exec(e)}return i},l=function(e){const t=o.exec(e);return!(null===t||"undefined"===typeof t)};t.isExist=function(e){return"undefined"!==typeof e},t.isEmptyObject=function(e){return 0===Object.keys(e).length},t.merge=function(e,t,i){if(t){const r=Object.keys(t),n=r.length;for(let s=0;s<n;s++)e[r[s]]="strict"===i?[t[r[s]]]:t[r[s]]}},t.getValue=function(e){return t.isExist(e)?e:""},t.isName=l,t.getAllMatches=a,t.nameRegexp=s},"./node_modules/fast-xml-parser/src/validator.js":
/*!*******************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/validator.js ***!
  \*******************************************************/
/*! no static exports found */function(e,t,i){"use strict";const r=i(/*! ./util */"./node_modules/fast-xml-parser/src/util.js"),n={allowBooleanAttributes:!1,unpairedTags:[]};function s(e){return" "===e||"\t"===e||"\n"===e||"\r"===e}function o(e,t){const i=t;for(;t<e.length;t++)if("?"!=e[t]&&" "!=e[t]);else{const r=e.substr(i,t-i);if(t>5&&"xml"===r)return m("InvalidXml","XML declaration allowed only at the start of the document.",_(e,t));if("?"==e[t]&&">"==e[t+1]){t++;break}}return t}function a(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){let i=1;for(t+=8;t<e.length;t++)if("<"===e[t])i++;else if(">"===e[t]&&(i--,0===i))break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7])for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}return t}t.validate=function(e,t){t=Object.assign({},n,t);const i=[];let r=!1,l=!1;"\ufeff"===e[0]&&(e=e.substr(1));for(let n=0;n<e.length;n++)if("<"===e[n]&&"?"===e[n+1]){if(n+=2,n=o(e,n),n.err)return n}else{if("<"!==e[n]){if(s(e[n]))continue;return m("InvalidChar","char '"+e[n]+"' is not expected.",_(e,n))}{let c=n;if(n++,"!"===e[n]){n=a(e,n);continue}{let u=!1;"/"===e[n]&&(u=!0,n++);let p="";for(;n<e.length&&">"!==e[n]&&" "!==e[n]&&"\t"!==e[n]&&"\n"!==e[n]&&"\r"!==e[n];n++)p+=e[n];if(p=p.trim(),"/"===p[p.length-1]&&(p=p.substring(0,p.length-1),n--),!y(p)){let t;return t=0===p.trim().length?"Invalid space after '<'.":"Tag '"+p+"' is an invalid name.",m("InvalidTag",t,_(e,n))}const g=d(e,n);if(!1===g)return m("InvalidAttr","Attributes for '"+p+"' have open quote.",_(e,n));let v=g.value;if(n=g.index,"/"===v[v.length-1]){const i=n-v.length;v=v.substring(0,v.length-1);const s=h(v,t);if(!0!==s)return m(s.err.code,s.err.msg,_(e,i+s.err.line));r=!0}else if(u){if(!g.tagClosed)return m("InvalidTag","Closing tag '"+p+"' doesn't have proper closing.",_(e,n));if(v.trim().length>0)return m("InvalidTag","Closing tag '"+p+"' can't have attributes or invalid starting.",_(e,c));if(0===i.length)return m("InvalidTag","Closing tag '"+p+"' has not been opened.",_(e,c));{const t=i.pop();if(p!==t.tagName){let i=_(e,t.tagStartPos);return m("InvalidTag","Expected closing tag '"+t.tagName+"' (opened in line "+i.line+", col "+i.col+") instead of closing tag '"+p+"'.",_(e,c))}0==i.length&&(l=!0)}}else{const s=h(v,t);if(!0!==s)return m(s.err.code,s.err.msg,_(e,n-v.length+s.err.line));if(!0===l)return m("InvalidXml","Multiple possible root nodes found.",_(e,n));-1!==t.unpairedTags.indexOf(p)||i.push({tagName:p,tagStartPos:c}),r=!0}for(n++;n<e.length;n++)if("<"===e[n]){if("!"===e[n+1]){n++,n=a(e,n);continue}if("?"!==e[n+1])break;if(n=o(e,++n),n.err)return n}else if("&"===e[n]){const t=f(e,n);if(-1==t)return m("InvalidChar","char '&' is not expected.",_(e,n));n=t}else if(!0===l&&!s(e[n]))return m("InvalidXml","Extra text at the end",_(e,n));"<"===e[n]&&n--}}}return r?1==i.length?m("InvalidTag","Unclosed tag '"+i[0].tagName+"'.",_(e,i[0].tagStartPos)):!(i.length>0)||m("InvalidXml","Invalid '"+JSON.stringify(i.map(e=>e.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):m("InvalidXml","Start tag expected.",1)};const l='"',c="'";function d(e,t){let i="",r="",n=!1;for(;t<e.length;t++){if(e[t]===l||e[t]===c)""===r?r=e[t]:r!==e[t]||(r="");else if(">"===e[t]&&""===r){n=!0;break}i+=e[t]}return""===r&&{value:i,index:t,tagClosed:n}}const u=new RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function h(e,t){const i=r.getAllMatches(e,u),n={};for(let r=0;r<i.length;r++){if(0===i[r][1].length)return m("InvalidAttr","Attribute '"+i[r][2]+"' has no space in starting.",v(i[r]));if(void 0!==i[r][3]&&void 0===i[r][4])return m("InvalidAttr","Attribute '"+i[r][2]+"' is without value.",v(i[r]));if(void 0===i[r][3]&&!t.allowBooleanAttributes)return m("InvalidAttr","boolean attribute '"+i[r][2]+"' is not allowed.",v(i[r]));const e=i[r][2];if(!g(e))return m("InvalidAttr","Attribute '"+e+"' is an invalid name.",v(i[r]));if(n.hasOwnProperty(e))return m("InvalidAttr","Attribute '"+e+"' is repeated.",v(i[r]));n[e]=1}return!0}function p(e,t){let i=/\d/;for("x"===e[t]&&(t++,i=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(i))break}return-1}function f(e,t){if(t++,";"===e[t])return-1;if("#"===e[t])return t++,p(e,t);let i=0;for(;t<e.length;t++,i++)if(!(e[t].match(/\w/)&&i<20)){if(";"===e[t])break;return-1}return t}function m(e,t,i){return{err:{code:e,msg:t,line:i.line||i,col:i.col}}}function g(e){return r.isName(e)}function y(e){return r.isName(e)}function _(e,t){const i=e.substring(0,t).split(/\r?\n/);return{line:i.length,col:i[i.length-1].length+1}}function v(e){return e.startIndex+e[1].length}},"./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlbuilder/json2xml.js ***!
  \*****************************************************************/
/*! no static exports found */function(e,t,i){"use strict";const r=i(/*! ./orderedJs2Xml */"./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js"),n=i(/*! ../ignoreAttributes */"./node_modules/fast-xml-parser/src/ignoreAttributes.js"),s={attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,cdataPropName:!1,format:!1,indentBy:"  ",suppressEmptyNode:!1,suppressUnpairedNode:!0,suppressBooleanAttributes:!0,tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},preserveOrder:!1,commentPropName:!1,unpairedTags:[],entities:[{regex:new RegExp("&","g"),val:"&amp;"},{regex:new RegExp(">","g"),val:"&gt;"},{regex:new RegExp("<","g"),val:"&lt;"},{regex:new RegExp("'","g"),val:"&apos;"},{regex:new RegExp('"',"g"),val:"&quot;"}],processEntities:!0,stopNodes:[],oneListGroup:!1};function o(e){this.options=Object.assign({},s,e),!0===this.options.ignoreAttributes||this.options.attributesGroupName?this.isAttribute=function(){return!1}:(this.ignoreAttributesFn=n(this.options.ignoreAttributes),this.attrPrefixLen=this.options.attributeNamePrefix.length,this.isAttribute=c),this.processTextOrObjNode=a,this.options.format?(this.indentate=l,this.tagEndChar=">\n",this.newLine="\n"):(this.indentate=function(){return""},this.tagEndChar=">",this.newLine="")}function a(e,t,i,r){const n=this.j2x(e,i+1,r.concat(t));return void 0!==e[this.options.textNodeName]&&1===Object.keys(e).length?this.buildTextValNode(e[this.options.textNodeName],t,n.attrStr,i):this.buildObjectNode(n.val,t,n.attrStr,i)}function l(e){return this.options.indentBy.repeat(e)}function c(e){return!(!e.startsWith(this.options.attributeNamePrefix)||e===this.options.textNodeName)&&e.substr(this.attrPrefixLen)}o.prototype.build=function(e){return this.options.preserveOrder?r(e,this.options):(Array.isArray(e)&&this.options.arrayNodeName&&this.options.arrayNodeName.length>1&&(e={[this.options.arrayNodeName]:e}),this.j2x(e,0,[]).val)},o.prototype.j2x=function(e,t,i){let r="",n="";const s=i.join(".");for(let o in e)if(Object.prototype.hasOwnProperty.call(e,o))if("undefined"===typeof e[o])this.isAttribute(o)&&(n+="");else if(null===e[o])this.isAttribute(o)?n+="":"?"===o[0]?n+=this.indentate(t)+"<"+o+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+o+"/"+this.tagEndChar;else if(e[o]instanceof Date)n+=this.buildTextValNode(e[o],o,"",t);else if("object"!==typeof e[o]){const i=this.isAttribute(o);if(i&&!this.ignoreAttributesFn(i,s))r+=this.buildAttrPairStr(i,""+e[o]);else if(!i)if(o===this.options.textNodeName){let t=this.options.tagValueProcessor(o,""+e[o]);n+=this.replaceEntitiesValue(t)}else n+=this.buildTextValNode(e[o],o,"",t)}else if(Array.isArray(e[o])){const r=e[o].length;let s="",a="";for(let l=0;l<r;l++){const r=e[o][l];if("undefined"===typeof r);else if(null===r)"?"===o[0]?n+=this.indentate(t)+"<"+o+"?"+this.tagEndChar:n+=this.indentate(t)+"<"+o+"/"+this.tagEndChar;else if("object"===typeof r)if(this.options.oneListGroup){const e=this.j2x(r,t+1,i.concat(o));s+=e.val,this.options.attributesGroupName&&r.hasOwnProperty(this.options.attributesGroupName)&&(a+=e.attrStr)}else s+=this.processTextOrObjNode(r,o,t,i);else if(this.options.oneListGroup){let e=this.options.tagValueProcessor(o,r);e=this.replaceEntitiesValue(e),s+=e}else s+=this.buildTextValNode(r,o,"",t)}this.options.oneListGroup&&(s=this.buildObjectNode(s,o,a,t)),n+=s}else if(this.options.attributesGroupName&&o===this.options.attributesGroupName){const t=Object.keys(e[o]),i=t.length;for(let n=0;n<i;n++)r+=this.buildAttrPairStr(t[n],""+e[o][t[n]])}else n+=this.processTextOrObjNode(e[o],o,t,i);return{attrStr:r,val:n}},o.prototype.buildAttrPairStr=function(e,t){return t=this.options.attributeValueProcessor(e,""+t),t=this.replaceEntitiesValue(t),this.options.suppressBooleanAttributes&&"true"===t?" "+e:" "+e+'="'+t+'"'},o.prototype.buildObjectNode=function(e,t,i,r){if(""===e)return"?"===t[0]?this.indentate(r)+"<"+t+i+"?"+this.tagEndChar:this.indentate(r)+"<"+t+i+this.closeTag(t)+this.tagEndChar;{let n="</"+t+this.tagEndChar,s="";return"?"===t[0]&&(s="?",n=""),!i&&""!==i||-1!==e.indexOf("<")?!1!==this.options.commentPropName&&t===this.options.commentPropName&&0===s.length?this.indentate(r)+`\x3c!--${e}--\x3e`+this.newLine:this.indentate(r)+"<"+t+i+s+this.tagEndChar+e+this.indentate(r)+n:this.indentate(r)+"<"+t+i+s+">"+e+n}},o.prototype.closeTag=function(e){let t="";return-1!==this.options.unpairedTags.indexOf(e)?this.options.suppressUnpairedNode||(t="/"):t=this.options.suppressEmptyNode?"/":"></"+e,t},o.prototype.buildTextValNode=function(e,t,i,r){if(!1!==this.options.cdataPropName&&t===this.options.cdataPropName)return this.indentate(r)+`<![CDATA[${e}]]>`+this.newLine;if(!1!==this.options.commentPropName&&t===this.options.commentPropName)return this.indentate(r)+`\x3c!--${e}--\x3e`+this.newLine;if("?"===t[0])return this.indentate(r)+"<"+t+i+"?"+this.tagEndChar;{let n=this.options.tagValueProcessor(t,e);return n=this.replaceEntitiesValue(n),""===n?this.indentate(r)+"<"+t+i+this.closeTag(t)+this.tagEndChar:this.indentate(r)+"<"+t+i+">"+n+"</"+t+this.tagEndChar}},o.prototype.replaceEntitiesValue=function(e){if(e&&e.length>0&&this.options.processEntities)for(let t=0;t<this.options.entities.length;t++){const i=this.options.entities[t];e=e.replace(i.regex,i.val)}return e},e.exports=o},"./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js":
/*!**********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlbuilder/orderedJs2Xml.js ***!
  \**********************************************************************/
/*! no static exports found */function(e,t){const i="\n";function r(e,t){let r="";return t.format&&t.indentBy.length>0&&(r=i),n(e,t,"",r)}function n(e,t,i,r){let c="",d=!1;for(let u=0;u<e.length;u++){const h=e[u],p=s(h);if(void 0===p)continue;let f="";if(f=0===i.length?p:`${i}.${p}`,p===t.textNodeName){let e=h[p];a(f,t)||(e=t.tagValueProcessor(p,e),e=l(e,t)),d&&(c+=r),c+=e,d=!1;continue}if(p===t.cdataPropName){d&&(c+=r),c+=`<![CDATA[${h[p][0][t.textNodeName]}]]>`,d=!1;continue}if(p===t.commentPropName){c+=r+`\x3c!--${h[p][0][t.textNodeName]}--\x3e`,d=!0;continue}if("?"===p[0]){const e=o(h[":@"],t),i="?xml"===p?"":r;let n=h[p][0][t.textNodeName];n=0!==n.length?" "+n:"",c+=i+`<${p}${n}${e}?>`,d=!0;continue}let m=r;""!==m&&(m+=t.indentBy);const g=o(h[":@"],t),y=r+`<${p}${g}`,_=n(h[p],t,f,m);-1!==t.unpairedTags.indexOf(p)?t.suppressUnpairedNode?c+=y+">":c+=y+"/>":_&&0!==_.length||!t.suppressEmptyNode?_&&_.endsWith(">")?c+=y+`>${_}${r}</${p}>`:(c+=y+">",_&&""!==r&&(_.includes("/>")||_.includes("</"))?c+=r+t.indentBy+_+r:c+=_,c+=`</${p}>`):c+=y+"/>",d=!0}return c}function s(e){const t=Object.keys(e);for(let i=0;i<t.length;i++){const r=t[i];if(e.hasOwnProperty(r)&&":@"!==r)return r}}function o(e,t){let i="";if(e&&!t.ignoreAttributes)for(let r in e){if(!e.hasOwnProperty(r))continue;let n=t.attributeValueProcessor(r,e[r]);n=l(n,t),!0===n&&t.suppressBooleanAttributes?i+=" "+r.substr(t.attributeNamePrefix.length):i+=` ${r.substr(t.attributeNamePrefix.length)}="${n}"`}return i}function a(e,t){e=e.substr(0,e.length-t.textNodeName.length-1);let i=e.substr(e.lastIndexOf(".")+1);for(let r in t.stopNodes)if(t.stopNodes[r]===e||t.stopNodes[r]==="*."+i)return!0;return!1}function l(e,t){if(e&&e.length>0&&t.processEntities)for(let i=0;i<t.entities.length;i++){const r=t.entities[i];e=e.replace(r.regex,r.val)}return e}e.exports=r},"./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js":
/*!*********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js ***!
  \*********************************************************************/
/*! no static exports found */function(e,t,i){const r=i(/*! ../util */"./node_modules/fast-xml-parser/src/util.js");function n(e,t){const i={};if("O"!==e[t+3]||"C"!==e[t+4]||"T"!==e[t+5]||"Y"!==e[t+6]||"P"!==e[t+7]||"E"!==e[t+8])throw new Error("Invalid Tag instead of DOCTYPE");{t+=9;let r=1,n=!1,h=!1,p="";for(;t<e.length;t++)if("<"!==e[t]||h)if(">"===e[t]){if(h?"-"===e[t-1]&&"-"===e[t-2]&&(h=!1,r--):r--,0===r)break}else"["===e[t]?n=!0:p+=e[t];else{if(n&&a(e,t))t+=7,[entityName,val,t]=s(e,t+1),-1===val.indexOf("&")&&(i[u(entityName)]={regx:RegExp(`&${entityName};`,"g"),val:val});else if(n&&l(e,t))t+=8;else if(n&&c(e,t))t+=8;else if(n&&d(e,t))t+=9;else{if(!o)throw new Error("Invalid DOCTYPE");h=!0}r++,p=""}if(0!==r)throw new Error("Unclosed DOCTYPE")}return{entities:i,i:t}}function s(e,t){let i="";for(;t<e.length&&"'"!==e[t]&&'"'!==e[t];t++)i+=e[t];if(i=i.trim(),-1!==i.indexOf(" "))throw new Error("External entites are not supported");const r=e[t++];let n="";for(;t<e.length&&e[t]!==r;t++)n+=e[t];return[i,n,t]}function o(e,t){return"!"===e[t+1]&&"-"===e[t+2]&&"-"===e[t+3]}function a(e,t){return"!"===e[t+1]&&"E"===e[t+2]&&"N"===e[t+3]&&"T"===e[t+4]&&"I"===e[t+5]&&"T"===e[t+6]&&"Y"===e[t+7]}function l(e,t){return"!"===e[t+1]&&"E"===e[t+2]&&"L"===e[t+3]&&"E"===e[t+4]&&"M"===e[t+5]&&"E"===e[t+6]&&"N"===e[t+7]&&"T"===e[t+8]}function c(e,t){return"!"===e[t+1]&&"A"===e[t+2]&&"T"===e[t+3]&&"T"===e[t+4]&&"L"===e[t+5]&&"I"===e[t+6]&&"S"===e[t+7]&&"T"===e[t+8]}function d(e,t){return"!"===e[t+1]&&"N"===e[t+2]&&"O"===e[t+3]&&"T"===e[t+4]&&"A"===e[t+5]&&"T"===e[t+6]&&"I"===e[t+7]&&"O"===e[t+8]&&"N"===e[t+9]}function u(e){if(r.isName(e))return e;throw new Error("Invalid entity name "+e)}e.exports=n},"./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js":
/*!**********************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js ***!
  \**********************************************************************/
/*! no static exports found */function(e,t){const i={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,i){return e}},r=function(e){return Object.assign({},i,e)};t.buildOptions=r,t.defaultOptions=i},"./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js":
/*!************************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js ***!
  \************************************************************************/
/*! no static exports found */function(e,t,i){"use strict";const r=i(/*! ../util */"./node_modules/fast-xml-parser/src/util.js"),n=i(/*! ./xmlNode */"./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js"),s=i(/*! ./DocTypeReader */"./node_modules/fast-xml-parser/src/xmlparser/DocTypeReader.js"),o=i(/*! strnum */"./node_modules/strnum/strnum.js"),a=i(/*! ../ignoreAttributes */"./node_modules/fast-xml-parser/src/ignoreAttributes.js");class l{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"¢"},pound:{regex:/&(pound|#163);/g,val:"£"},yen:{regex:/&(yen|#165);/g,val:"¥"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"©"},reg:{regex:/&(reg|#174);/g,val:"®"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(e,t)=>String.fromCharCode(Number.parseInt(t,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(e,t)=>String.fromCharCode(Number.parseInt(t,16))}},this.addExternalEntities=c,this.parseXml=f,this.parseTextData=d,this.resolveNameSpace=u,this.buildAttributesMap=p,this.isItStopNode=_,this.replaceEntitiesValue=g,this.readStopNodeData=x,this.saveTextToParentTag=y,this.addChild=m,this.ignoreAttributesFn=a(this.options.ignoreAttributes)}}function c(e){const t=Object.keys(e);for(let i=0;i<t.length;i++){const r=t[i];this.lastEntities[r]={regex:new RegExp("&"+r+";","g"),val:e[r]}}}function d(e,t,i,r,n,s,o){if(void 0!==e&&(this.options.trimValues&&!r&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));const r=this.options.tagValueProcessor(t,e,i,n,s);if(null===r||void 0===r)return e;if(typeof r!==typeof e||r!==e)return r;if(this.options.trimValues)return k(e,this.options.parseTagValue,this.options.numberParseOptions);{const t=e.trim();return t===e?k(e,this.options.parseTagValue,this.options.numberParseOptions):e}}}function u(e){if(this.options.removeNSPrefix){const t=e.split(":"),i="/"===e.charAt(0)?"/":"";if("xmlns"===t[0])return"";2===t.length&&(e=i+t[1])}return e}const h=new RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function p(e,t,i){if(!0!==this.options.ignoreAttributes&&"string"===typeof e){const i=r.getAllMatches(e,h),n=i.length,s={};for(let e=0;e<n;e++){const r=this.resolveNameSpace(i[e][1]);if(this.ignoreAttributesFn(r,t))continue;let n=i[e][4],o=this.options.attributeNamePrefix+r;if(r.length)if(this.options.transformAttributeName&&(o=this.options.transformAttributeName(o)),"__proto__"===o&&(o="#__proto__"),void 0!==n){this.options.trimValues&&(n=n.trim()),n=this.replaceEntitiesValue(n);const e=this.options.attributeValueProcessor(r,n,t);s[o]=null===e||void 0===e?n:typeof e!==typeof n||e!==n?e:k(n,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(s[o]=!0)}if(!Object.keys(s).length)return;if(this.options.attributesGroupName){const e={};return e[this.options.attributesGroupName]=s,e}return s}}const f=function(e){const t=new n("!xml");let i=t,r="",o="";for(let a=0;a<e.length;a++){const l=e[a];if("<"===l)if("/"===e[a+1]){const t=b(e,">",a,"Closing Tag is not closed.");let n=e.substring(a+2,t).trim();if(this.options.removeNSPrefix){const e=n.indexOf(":");-1!==e&&(n=n.substr(e+1))}this.options.transformTagName&&(n=this.options.transformTagName(n)),i&&(r=this.saveTextToParentTag(r,i,o));const s=o.substring(o.lastIndexOf(".")+1);if(n&&-1!==this.options.unpairedTags.indexOf(n))throw new Error(`Unpaired tag can not be used as closing tag: </${n}>`);let l=0;s&&-1!==this.options.unpairedTags.indexOf(s)?(l=o.lastIndexOf(".",o.lastIndexOf(".")-1),this.tagsNodeStack.pop()):l=o.lastIndexOf("."),o=o.substring(0,l),i=this.tagsNodeStack.pop(),r="",a=t}else if("?"===e[a+1]){let t=S(e,a,!1,"?>");if(!t)throw new Error("Pi Tag is not closed.");if(r=this.saveTextToParentTag(r,i,o),this.options.ignoreDeclaration&&"?xml"===t.tagName||this.options.ignorePiTags);else{const e=new n(t.tagName);e.add(this.options.textNodeName,""),t.tagName!==t.tagExp&&t.attrExpPresent&&(e[":@"]=this.buildAttributesMap(t.tagExp,o,t.tagName)),this.addChild(i,e,o)}a=t.closeIndex+1}else if("!--"===e.substr(a+1,3)){const t=b(e,"--\x3e",a+4,"Comment is not closed.");if(this.options.commentPropName){const n=e.substring(a+4,t-2);r=this.saveTextToParentTag(r,i,o),i.add(this.options.commentPropName,[{[this.options.textNodeName]:n}])}a=t}else if("!D"===e.substr(a+1,2)){const t=s(e,a);this.docTypeEntities=t.entities,a=t.i}else if("!["===e.substr(a+1,2)){const t=b(e,"]]>",a,"CDATA is not closed.")-2,n=e.substring(a+9,t);r=this.saveTextToParentTag(r,i,o);let s=this.parseTextData(n,i.tagname,o,!0,!1,!0,!0);void 0==s&&(s=""),this.options.cdataPropName?i.add(this.options.cdataPropName,[{[this.options.textNodeName]:n}]):i.add(this.options.textNodeName,s),a=t+2}else{let s=S(e,a,this.options.removeNSPrefix),l=s.tagName;const c=s.rawTagName;let d=s.tagExp,u=s.attrExpPresent,h=s.closeIndex;this.options.transformTagName&&(l=this.options.transformTagName(l)),i&&r&&"!xml"!==i.tagname&&(r=this.saveTextToParentTag(r,i,o,!1));const p=i;if(p&&-1!==this.options.unpairedTags.indexOf(p.tagname)&&(i=this.tagsNodeStack.pop(),o=o.substring(0,o.lastIndexOf("."))),l!==t.tagname&&(o+=o?"."+l:l),this.isItStopNode(this.options.stopNodes,o,l)){let t="";if(d.length>0&&d.lastIndexOf("/")===d.length-1)"/"===l[l.length-1]?(l=l.substr(0,l.length-1),o=o.substr(0,o.length-1),d=l):d=d.substr(0,d.length-1),a=s.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(l))a=s.closeIndex;else{const i=this.readStopNodeData(e,c,h+1);if(!i)throw new Error("Unexpected end of "+c);a=i.i,t=i.tagContent}const r=new n(l);l!==d&&u&&(r[":@"]=this.buildAttributesMap(d,o,l)),t&&(t=this.parseTextData(t,l,o,!0,u,!0,!0)),o=o.substr(0,o.lastIndexOf(".")),r.add(this.options.textNodeName,t),this.addChild(i,r,o)}else{if(d.length>0&&d.lastIndexOf("/")===d.length-1){"/"===l[l.length-1]?(l=l.substr(0,l.length-1),o=o.substr(0,o.length-1),d=l):d=d.substr(0,d.length-1),this.options.transformTagName&&(l=this.options.transformTagName(l));const e=new n(l);l!==d&&u&&(e[":@"]=this.buildAttributesMap(d,o,l)),this.addChild(i,e,o),o=o.substr(0,o.lastIndexOf("."))}else{const e=new n(l);this.tagsNodeStack.push(i),l!==d&&u&&(e[":@"]=this.buildAttributesMap(d,o,l)),this.addChild(i,e,o),i=e}r="",a=h}}else r+=e[a]}return t.child};function m(e,t,i){const r=this.options.updateTag(t.tagname,i,t[":@"]);!1===r||("string"===typeof r?(t.tagname=r,e.addChild(t)):e.addChild(t))}const g=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){const i=this.docTypeEntities[t];e=e.replace(i.regx,i.val)}for(let t in this.lastEntities){const i=this.lastEntities[t];e=e.replace(i.regex,i.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){const i=this.htmlEntities[t];e=e.replace(i.regex,i.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function y(e,t,i,r){return e&&(void 0===r&&(r=0===Object.keys(t.child).length),e=this.parseTextData(e,t.tagname,i,!1,!!t[":@"]&&0!==Object.keys(t[":@"]).length,r),void 0!==e&&""!==e&&t.add(this.options.textNodeName,e),e=""),e}function _(e,t,i){const r="*."+i;for(const n in e){const i=e[n];if(r===i||t===i)return!0}return!1}function v(e,t,i=">"){let r,n="";for(let s=t;s<e.length;s++){let t=e[s];if(r)t===r&&(r="");else if('"'===t||"'"===t)r=t;else if(t===i[0]){if(!i[1])return{data:n,index:s};if(e[s+1]===i[1])return{data:n,index:s}}else"\t"===t&&(t=" ");n+=t}}function b(e,t,i,r){const n=e.indexOf(t,i);if(-1===n)throw new Error(r);return n+t.length-1}function S(e,t,i,r=">"){const n=v(e,t+1,r);if(!n)return;let s=n.data;const o=n.index,a=s.search(/\s/);let l=s,c=!0;-1!==a&&(l=s.substring(0,a),s=s.substring(a+1).trimStart());const d=l;if(i){const e=l.indexOf(":");-1!==e&&(l=l.substr(e+1),c=l!==n.data.substr(e+1))}return{tagName:l,tagExp:s,closeIndex:o,attrExpPresent:c,rawTagName:d}}function x(e,t,i){const r=i;let n=1;for(;i<e.length;i++)if("<"===e[i])if("/"===e[i+1]){const s=b(e,">",i,t+" is not closed");let o=e.substring(i+2,s).trim();if(o===t&&(n--,0===n))return{tagContent:e.substring(r,i),i:s};i=s}else if("?"===e[i+1]){const t=b(e,"?>",i+1,"StopNode is not closed.");i=t}else if("!--"===e.substr(i+1,3)){const t=b(e,"--\x3e",i+3,"StopNode is not closed.");i=t}else if("!["===e.substr(i+1,2)){const t=b(e,"]]>",i,"StopNode is not closed.")-2;i=t}else{const r=S(e,i,">");if(r){const e=r&&r.tagName;e===t&&"/"!==r.tagExp[r.tagExp.length-1]&&n++,i=r.closeIndex}}}function k(e,t,i){if(t&&"string"===typeof e){const t=e.trim();return"true"===t||"false"!==t&&o(e,i)}return r.isExist(e)?e:""}e.exports=l},"./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/XMLParser.js ***!
  \*****************************************************************/
/*! no static exports found */function(e,t,i){const{buildOptions:r}=i(/*! ./OptionsBuilder */"./node_modules/fast-xml-parser/src/xmlparser/OptionsBuilder.js"),n=i(/*! ./OrderedObjParser */"./node_modules/fast-xml-parser/src/xmlparser/OrderedObjParser.js"),{prettify:s}=i(/*! ./node2json */"./node_modules/fast-xml-parser/src/xmlparser/node2json.js"),o=i(/*! ../validator */"./node_modules/fast-xml-parser/src/validator.js");class a{constructor(e){this.externalEntities={},this.options=r(e)}parse(e,t){if("string"===typeof e);else{if(!e.toString)throw new Error("XML data is accepted in String or Bytes[] form.");e=e.toString()}if(t){!0===t&&(t={});const i=o.validate(e,t);if(!0!==i)throw Error(`${i.err.msg}:${i.err.line}:${i.err.col}`)}const i=new n(this.options);i.addExternalEntities(this.externalEntities);const r=i.parseXml(e);return this.options.preserveOrder||void 0===r?r:s(r,this.options)}addEntity(e,t){if(-1!==t.indexOf("&"))throw new Error("Entity value can't have '&'");if(-1!==e.indexOf("&")||-1!==e.indexOf(";"))throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===t)throw new Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}}e.exports=a},"./node_modules/fast-xml-parser/src/xmlparser/node2json.js":
/*!*****************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/node2json.js ***!
  \*****************************************************************/
/*! no static exports found */function(e,t,i){"use strict";function r(e,t){return n(e,t)}function n(e,t,i){let r;const l={};for(let c=0;c<e.length;c++){const d=e[c],u=s(d);let h="";if(h=void 0===i?u:i+"."+u,u===t.textNodeName)void 0===r?r=d[u]:r+=""+d[u];else{if(void 0===u)continue;if(d[u]){let e=n(d[u],t,h);const i=a(e,t);d[":@"]?o(e,d[":@"],h,t):1!==Object.keys(e).length||void 0===e[t.textNodeName]||t.alwaysCreateTextNode?0===Object.keys(e).length&&(t.alwaysCreateTextNode?e[t.textNodeName]="":e=""):e=e[t.textNodeName],void 0!==l[u]&&l.hasOwnProperty(u)?(Array.isArray(l[u])||(l[u]=[l[u]]),l[u].push(e)):t.isArray(u,h,i)?l[u]=[e]:l[u]=e}}}return"string"===typeof r?r.length>0&&(l[t.textNodeName]=r):void 0!==r&&(l[t.textNodeName]=r),l}function s(e){const t=Object.keys(e);for(let i=0;i<t.length;i++){const e=t[i];if(":@"!==e)return e}}function o(e,t,i,r){if(t){const n=Object.keys(t),s=n.length;for(let o=0;o<s;o++){const s=n[o];r.isArray(s,i+"."+s,!0,!0)?e[s]=[t[s]]:e[s]=t[s]}}}function a(e,t){const{textNodeName:i}=t,r=Object.keys(e).length;return 0===r||!(1!==r||!e[i]&&"boolean"!==typeof e[i]&&0!==e[i])}t.prettify=r},"./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js":
/*!***************************************************************!*\
  !*** ./node_modules/fast-xml-parser/src/xmlparser/xmlNode.js ***!
  \***************************************************************/
/*! no static exports found */function(e,t,i){"use strict";class r{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){"__proto__"===e&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e){"__proto__"===e.tagname&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,[":@"]:e[":@"]}):this.child.push({[e.tagname]:e.child})}}e.exports=r},"./node_modules/process/browser.js":
/*!*****************************************!*\
  !*** ./node_modules/process/browser.js ***!
  \*****************************************/
/*! no static exports found */function(e,t){var i,r,n=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function a(e){if(i===setTimeout)return setTimeout(e,0);if((i===s||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(t){try{return i.call(null,e,0)}catch(t){return i.call(this,e,0)}}}function l(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{return r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(function(){try{i="function"===typeof setTimeout?setTimeout:s}catch(e){i=s}try{r="function"===typeof clearTimeout?clearTimeout:o}catch(e){r=o}})();var c,d=[],u=!1,h=-1;function p(){u&&c&&(u=!1,c.length?d=c.concat(d):h=-1,d.length&&f())}function f(){if(!u){var e=a(p);u=!0;var t=d.length;while(t){c=d,d=[];while(++h<t)c&&c[h].run();h=-1,t=d.length}c=null,u=!1,l(e)}}function m(e,t){this.fun=e,this.array=t}function g(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)t[i-1]=arguments[i];d.push(new m(e,t)),1!==d.length||u||a(f)},m.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=g,n.addListener=g,n.once=g,n.off=g,n.removeListener=g,n.removeAllListeners=g,n.emit=g,n.prependListener=g,n.prependOnceListener=g,n.listeners=function(e){return[]},n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},"./node_modules/strnum/strnum.js":
/*!***************************************!*\
  !*** ./node_modules/strnum/strnum.js ***!
  \***************************************/
/*! no static exports found */function(e,t){const i=/^[-+]?0x[a-fA-F0-9]+$/,r=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,n={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0};function s(e,t={}){if(t=Object.assign({},n,t),!e||"string"!==typeof e)return e;let s=e.trim();if(void 0!==t.skipLike&&t.skipLike.test(s))return e;if("0"===e)return 0;if(t.hex&&i.test(s))return a(s,16);if(-1!==s.search(/[eE]/)){const i=s.match(/^([-\+])?(0*)([0-9]*(\.[0-9]*)?[eE][-\+]?[0-9]+)$/);if(i){if(t.leadingZeros)s=(i[1]||"")+i[3];else if("0"!==i[2]||"."!==i[3][0])return e;return t.eNotation?Number(s):e}return e}{const i=r.exec(s);if(i){const r=i[1],n=i[2];let a=o(i[3]);if(!t.leadingZeros&&n.length>0&&r&&"."!==s[2])return e;if(!t.leadingZeros&&n.length>0&&!r&&"."!==s[1])return e;if(t.leadingZeros&&n===e)return 0;{const i=Number(s),o=""+i;return-1!==o.search(/[eE]/)?t.eNotation?i:e:-1!==s.indexOf(".")?"0"===o&&""===a||o===a||r&&o==="-"+a?i:e:n?a===o||r+a===o?i:e:s===o||s===r+o?i:e}}return e}}function o(e){return e&&-1!==e.indexOf(".")?(e=e.replace(/0+$/,""),"."===e?e="0":"."===e[0]?e="0"+e:"."===e[e.length-1]&&(e=e.substr(0,e.length-1)),e):e}function a(e,t){if(parseInt)return parseInt(e,t);if(Number.parseInt)return Number.parseInt(e,t);if(window&&window.parseInt)return window.parseInt(e,t);throw new Error("parseInt, Number.parseInt, window.parseInt are not supported")}e.exports=s},"./node_modules/webpack/buildin/amd-options.js":
/*!****************************************!*\
  !*** (webpack)/buildin/amd-options.js ***!
  \****************************************/
/*! no static exports found */function(e,t){(function(t){e.exports=t}).call(this,{})},"./node_modules/webpack/buildin/module.js":
/*!***********************************!*\
  !*** (webpack)/buildin/module.js ***!
  \***********************************/
/*! no static exports found */function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},"./package.json":
/*!**********************!*\
  !*** ./package.json ***!
  \**********************/
/*! exports provided: name, version, description, main, types, scripts, repository, keywords, author, license, bugs, homepage, dependencies, devDependencies, default */function(e){e.exports=JSON.parse('{"name":"cos-js-sdk-v5","version":"1.10.1","description":"JavaScript SDK for [腾讯云对象存储](https://cloud.tencent.com/product/cos)","main":"dist/cos-js-sdk-v5.js","types":"index.d.ts","scripts":{"prettier":"prettier --write src demo/demo.js demo/CIDemos/*.js test/test.js server/sts.js lib/request.js index.d.ts","server":"node server/sts.js","dev":"cross-env NODE_ENV=development webpack -w --mode=development","build":"cross-env NODE_ENV=production webpack --mode=production","cos-auth.min.js":"uglifyjs ./demo/common/cos-auth.js -o ./demo/common/cos-auth.min.js -c -m","test":"jest --runInBand --coverage","postinstall":"node scripts/patch-check.js"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/cos-js-sdk-v5.git"},"keywords":[],"author":"carsonxu","license":"ISC","bugs":{"url":"https://github.com/tencentyun/cos-js-sdk-v5/issues"},"homepage":"https://github.com/tencentyun/cos-js-sdk-v5#readme","dependencies":{"fast-xml-parser":"4.5.0"},"devDependencies":{"@babel/core":"7.17.9","@babel/plugin-transform-runtime":"7.18.10","@babel/preset-env":"7.16.11","babel-loader":"8.2.5","body-parser":"^1.18.3","cross-env":"^5.2.0","express":"^4.16.4","jest":"29.7.0","jest-environment-jsdom":"29.7.0","patch-package":"^8.0.0","prettier":"^3.0.1","qcloud-cos-sts":"^3.0.2","request":"^2.87.0","terser-webpack-plugin":"4.2.3","uglifyjs":"^2.4.11","webpack":"4.46.0","webpack-cli":"4.10.0"}}')},"./src/advance.js":
/*!************************!*\
  !*** ./src/advance.js ***!
  \************************/
/*! no static exports found */function(e,t,i){var r=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js"),n=i(/*! ./session */"./src/session.js"),s=i(/*! ./async */"./src/async.js"),o=i(/*! ./event */"./src/event.js").EventProxy,a=i(/*! ./util */"./src/util.js"),l=i(/*! ./tracker */"./src/tracker.js");function c(e,t){var i,r,s=this,l=new o,c=e.TaskId,u=e.Bucket,h=e.Region,f=e.Key,g=e.Body,y=e.ChunkSize||e.SliceSize||s.options.ChunkSize,_=e.AsyncLimit,v=e.StorageClass,b=e.ServerSideEncryption,S=e.onHashProgress,x=e.tracker;x&&x.setParams({chunkSize:y}),s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 分块上传开始")}),l.on("error",(function(i){if(s._isRunningTask(c))return e.UploadData.UploadId&&n.removeUsing(e.UploadData.UploadId),i.UploadId=e.UploadData.UploadId||"",s.logger.error({cate:"RESULT",tag:"upload",msg:"[key=".concat(e.Key,"] 分块上传失败: ").concat(JSON.stringify(i))}),t(i)})),l.on("upload_complete",(function(i){var r=a.extend({UploadId:e.UploadData.UploadId||""},i);t(null,r)})),l.on("upload_slice_complete",(function(t){var o={};a.each(e.Headers,(function(e,t){var i=t.toLowerCase();(0===i.indexOf("x-cos-meta-")||["pic-operations","x-cos-callback","x-cos-callback-var","x-cos-return-body"].includes(i))&&(o[t]=e)})),s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 开始完成分块请求")}),m.call(s,{Bucket:u,Region:h,Key:f,UploadId:t.UploadId,SliceList:t.SliceList,Headers:o,tracker:x},(function(o,a){if(s._isRunningTask(c)){if(n.removeUsing(t.UploadId),o)return r(null,!0),s.logger.error({cate:"RESULT",tag:"upload",msg:"[key=".concat(e.Key,"] 完成分块请求失败")}),l.emit("error",o);n.removeUploadId.call(s,t.UploadId),r({loaded:i,total:i},!0),s.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(e.Key,"] 完成分块请求成功")}),l.emit("upload_complete",a)}}))})),l.on("get_upload_data_finish",(function(t){var o=n.getFileId(g,e.ChunkSize,u,f);o&&n.saveUploadId.call(s,o,t.UploadId,s.options.UploadIdCacheLimit),n.setUsing(t.UploadId),r(null,!0),s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 开始上传各个分块")}),p.call(s,{TaskId:c,Bucket:u,Region:h,Key:f,Body:g,FileSize:i,SliceSize:y,AsyncLimit:_,ServerSideEncryption:b,UploadData:t,Headers:e.Headers,onProgress:r,tracker:x},(function(t,i){if(s._isRunningTask(c)){if(t)return r(null,!0),s.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 分块上传失败")}),l.emit("error",t);s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 所有分块上传完成")}),l.emit("upload_slice_complete",i)}}))})),l.on("get_file_size_finish",(function(){if(r=a.throttleOnProgress.call(s,i,e.onProgress),e.UploadData.UploadId)s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 已经获取到 uploadId, ").concat(e.UploadData.UploadId)}),l.emit("get_upload_data_finish",e.UploadData);else{var t=a.extend({TaskId:c,Bucket:u,Region:h,Key:f,Headers:e.Headers,StorageClass:v,Body:g,FileSize:i,SliceSize:y,onHashProgress:S,tracker:x},e);s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 去获取 uploadId")}),d.call(s,t,(function(t,i){if(s._isRunningTask(c)){if(t)return l.emit("error",t);e.UploadData.UploadId=i.UploadId,e.UploadData.PartList=i.PartList,s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 获取到 uploadId, ").concat(e.UploadData.UploadId)}),l.emit("get_upload_data_finish",e.UploadData)}}))}})),i=e.ContentLength,delete e.ContentLength,!e.Headers&&(e.Headers={}),a.each(e.Headers,(function(t,i){"content-length"===i.toLowerCase()&&delete e.Headers[i]})),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,n=0;n<t.length;n++)if(r=1024*t[n]*1024,i/r<=s.options.MaxPartNumber)break;e.ChunkSize=e.SliceSize=y=Math.max(y,r)}(),0===i?(e.Body="",e.ContentLength=0,e.SkipTask=!0,s.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 文件大小为 0，执行简单上传")}),s.putObject(e,t)):l.emit("get_file_size_finish")}function d(e,t){var i=e.TaskId,r=e.Bucket,l=e.Region,c=e.Key,d=e.StorageClass,p=this,f={},m=e.FileSize,g=e.SliceSize,y=Math.ceil(m/g),_=0,v=a.throttleOnProgress.call(p,m,e.onHashProgress),b=function(t,i){var r=g*(t-1),n=Math.min(r+g,m),s=n-r;f[t]?i(null,{PartNumber:t,ETag:f[t],Size:s}):a.fileSlice(e.Body,r,n,!1,(function(e){a.getFileMd5(e,(function(e,r){if(e)return i(a.error(e));var n='"'+r+'"';f[t]=n,1,_+=s,v({loaded:_,total:m}),i(null,{PartNumber:t,ETag:n,Size:s})}))}))},S=function(e,t){var i=e.length;if(0===i)return t(null,!0);if(i>y)return t(null,!1);if(i>1){var r=Math.max(e[0].Size,e[1].Size);if(r!==g)return t(null,!1)}var n=function(r){if(r<i){var s=e[r];b(s.PartNumber,(function(e,i){i&&i.ETag===s.ETag&&i.Size===s.Size?n(r+1):t(null,!1)}))}else t(null,!0)};n(0)},x=new o;x.on("error",(function(e){if(p._isRunningTask(i))return t(e)})),x.on("upload_id_available",(function(e){var i={},r=[];a.each(e.PartList,(function(e){i[e.PartNumber]=e}));for(var n=1;n<=y;n++){var s=i[n];s?(s.PartNumber=n,s.Uploaded=!0):s={PartNumber:n,ETag:null,Uploaded:!1},r.push(s)}e.PartList=r,t(null,e)})),x.on("no_available_upload_id",(function(){if(p._isRunningTask(i)){var n=a.extend({Bucket:r,Region:l,Key:c,Query:a.clone(e.Query),StorageClass:d,Body:e.Body,calledBySdk:"sliceUploadFile",tracker:e.tracker},e),s=a.clone(e.Headers);delete s["x-cos-mime-limit"],n.Headers=s,p.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 准备初始化分块上传")}),p.multipartInit(n,(function(r,n){if(p._isRunningTask(i)){if(r)return p.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 初始化分块上传失败, ").concat(JSON.stringify(r))}),x.emit("error",r);var s=n.UploadId;if(!s)return t(a.error(new Error("no such upload id")));p.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 初始化分块上传成功")}),x.emit("upload_id_available",{UploadId:s,PartList:[]})}}))}})),x.on("has_and_check_upload_id",(function(t){t=t.reverse(),s.eachLimit(t,1,(function(t,s){p._isRunningTask(i)&&(n.using[t]?s():h.call(p,{Bucket:r,Region:l,Key:c,UploadId:t,tracker:e.tracker},(function(e,r){if(p._isRunningTask(i)){if(e)return n.removeUsing(t),x.emit("error",e);var o=r.PartList;o.forEach((function(e){e.PartNumber*=1,e.Size*=1,e.ETag=e.ETag||""})),S(o,(function(e,r){if(p._isRunningTask(i))return e?x.emit("error",e):void(r?s({UploadId:t,PartList:o}):s())}))}})))}),(function(e){p._isRunningTask(i)&&(v(null,!0),e&&e.UploadId?x.emit("upload_id_available",e):x.emit("no_available_upload_id"))}))})),x.on("seek_local_avail_upload_id",(function(t){var s=n.getFileId(e.Body,e.ChunkSize,r,c),o=n.getUploadIdList.call(p,s);if(s&&o){var d=function(s){if(s>=o.length)x.emit("has_and_check_upload_id",t);else{var u=o[s];if(!a.isInArray(t,u))return n.removeUploadId.call(p,u),void d(s+1);n.using[u]?d(s+1):h.call(p,{Bucket:r,Region:l,Key:c,UploadId:u,tracker:e.tracker},(function(e,t){p._isRunningTask(i)&&(e?(n.removeUploadId.call(p,u),d(s+1)):x.emit("upload_id_available",{UploadId:u,PartList:t.PartList}))}))}};d(0)}else x.emit("has_and_check_upload_id",t)})),x.on("get_remote_upload_id_list",(function(){u.call(p,{Bucket:r,Region:l,Key:c,tracker:e.tracker},(function(t,s){if(p._isRunningTask(i)){if(t)return x.emit("error",t);var o=a.filter(s.UploadList,(function(e){return e.Key===c&&(!d||e.StorageClass.toUpperCase()===d.toUpperCase())})).reverse().map((function(e){return e.UploadId||e.UploadID}));if(o.length)x.emit("seek_local_avail_upload_id",o);else{var l,u=n.getFileId(e.Body,e.ChunkSize,r,c);u&&(l=n.getUploadIdList.call(p,u))&&a.each(l,(function(e){n.removeUploadId.call(p,e)})),x.emit("no_available_upload_id")}}}))})),x.emit("get_remote_upload_id_list")}function u(e,t){var i=this,r=[],n={Bucket:e.Bucket,Region:e.Region,Prefix:e.Key,calledBySdk:e.calledBySdk||"sliceUploadFile",tracker:e.tracker},s=function(){i.multipartList(n,(function(e,i){if(e)return t(e);r.push.apply(r,i.Upload||[]),"true"===i.IsTruncated?(n.KeyMarker=i.NextKeyMarker,n.UploadIdMarker=i.NextUploadIdMarker,s()):t(null,{UploadList:r})}))};s()}function h(e,t){var i=this,r=[],n={Bucket:e.Bucket,Region:e.Region,Key:e.Key,UploadId:e.UploadId,calledBySdk:"sliceUploadFile",tracker:e.tracker},s=function(){i.multipartListPart(n,(function(e,i){if(e)return t(e);r.push.apply(r,i.Part||[]),"true"===i.IsTruncated?(n.PartNumberMarker=i.NextPartNumberMarker,s()):t(null,{PartList:r})}))};s()}function p(e,t){var i=this,r=e.TaskId,n=e.Bucket,o=e.Region,l=e.Key,c=e.UploadData,d=e.FileSize,u=e.SliceSize,h=Math.min(e.AsyncLimit||i.options.ChunkParallelLimit||1,256),p=e.Body,m=Math.ceil(d/u),g=0,y=e.ServerSideEncryption,_=e.Headers,v=a.filter(c.PartList,(function(e){return e["Uploaded"]&&(g+=e["PartNumber"]>=m&&d%u||u),!e["Uploaded"]})),b=e.onProgress;i.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 开始并发上传各个分块")}),s.eachLimit(v,h,(function(t,s){if(i._isRunningTask(r)){var a=t["PartNumber"],h=Math.min(d,t["PartNumber"]*u)-(t["PartNumber"]-1)*u,m=0;i.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 分块").concat(a,"开始上传")}),f.call(i,{TaskId:r,Bucket:n,Region:o,Key:l,SliceSize:u,FileSize:d,PartNumber:a,ServerSideEncryption:y,Body:p,UploadData:c,Headers:_,onProgress:function(e){g+=e.loaded-m,m=e.loaded,b({loaded:g,total:d})},tracker:e.tracker},(function(n,o){i._isRunningTask(r)&&(n||o.ETag||(n='get ETag error, please add "ETag" to CORS ExposeHeader setting.( 获取ETag失败，请在CORS ExposeHeader设置中添加ETag，请参考文档：https://cloud.tencent.com/document/product/436/13318 )',i.logger.error({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 分块").concat(a,"上传请求成功，但是未获取到 eTag")})),n?(g-=m,i.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(e.Key,"] 分块").concat(a,"上传失败")})):(g+=h-m,t.ETag=o.ETag),i.logger.info({cate:"RESULT",tag:"upload",msg:"[key=".concat(e.Key,"] 分块").concat(a,"上传成功")}),b({loaded:g,total:d}),s(n||null,o))}))}}),(function(e){if(i._isRunningTask(r))return e?t(e):void t(null,{UploadId:c.UploadId,SliceList:c.PartList})}))}function f(e,t){var i=this,r=e.TaskId,n=e.Bucket,o=e.Region,l=e.Key,c=e.FileSize,d=e.Body,u=1*e.PartNumber,h=e.SliceSize,p=e.ServerSideEncryption,f=e.UploadData,m=e.Headers||{},g=i.options.ChunkRetryTimes+1,y=h*(u-1),_=h,v=y+h;v>c&&(v=c,_=v-y);var b=["x-cos-traffic-limit","x-cos-mime-limit"],S={};a.each(m,(function(e,t){b.indexOf(t)>-1&&(S[t]=e)}));var x=f.PartList[u-1];s.retry(g,(function(t){i._isRunningTask(r)&&a.fileSlice(d,y,v,!0,(function(s){i.multipartUpload({TaskId:r,Bucket:n,Region:o,Key:l,ContentLength:_,PartNumber:u,UploadId:f.UploadId,ServerSideEncryption:p,Body:s,Headers:S,onProgress:e.onProgress,calledBySdk:"sliceUploadFile",tracker:e.tracker},(function(e,n){if(i._isRunningTask(r))return e?t(e):(x.Uploaded=!0,t(null,n))}))}))}),(function(e,n){if(i._isRunningTask(r))return t(e,n)}))}function m(e,t){var i=e.Bucket,r=e.Region,n=e.Key,o=e.UploadId,a=e.SliceList,l=this,c=this.options.ChunkRetryTimes+1,d=e.Headers,u=a.map((function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));s.retry(c,(function(t){l.multipartComplete({Bucket:i,Region:r,Key:n,UploadId:o,Parts:u,Headers:d,calledBySdk:"sliceUploadFile",tracker:e.tracker},t)}),(function(e,i){t(e,i)}))}function g(e,t){var i=e.Bucket,r=e.Region,n=e.Key,s=e.UploadId,l=e.Level||"task",c=e.AsyncLimit,d=this,h=new o;if(h.on("error",(function(e){return t(e)})),h.on("get_abort_array",(function(s){y.call(d,{Bucket:i,Region:r,Key:n,Headers:e.Headers,AsyncLimit:c,AbortArray:s},t)})),"bucket"===l)u.call(d,{Bucket:i,Region:r,calledBySdk:"abortUploadTask"},(function(e,i){if(e)return t(e);h.emit("get_abort_array",i.UploadList||[])}));else if("file"===l){if(!n)return t(a.error(new Error("abort_upload_task_no_key")));u.call(d,{Bucket:i,Region:r,Key:n,calledBySdk:"abortUploadTask"},(function(e,i){if(e)return t(e);h.emit("get_abort_array",i.UploadList||[])}))}else{if("task"!==l)return t(a.error(new Error("abort_unknown_level")));if(!s)return t(a.error(new Error("abort_upload_task_no_id")));if(!n)return t(a.error(new Error("abort_upload_task_no_key")));h.emit("get_abort_array",[{Key:n,UploadId:s}])}}function y(e,t){var i=e.Bucket,r=e.Region,n=e.Key,o=e.AbortArray,a=e.AsyncLimit||1,l=this,c=0,d=new Array(o.length);s.eachLimit(o,a,(function(t,s){var o=c;if(n&&n!==t.Key)return d[o]={error:{KeyNotMatch:!0}},void s(null);var a=t.UploadId||t.UploadID;l.multipartAbort({Bucket:i,Region:r,Key:t.Key,Headers:e.Headers,UploadId:a},(function(e){var n={Bucket:i,Region:r,Key:t.Key,UploadId:a};d[o]={error:e,task:n},s(null)})),c++}),(function(e){if(e)return t(e);for(var i=[],r=[],n=0,s=d.length;n<s;n++){var o=d[n];o["task"]&&(o["error"]?r.push(o["task"]):i.push(o["task"]))}return t(null,{successList:i,errorList:r})}))}function _(e,t){var i=this,n=void 0===e.SliceSize?i.options.SliceSize:e.SliceSize,s=[],o=e.Body,c=o.size||o.length||0,d={TaskId:""};if(i.options.EnableReporter){var u=i.options.UseAccelerate||"string"===typeof i.options.Domain&&i.options.Domain.includes("accelerate."),h=c>n?"sliceUploadFile":"putObject";e.tracker=new l({Beacon:i.options.BeaconReporter,clsReporter:i.options.ClsReporter,bucket:e.Bucket,region:e.Region,apiName:"uploadFile",realApi:h,fileKey:e.Key,fileSize:c,accelerate:u,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})}a.each(e,(function(e,t){"object"!==r(e)&&"function"!==typeof e&&(d[t]=e)}));var p=e.onTaskReady,f=function(e){d.TaskId=e,p&&p(e)};e.onTaskReady=f;var m=c>n?"sliceUploadFile":"putObject",g=e.onFileFinish,y=function(i,r){e.tracker&&e.tracker.report(i,r),g&&g(i,r,d),t&&t(i,r)};s.push({api:m,params:e,callback:y}),i._addTasks(s)}function v(e,t){var i=this,n=void 0===e.SliceSize?i.options.SliceSize:e.SliceSize,s=0,o=0,c=a.throttleOnProgress.call(i,o,e.onProgress),d=e.files.length,u=e.onFileFinish,h=Array(d),p=function(e,i,r){c(null,!0),u&&u(e,i,r),h[r.Index]={options:r,error:e,data:i},--d<=0&&t&&t(null,{files:h})},f=[];a.each(e.files,(function(e,t){(function(){var d=e.Body,u=d.size||d.length||0,h={Index:t,TaskId:""};if(!i.options.UseRawKey&&e.Key&&"/"===e.Key.substr(0,1)&&(e.Key=e.Key.substr(1)),s+=u,i.options.EnableReporter){var m=i.options.UseAccelerate||"string"===typeof i.options.Domain&&i.options.Domain.includes("accelerate."),g=u>n?"sliceUploadFile":"putObject";e.tracker=new l({Beacon:i.options.BeaconReporter,clsReporter:i.options.ClsReporter,bucket:e.Bucket,region:e.Region,apiName:"uploadFiles",realApi:g,fileKey:e.Key,fileSize:u,accelerate:m,deepTracker:i.options.DeepTracker,customId:i.options.CustomId,delay:i.options.TrackerDelay})}a.each(e,(function(e,t){"object"!==r(e)&&"function"!==typeof e&&(h[t]=e)}));var y=e.onTaskReady,_=function(e){h.TaskId=e,y&&y(e)};e.onTaskReady=_;var v=0,b=e.onProgress,S=function(e){o=o-v+e.loaded,v=e.loaded,b&&b(e),c({loaded:o,total:s})};e.onProgress=S;var x=u>n?"sliceUploadFile":"putObject",k=e.onFileFinish,C=function(t,i){e.tracker&&e.tracker.report(t,i),k&&k(t,i),p&&p(t,i,h)};f.push({api:x,params:e,callback:C})})()})),i._addTasks(f)}function b(e,t){var i=new o,r=this,l=e.Bucket,c=e.Region,d=e.Key,u=e.CopySource,p=a.getSourceParams.call(this,u);if(p){var f=p.Bucket,m=p.Region,g=decodeURIComponent(p.Key),y=void 0===e.CopySliceSize?r.options.CopySliceSize:e.CopySliceSize;y=Math.max(0,y);var _,v,b=e.CopyChunkSize||this.options.CopyChunkSize,x=this.options.CopyChunkParallelLimit,k=this.options.ChunkRetryTimes+1,C=0,U=0,B={},w={},E={};i.on("copy_slice_complete",(function(i){var o={};a.each(e.Headers,(function(e,t){0===t.toLowerCase().indexOf("x-cos-meta-")&&(o[t]=e)}));var u=a.map(i.PartList,(function(e){return{PartNumber:e.PartNumber,ETag:e.ETag}}));s.retry(k,(function(t){r.multipartComplete({Bucket:l,Region:c,Key:d,UploadId:i.UploadId,Parts:u,tracker:e.tracker,calledBySdk:"sliceCopyFile"},t)}),(function(e,r){if(n.removeUsing(i.UploadId),e)return v(null,!0),t(e);n.removeUploadId(i.UploadId),v({loaded:_,total:_},!0),t(null,r)}))})),i.on("get_copy_data_finish",(function(o){var h=n.getCopyFileId(u,B,b,l,d);h&&n.saveUploadId(h,o.UploadId,r.options.UploadIdCacheLimit),n.setUsing(o.UploadId);var p=a.filter(o.PartList,(function(e){return e["Uploaded"]&&(U+=e["PartNumber"]>=C&&_%b||b),!e["Uploaded"]}));s.eachLimit(p,x,(function(t,i){var n=t.PartNumber,a=t.CopySourceRange,h=t.end-t.start;s.retry(k,(function(t){S.call(r,{Bucket:l,Region:c,Key:d,CopySource:u,UploadId:o.UploadId,PartNumber:n,CopySourceRange:a,tracker:e.tracker,calledBySdk:"sliceCopyFile"},t)}),(function(e,r){if(e)return i(e);U+=h,v({loaded:U,total:_}),t.ETag=r.ETag,i(e||null,r)}))}),(function(e){if(e)return n.removeUsing(o.UploadId),v(null,!0),t(e);i.emit("copy_slice_complete",o)}))})),i.on("get_chunk_size_finish",(function(){var s=function(){r.multipartInit({Bucket:l,Region:c,Key:d,Headers:E,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(r,n){if(r)return t(r);e.UploadId=n.UploadId,i.emit("get_copy_data_finish",{UploadId:e.UploadId,PartList:e.PartList})}))},o=n.getCopyFileId(u,B,b,l,d),p=n.getUploadIdList(o);if(!o||!p)return s();var f=function(t){if(t>=p.length)return s();var o=p[t];if(n.using[o])return f(t+1);h.call(r,{Bucket:l,Region:c,Key:d,UploadId:o,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(r,s){if(r)n.removeUploadId(o),f(t+1);else{if(n.using[o])return f(t+1);var l={},c=0;a.each(s.PartList,(function(e){var t=parseInt(e.Size),i=c+t-1;l[e.PartNumber+"|"+c+"|"+i]=e.ETag,c+=t})),a.each(e.PartList,(function(e){var t=l[e.PartNumber+"|"+e.start+"|"+e.end];t&&(e.ETag=t,e.Uploaded=!0)})),i.emit("get_copy_data_finish",{UploadId:o,PartList:e.PartList})}}))};f(0)})),i.on("get_file_size_finish",(function(){if(function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],i=1048576,n=0;n<t.length;n++)if(i=1024*t[n]*1024,_/i<=r.options.MaxPartNumber)break;e.ChunkSize=b=Math.max(b,i),C=Math.ceil(_/b);for(var s=[],o=1;o<=C;o++){var a=(o-1)*b,l=o*b<_?o*b-1:_-1,c={PartNumber:o,start:a,end:l,CopySourceRange:"bytes="+a+"-"+l};s.push(c)}e.PartList=s}(),E="Replaced"===e.Headers["x-cos-metadata-directive"]?e.Headers:w,E["x-cos-storage-class"]=e.Headers["x-cos-storage-class"]||w["x-cos-storage-class"],E=a.clearKey(E),"ARCHIVE"===w["x-cos-storage-class"]||"DEEP_ARCHIVE"===w["x-cos-storage-class"]){var n=w["x-cos-restore"];if(!n||'ongoing-request="true"'===n)return void t(a.error(new Error("Unrestored archive object is not allowed to be copied")))}delete E["x-cos-copy-source"],delete E["x-cos-metadata-directive"],delete E["x-cos-copy-source-If-Modified-Since"],delete E["x-cos-copy-source-If-Unmodified-Since"],delete E["x-cos-copy-source-If-Match"],delete E["x-cos-copy-source-If-None-Match"],i.emit("get_chunk_size_finish")})),r.headObject({Bucket:f,Region:m,Key:g,tracker:e.tracker,calledBySdk:"sliceCopyFile"},(function(n,s){if(n)n.statusCode&&404===n.statusCode?t(a.error(n,{ErrorStatus:g+" Not Exist"})):t(n);else if(_=e.FileSize=s.headers["content-length"],void 0!==_&&_)if(e.tracker&&e.tracker.setParams({httpSize:_}),v=a.throttleOnProgress.call(r,_,e.onProgress),_<=y)e.Headers["x-cos-metadata-directive"]||(e.Headers["x-cos-metadata-directive"]="Copy"),r.putObjectCopy(Object.assign(e,{calledBySdk:"sliceCopyFile"}),(function(e,i){if(e)return v(null,!0),t(e);v({loaded:_,total:_},!0),t(e,i)}));else{var o=s.headers;B=o,w={"Cache-Control":o["cache-control"],"Content-Disposition":o["content-disposition"],"Content-Encoding":o["content-encoding"],"Content-Type":o["content-type"],Expires:o["expires"],"x-cos-storage-class":o["x-cos-storage-class"]},a.each(o,(function(e,t){var i="x-cos-meta-";0===t.indexOf(i)&&t.length>i.length&&(w[t]=e)})),i.emit("get_file_size_finish")}else t(a.error(new Error('get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.（ 获取Content-Length失败，请在CORS ExposeHeader设置中添加Content-Length，请参考文档：https://cloud.tencent.com/document/product/436/13318 ）')))}))}else t(a.error(new Error("CopySource format error")))}function S(e,t){var i=e.TaskId,r=e.Bucket,n=e.Region,o=e.Key,a=e.CopySource,l=e.UploadId,c=1*e.PartNumber,d=e.CopySourceRange,u=this.options.ChunkRetryTimes+1,h=this;s.retry(u,(function(t){h.uploadPartCopy({TaskId:i,Bucket:r,Region:n,Key:o,CopySource:a,UploadId:l,PartNumber:c,CopySourceRange:d,tracker:e.tracker,calledBySdk:e.calledBySdk},(function(e,i){t(e||null,i)}))}),(function(e,i){return t(e,i)}))}var x={sliceUploadFile:c,abortUploadTask:g,uploadFile:_,uploadFiles:v,sliceCopyFile:b};e.exports.init=function(e,t){t.transferToTaskMethod(x,"sliceUploadFile"),a.each(x,(function(t,i){e.prototype[i]=a.apiWrapper(i,t)}))}},"./src/async.js":
/*!**********************!*\
  !*** ./src/async.js ***!
  \**********************/
/*! no static exports found */function(e,t){var i=function(e,t,i,r){if(r=r||function(){},!e.length||t<=0)return r();var n=0,s=0,o=0;(function a(){if(n>=e.length)return r();while(o<t&&s<e.length)s+=1,o+=1,i(e[s-1],(function(t){t?(r(t),r=function(){}):(n+=1,o-=1,n>=e.length?r():a())}))})()},r=function(e,t,i){var r=function(n){t((function(t,s){t&&n<e?r(n+1):i(t,s)}))};e<1?i():r(1)},n={eachLimit:i,retry:r};e.exports=n},"./src/base.js":
/*!*********************!*\
  !*** ./src/base.js ***!
  \*********************/
/*! no static exports found */function(e,t,i){var r=i(/*! @babel/runtime/helpers/defineProperty */"./node_modules/@babel/runtime/helpers/defineProperty.js"),n=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js");function s(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function o(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?s(Object(i),!0).forEach((function(t){r(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):s(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var a=i(/*! ../lib/request */"./lib/request.js"),l=i(/*! ./util */"./src/util.js");function c(e,t){var i=this.options.Protocol||(l.isBrowser&&"object"===("undefined"===typeof location?"undefined":n(location))&&"http:"===location.protocol?"http:":"https:"),r=this.options.ServiceDomain,s=e.AppId||this.options.appId,o=e.Region;r?(r=r.replace(/\{\{AppId\}\}/gi,s||"").replace(/\{\{Region\}\}/gi,o||"").replace(/\{\{.*?\}\}/gi,""),/^[a-zA-Z]+:\/\//.test(r)||(r=i+"//"+r),"/"===r.slice(-1)&&(r=r.slice(0,-1))):r=o?i+"//cos."+o+".myqcloud.com":i+"//service.cos.myqcloud.com";var a="",c=o?"cos."+o+".myqcloud.com":"service.cos.myqcloud.com",d=r.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");c===d&&(a=c),De.call(this,{Action:"name/cos:GetService",url:r,method:"GET",headers:e.Headers,SignHost:a,tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i&&i.ListAllMyBucketsResult&&i.ListAllMyBucketsResult.Buckets&&i.ListAllMyBucketsResult.Buckets.Bucket||[];r=l.isArray(r)?r:[r];var n=i&&i.ListAllMyBucketsResult&&i.ListAllMyBucketsResult.Owner||{};t(null,{Buckets:r,Owner:n,statusCode:i.statusCode,headers:i.headers})}))}function d(e,t){var i=this,r="";if(e["BucketAZConfig"]){var n={BucketAZConfig:e.BucketAZConfig};r=l.json2xml({CreateBucketConfiguration:n})}De.call(this,{Action:"name/cos:PutBucket",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,body:r,tracker:e.tracker},(function(r,n){if(r)return t(r);var s=Re({protocol:i.options.Protocol,domain:i.options.Domain,bucket:e.Bucket,region:e.Region,isLocation:!0});t(null,{Location:s,statusCode:n.statusCode,headers:n.headers})}))}function u(e,t){De.call(this,{Action:"name/cos:HeadBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"HEAD",tracker:e.tracker},t)}function h(e,t){var i={};i["prefix"]=e["Prefix"]||"",i["delimiter"]=e["Delimiter"],i["marker"]=e["Marker"],i["max-keys"]=e["MaxKeys"],i["encoding-type"]=e["EncodingType"],De.call(this,{Action:"name/cos:GetBucket",ResourceKey:i["prefix"],method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:i,tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i.ListBucketResult||{},n=r.Contents||[],s=r.CommonPrefixes||[];n=l.isArray(n)?n:[n],s=l.isArray(s)?s:[s];var o=l.clone(r);l.extend(o,{Contents:n,CommonPrefixes:s,statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function p(e,t){De.call(this,{Action:"name/cos:DeleteBucket",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,method:"DELETE",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function f(e,t){var i=e.Headers,r="";if(e["AccessControlPolicy"]){var n=l.clone(e["AccessControlPolicy"]||{}),s=n.Grants||n.Grant;s=l.isArray(s)?s:[s],delete n.Grant,delete n.Grants,n.AccessControlList={Grant:s},r=l.json2xml({AccessControlPolicy:n}),i["Content-Type"]="application/xml",i["Content-MD5"]=l.b64(l.md5(r))}l.each(i,(function(e,t){0===t.indexOf("x-cos-grant-")&&(i[t]=Pe(i[t]))})),De.call(this,{Action:"name/cos:PutBucketACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,headers:i,action:"acl",body:r,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{statusCode:i.statusCode,headers:i.headers})}))}function m(e,t){De.call(this,{Action:"name/cos:GetBucketACL",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"acl",tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i.AccessControlPolicy||{},n=r.Owner||{},s=r.AccessControlList.Grant||[];s=l.isArray(s)?s:[s];var o=Ae(r);i.headers&&i.headers["x-cos-acl"]&&(o.ACL=i.headers["x-cos-acl"]),o=l.extend(o,{Owner:n,Grants:s,statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function g(e,t){var i=e["CORSConfiguration"]||{},r=i["CORSRules"]||e["CORSRules"]||[];r=l.clone(l.isArray(r)?r:[r]),l.each(r,(function(e){l.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var i=t+"s",r=e[i]||e[t]||[];delete e[i],e[t]=l.isArray(r)?r:[r]}))}));var n={CORSRule:r};e.ResponseVary&&(n.ResponseVary=e.ResponseVary);var s=l.json2xml({CORSConfiguration:n}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=l.b64(l.md5(s)),De.call(this,{Action:"name/cos:PutBucketCORS",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:s,action:"cors",headers:o,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{statusCode:i.statusCode,headers:i.headers})}))}function y(e,t){De.call(this,{Action:"name/cos:GetBucketCORS",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},(function(e,i){if(e)if(404===e.statusCode&&e.error&&"NoSuchCORSConfiguration"===e.error.Code){var r={CORSRules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var n=i.CORSConfiguration||{},s=n.CORSRules||n.CORSRule||[];s=l.clone(l.isArray(s)?s:[s]);var o=n.ResponseVary;l.each(s,(function(e){l.each(["AllowedOrigin","AllowedHeader","AllowedMethod","ExposeHeader"],(function(t){var i=t+"s",r=e[i]||e[t]||[];delete e[t],e[i]=l.isArray(r)?r:[r]}))})),t(null,{CORSRules:s,ResponseVary:o,statusCode:i.statusCode,headers:i.headers})}}))}function _(e,t){De.call(this,{Action:"name/cos:DeleteBucketCORS",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"cors",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode||e.statusCode,headers:i.headers})}))}function v(e,t){De.call(this,{Action:"name/cos:GetBucketLocation",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"location",tracker:e.tracker},t)}function b(e,t){var i=e["Policy"];try{"string"===typeof i&&(i=JSON.parse(i))}catch(s){}if(!i||"string"===typeof i)return t(l.error(new Error("Policy format error")));var r=JSON.stringify(i);i.version||(i.version="2.0");var n=e.Headers;n["Content-Type"]="application/json",n["Content-MD5"]=l.b64(l.md5(r)),De.call(this,{Action:"name/cos:PutBucketPolicy",method:"PUT",Bucket:e.Bucket,Region:e.Region,action:"policy",body:r,headers:n,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function S(e,t){De.call(this,{Action:"name/cos:GetBucketPolicy",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",rawBody:!0,tracker:e.tracker},(function(e,i){if(e)return e.statusCode&&403===e.statusCode?t(l.error(e,{ErrorStatus:"Access Denied"})):e.statusCode&&405===e.statusCode?t(l.error(e,{ErrorStatus:"Method Not Allowed"})):e.statusCode&&404===e.statusCode?t(l.error(e,{ErrorStatus:"Policy Not Found"})):t(e);var r={};try{r=JSON.parse(i.body)}catch(n){}t(null,{Policy:r,statusCode:i.statusCode,headers:i.headers})}))}function x(e,t){De.call(this,{Action:"name/cos:DeleteBucketPolicy",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"policy",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode||e.statusCode,headers:i.headers})}))}function k(e,t){var i=e["Tagging"]||{},r=i.TagSet||i.Tags||e["Tags"]||[];r=l.clone(l.isArray(r)?r:[r]);var n=l.json2xml({Tagging:{TagSet:{Tag:r}}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketTagging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"tagging",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function C(e,t){De.call(this,{Action:"name/cos:GetBucketTagging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},(function(e,i){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var r={Tags:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else{var n=[];try{n=i.Tagging.TagSet.Tag||[]}catch(s){}n=l.clone(l.isArray(n)?n:[n]),t(null,{Tags:n,statusCode:i.statusCode,headers:i.headers})}}))}function U(e,t){De.call(this,{Action:"name/cos:DeleteBucketTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function B(e,t){var i=e["LifecycleConfiguration"]||{},r=i.Rules||e.Rules||[];r=l.clone(r);var n=l.json2xml({LifecycleConfiguration:{Rule:r}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketLifecycle",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"lifecycle",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function w(e,t){De.call(this,{Action:"name/cos:GetBucketLifecycle",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},(function(e,i){if(e)if(404===e.statusCode&&e.error&&"NoSuchLifecycleConfiguration"===e.error.Code){var r={Rules:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var n=[];try{n=i.LifecycleConfiguration.Rule||[]}catch(s){}n=l.clone(l.isArray(n)?n:[n]),t(null,{Rules:n,statusCode:i.statusCode,headers:i.headers})}}))}function E(e,t){De.call(this,{Action:"name/cos:DeleteBucketLifecycle",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"lifecycle",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function T(e,t){if(e["VersioningConfiguration"]){var i=e["VersioningConfiguration"]||{},r=l.json2xml({VersioningConfiguration:i}),n=e.Headers;n["Content-Type"]="application/xml",n["Content-MD5"]=l.b64(l.md5(r)),De.call(this,{Action:"name/cos:PutBucketVersioning",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"versioning",headers:n,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}else t(l.error(new Error("missing param VersioningConfiguration")))}function A(e,t){De.call(this,{Action:"name/cos:GetBucketVersioning",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"versioning",tracker:e.tracker},(function(e,i){e||!i.VersioningConfiguration&&(i.VersioningConfiguration={}),t(e,i)}))}function P(e,t){var i=l.clone(e.ReplicationConfiguration),r=l.json2xml({ReplicationConfiguration:i});r=r.replace(/<(\/?)Rules>/gi,"<$1Rule>"),r=r.replace(/<(\/?)Tags>/gi,"<$1Tag>");var n=e.Headers;n["Content-Type"]="application/xml",n["Content-MD5"]=l.b64(l.md5(r)),De.call(this,{Action:"name/cos:PutBucketReplication",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"replication",headers:n,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function R(e,t){De.call(this,{Action:"name/cos:GetBucketReplication",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,i){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"ReplicationConfigurationnotFoundError"!==e.error.Code)t(e);else{var r={ReplicationConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else!i.ReplicationConfiguration&&(i.ReplicationConfiguration={}),i.ReplicationConfiguration.Rule&&(i.ReplicationConfiguration.Rules=l.makeArray(i.ReplicationConfiguration.Rule),delete i.ReplicationConfiguration.Rule),t(e,i)}))}function I(e,t){De.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function O(e,t){if(e["WebsiteConfiguration"]){var i=l.clone(e["WebsiteConfiguration"]||{}),r=i["RoutingRules"]||i["RoutingRule"]||[];r=l.isArray(r)?r:[r],delete i.RoutingRule,delete i.RoutingRules,r.length&&(i.RoutingRules={RoutingRule:r});var n=l.json2xml({WebsiteConfiguration:i}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketWebsite",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"website",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}else t(l.error(new Error("missing param WebsiteConfiguration")))}function L(e,t){De.call(this,{Action:"name/cos:GetBucketWebsite",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"website",tracker:e.tracker},(function(e,i){if(e)if(404===e.statusCode&&"NoSuchWebsiteConfiguration"===e.error.Code){var r={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var n=i.WebsiteConfiguration||{};if(n["RoutingRules"]){var s=l.clone(n["RoutingRules"].RoutingRule||[]);s=l.makeArray(s),n.RoutingRules=s}t(null,{WebsiteConfiguration:n,statusCode:i.statusCode,headers:i.headers})}}))}function z(e,t){De.call(this,{Action:"name/cos:DeleteBucketWebsite",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"website",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function D(e,t){if(e["RefererConfiguration"]){var i=l.clone(e["RefererConfiguration"]||{}),r=i["DomainList"]||{},n=r["Domains"]||r["Domain"]||[];n=l.isArray(n)?n:[n],n.length&&(i.DomainList={Domain:n});var s=l.json2xml({RefererConfiguration:i}),o=e.Headers;o["Content-Type"]="application/xml",o["Content-MD5"]=l.b64(l.md5(s)),De.call(this,{Action:"name/cos:PutBucketReferer",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:s,action:"referer",headers:o,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}else t(l.error(new Error("missing param RefererConfiguration")))}function F(e,t){De.call(this,{Action:"name/cos:GetBucketReferer",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"referer",tracker:e.tracker},(function(e,i){if(e)if(404===e.statusCode&&"NoSuchRefererConfiguration"===e.error.Code){var r={WebsiteConfiguration:{},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var n=i.RefererConfiguration||{};if(n["DomainList"]){var s=l.makeArray(n["DomainList"].Domain||[]);n.DomainList={Domains:s}}t(null,{RefererConfiguration:n,statusCode:i.statusCode,headers:i.headers})}}))}function N(e,t){var i=e["DomainConfiguration"]||{},r=i.DomainRule||e.DomainRule||[];r=l.clone(r);var n=l.json2xml({DomainConfiguration:{DomainRule:r}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketDomain",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"domain",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function M(e,t){De.call(this,{Action:"name/cos:GetBucketDomain",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},(function(e,i){if(e)return t(e);var r=[];try{r=i.DomainConfiguration.DomainRule||[]}catch(n){}r=l.clone(l.isArray(r)?r:[r]),t(null,{DomainRule:r,statusCode:i.statusCode,headers:i.headers})}))}function j(e,t){De.call(this,{Action:"name/cos:DeleteBucketDomain",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"domain",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function H(e,t){var i=e["OriginConfiguration"]||{},r=i.OriginRule||e.OriginRule||[];r=l.clone(r);var n=l.json2xml({OriginConfiguration:{OriginRule:r}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketOrigin",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"origin",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function G(e,t){De.call(this,{Action:"name/cos:GetBucketOrigin",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},(function(e,i){if(e)return t(e);var r=[];try{r=i.OriginConfiguration.OriginRule||[]}catch(n){}r=l.clone(l.isArray(r)?r:[r]),t(null,{OriginRule:r,statusCode:i.statusCode,headers:i.headers})}))}function K(e,t){De.call(this,{Action:"name/cos:DeleteBucketOrigin",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"origin",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function V(e,t){var i=l.json2xml({BucketLoggingStatus:e["BucketLoggingStatus"]||""}),r=e.Headers;r["Content-Type"]="application/xml",r["Content-MD5"]=l.b64(l.md5(i)),De.call(this,{Action:"name/cos:PutBucketLogging",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:i,action:"logging",headers:r,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function Y(e,t){De.call(this,{Action:"name/cos:GetBucketLogging",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"logging",tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{BucketLoggingStatus:i.BucketLoggingStatus,statusCode:i.statusCode,headers:i.headers})}))}function q(e,t,i){var r=l.clone(t["InventoryConfiguration"]);if(r.OptionalFields){var n=r.OptionalFields||[];r.OptionalFields={Field:n}}if(r.Destination&&r.Destination.COSBucketDestination&&r.Destination.COSBucketDestination.Encryption){var s=r.Destination.COSBucketDestination.Encryption;Object.keys(s).indexOf("SSECOS")>-1&&(s["SSE-COS"]=s["SSECOS"],delete s["SSECOS"])}var o=l.json2xml({InventoryConfiguration:r}),a=t.Headers;a["Content-Type"]="application/xml",a["Content-MD5"]=l.b64(l.md5(o));var c="PUT"===e?"name/cos:PutBucketInventory":"name/cos:PostBucketInventory";De.call(this,{Action:c,method:e,Bucket:t.Bucket,Region:t.Region,body:o,action:"inventory",qs:{id:t["Id"]},headers:a,tracker:t.tracker},(function(e,t){return e&&204===e.statusCode?i(null,{statusCode:e.statusCode}):e?i(e):void i(null,{statusCode:t.statusCode,headers:t.headers})}))}function W(e,t){return q.call(this,"PUT",e,t)}function X(e,t){return q.call(this,"POST",e,t)}function Q(e,t){De.call(this,{Action:"name/cos:GetBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e["Id"]},tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i["InventoryConfiguration"];if(r&&r.OptionalFields&&r.OptionalFields.Field){var n=r.OptionalFields.Field;l.isArray(n)||(n=[n]),r.OptionalFields=n}if(r.Destination&&r.Destination.COSBucketDestination&&r.Destination.COSBucketDestination.Encryption){var s=r.Destination.COSBucketDestination.Encryption;Object.keys(s).indexOf("SSE-COS")>-1&&(s["SSECOS"]=s["SSE-COS"],delete s["SSE-COS"])}t(null,{InventoryConfiguration:r,statusCode:i.statusCode,headers:i.headers})}))}function $(e,t){De.call(this,{Action:"name/cos:ListBucketInventory",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{"continuation-token":e["ContinuationToken"]},tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i["ListInventoryConfigurationResult"],n=r.InventoryConfiguration||[];n=l.isArray(n)?n:[n],delete r["InventoryConfiguration"],l.each(n,(function(e){if(e&&e.OptionalFields&&e.OptionalFields.Field){var t=e.OptionalFields.Field;l.isArray(t)||(t=[t]),e.OptionalFields=t}if(e.Destination&&e.Destination.COSBucketDestination&&e.Destination.COSBucketDestination.Encryption){var i=e.Destination.COSBucketDestination.Encryption;Object.keys(i).indexOf("SSE-COS")>-1&&(i["SSECOS"]=i["SSE-COS"],delete i["SSE-COS"])}})),r.InventoryConfigurations=n,l.extend(r,{statusCode:i.statusCode,headers:i.headers}),t(null,r)}))}function J(e,t){De.call(this,{Action:"name/cos:DeleteBucketInventory",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"inventory",qs:{id:e["Id"]},tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function Z(e,t){if(e["AccelerateConfiguration"]){var i={AccelerateConfiguration:e.AccelerateConfiguration||{}},r=l.json2xml(i),n={"Content-Type":"application/xml"};n["Content-MD5"]=l.b64(l.md5(r)),De.call(this,{Action:"name/cos:PutBucketAccelerate",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:r,action:"accelerate",headers:n,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{statusCode:i.statusCode,headers:i.headers})}))}else t(l.error(new Error("missing param AccelerateConfiguration")))}function ee(e,t){De.call(this,{Action:"name/cos:GetBucketAccelerate",method:"GET",Bucket:e.Bucket,Region:e.Region,action:"accelerate",tracker:e.tracker},(function(e,i){e||!i.AccelerateConfiguration&&(i.AccelerateConfiguration={}),t(e,i)}))}function te(e,t){var i=e.ServerSideEncryptionConfiguration||{},r=i.Rule||i.Rules||[],n=l.json2xml({ServerSideEncryptionConfiguration:{Rule:r}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutBucketEncryption",method:"PUT",Bucket:e.Bucket,Region:e.Region,body:n,action:"encryption",headers:s,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ie(e,t){De.call(this,{Action:"name/cos:GetBucketEncryption",method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption",tracker:e.tracker},(function(e,i){if(e)if(404===e.statusCode&&"NoSuchEncryptionConfiguration"===e.code){var r={EncryptionConfiguration:{Rules:[]},statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else t(e);else{var n=l.makeArray(i.EncryptionConfiguration&&i.EncryptionConfiguration.Rule||[]);i.EncryptionConfiguration={Rules:n},t(e,i)}}))}function re(e,t){De.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"encryption",tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ne(e,t){De.call(this,{Action:"name/cos:HeadObject",method:"HEAD",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},(function(i,r){if(i){var n=i.statusCode;return e.Headers["If-Modified-Since"]&&n&&304===n?t(null,{NotModified:!0,statusCode:n}):t(i)}r.ETag=l.attr(r.headers,"etag",""),t(null,r)}))}function se(e,t){var i={};i["prefix"]=e["Prefix"]||"",i["delimiter"]=e["Delimiter"],i["key-marker"]=e["KeyMarker"],i["version-id-marker"]=e["VersionIdMarker"],i["max-keys"]=e["MaxKeys"],i["encoding-type"]=e["EncodingType"],De.call(this,{Action:"name/cos:GetBucketObjectVersions",ResourceKey:i["prefix"],method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:i,action:"versions",tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i.ListVersionsResult||{},n=r.DeleteMarker||[];n=l.isArray(n)?n:[n];var s=r.Version||[];s=l.isArray(s)?s:[s];var o=l.clone(r);delete o.DeleteMarker,delete o.Version,l.extend(o,{DeleteMarkers:n,Versions:s,statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function oe(e,t){var i=this;if(i.logger.info({cate:"PROCESS",tag:"download",msg:"[key=".concat(e.Key,"] getObject开始")}),this.options.ObjectKeySimplifyCheck){var r=l.simplifyPath(e.Key);if("/"===r)return void t(l.error(new Error("The Getobject Key is illegal")))}var n=e.Query||{},s=e.QueryString||"",o=l.throttleOnProgress.call(this,0,e.onProgress),a=e.tracker;a&&a.setParams({signStartTime:(new Date).getTime()}),n["response-content-type"]=e["ResponseContentType"],n["response-content-language"]=e["ResponseContentLanguage"],n["response-expires"]=e["ResponseExpires"],n["response-cache-control"]=e["ResponseCacheControl"],n["response-content-disposition"]=e["ResponseContentDisposition"],n["response-content-encoding"]=e["ResponseContentEncoding"],De.call(this,{Action:"name/cos:GetObject",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,DataType:e.DataType,headers:e.Headers,qs:n,qsStr:s,rawBody:!0,onDownloadProgress:o,tracker:a},(function(r,n){if(o(null,!0),r){var s=r.statusCode;return e.Headers["If-Modified-Since"]&&s&&304===s?t(null,{NotModified:!0}):t(r)}t(null,{Body:n.body,ETag:l.attr(n.headers,"etag",""),statusCode:n.statusCode,headers:n.headers}),i.logger.info({cate:"PROCESS",tag:"download",msg:"[key=".concat(e.Key,"] getObject结束")})}))}function ae(e,t){var i=this,r=e.ContentLength,n=l.throttleOnProgress.call(i,r,e.onProgress);i.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] putObject开始")});var s=e.Headers;s["Cache-Control"]||s["cache-control"]||(s["Cache-Control"]=""),s["Content-Type"]||s["content-type"]||(s["Content-Type"]=e.Body&&e.Body.type||"");var o=e.UploadAddMetaMd5||i.options.UploadAddMetaMd5||i.options.UploadCheckContentMd5,a=e.tracker;o&&a&&a.setParams({md5StartTime:(new Date).getTime()}),o&&i.logger.debug({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] 开始计算 md5")}),l.getBodyMd5(o,e.Body,(function(o){o&&(i.logger.debug({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] md5: ").concat(o,"，md5Base64=").concat(l.b64(o))}),a&&a.setParams({md5EndTime:(new Date).getTime()}),i.options.UploadCheckContentMd5&&(s["Content-MD5"]=l.b64(o)),(e.UploadAddMetaMd5||i.options.UploadAddMetaMd5)&&(s["x-cos-meta-md5"]=o)),void 0!==e.ContentLength&&(s["Content-Length"]=e.ContentLength),n(null,!0),De.call(i,{Action:"name/cos:PutObject",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:e.Query,body:e.Body,onProgress:n,tracker:a},(function(s,o){if(s)return i.logger.error({cate:"ERROR",tag:"upload",msg:"上传失败，错误信息：".concat(JSON.stringify(s))}),n(null,!0),t(s);n({loaded:r,total:r},!0);var a=Re({ForcePathStyle:i.options.ForcePathStyle,protocol:i.options.Protocol,domain:i.options.Domain,bucket:e.Bucket,region:i.options.UseAccelerate?"accelerate":e.Region,object:e.Key});a=a.substr(a.indexOf("://")+3),o.Location=a,o.ETag=l.attr(o.headers,"etag",""),i.logger.info({cate:"RESULT",tag:"upload",msg:"上传成功，Location=".concat(a)}),i.logger.info({cate:"PROCESS",tag:"upload",msg:"[key=".concat(e.Key,"] putObject结束")}),t(null,o)}))}),e.onHashProgress)}function le(e,t){De.call(this,{Action:"name/cos:DeleteObject",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,VersionId:e.VersionId,action:e.Recursive?"recursive":"",tracker:e.tracker},(function(e,i){if(e){var r=e.statusCode;return r&&404===r?t(null,{BucketNotFound:!0,statusCode:r}):t(e)}t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ce(e,t){var i={};e.VersionId&&(i.versionId=e.VersionId),De.call(this,{Action:"name/cos:GetObjectACL",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:i,action:"acl",tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i.AccessControlPolicy||{},n=r.Owner||{},s=r.AccessControlList&&r.AccessControlList.Grant||[];s=l.isArray(s)?s:[s];var o=Ae(r);delete o.GrantWrite,i.headers&&i.headers["x-cos-acl"]&&(o.ACL=i.headers["x-cos-acl"]),o=l.extend(o,{Owner:n,Grants:s,statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function de(e,t){var i=e.Headers,r="";if(e["AccessControlPolicy"]){var n=l.clone(e["AccessControlPolicy"]||{}),s=n.Grants||n.Grant;s=l.isArray(s)?s:[s],delete n.Grant,delete n.Grants,n.AccessControlList={Grant:s},r=l.json2xml({AccessControlPolicy:n}),i["Content-Type"]="application/xml",i["Content-MD5"]=l.b64(l.md5(r))}l.each(i,(function(e,t){0===t.indexOf("x-cos-grant-")&&(i[t]=Pe(i[t]))})),De.call(this,{Action:"name/cos:PutObjectACL",method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"acl",headers:i,body:r,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ue(e,t){var i=e.Headers;i["Origin"]=e["Origin"],i["Access-Control-Request-Method"]=e["AccessControlRequestMethod"],i["Access-Control-Request-Headers"]=e["AccessControlRequestHeaders"],De.call(this,{Action:"name/cos:OptionsObject",method:"OPTIONS",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:i,tracker:e.tracker},(function(e,i){if(e)return e.statusCode&&403===e.statusCode?t(null,{OptionsForbidden:!0,statusCode:e.statusCode}):t(e);var r=i.headers||{};t(null,{AccessControlAllowOrigin:r["access-control-allow-origin"],AccessControlAllowMethods:r["access-control-allow-methods"],AccessControlAllowHeaders:r["access-control-allow-headers"],AccessControlExposeHeaders:r["access-control-expose-headers"],AccessControlMaxAge:r["access-control-max-age"],statusCode:i.statusCode,headers:i.headers})}))}function he(e,t){var i=this,r=e.Headers;r["Cache-Control"]||r["cache-control"]||(r["Cache-Control"]="");var n=e.CopySource||"",s=l.getSourceParams.call(this,n);if(s){var o=s.Bucket,a=s.Region,c=decodeURIComponent(s.Key);De.call(this,{Scope:[{action:"name/cos:GetObject",bucket:o,region:a,prefix:c},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,headers:e.Headers,tracker:e.tracker},(function(r,n){if(r)return t(r);var s=l.clone(n.CopyObjectResult||{}),o=Re({ForcePathStyle:i.options.ForcePathStyle,protocol:i.options.Protocol,domain:i.options.Domain,bucket:e.Bucket,region:e.Region,object:e.Key,isLocation:!0});l.extend(s,{Location:o,statusCode:n.statusCode,headers:n.headers}),t(null,s)}))}else t(l.error(new Error("CopySource format error")))}function pe(e,t){var i=e.CopySource||"",r=l.getSourceParams.call(this,i);if(r){var n=r.Bucket,s=r.Region,o=decodeURIComponent(r.Key);De.call(this,{Scope:[{action:"name/cos:GetObject",bucket:n,region:s,prefix:o},{action:"name/cos:PutObject",bucket:e.Bucket,region:e.Region,prefix:e.Key}],method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,qs:{partNumber:e["PartNumber"],uploadId:e["UploadId"]},headers:e.Headers,tracker:e.tracker},(function(e,i){if(e)return t(e);var r=l.clone(i.CopyPartResult||{});l.extend(r,{statusCode:i.statusCode,headers:i.headers}),t(null,r)}))}else t(l.error(new Error("CopySource format error")))}function fe(e,t){var i=e.Objects||[],r=e.Quiet;i=l.isArray(i)?i:[i];var n=l.json2xml({Delete:{Object:i,Quiet:r||!1}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n));var o=l.map(i,(function(t){return{action:"name/cos:DeleteObject",bucket:e.Bucket,region:e.Region,prefix:t.Key}}));De.call(this,{Scope:o,method:"POST",Bucket:e.Bucket,Region:e.Region,body:n,action:"delete",headers:s,tracker:e.tracker},(function(e,i){if(e)return t(e);var r=i.DeleteResult||{},n=r.Deleted||[],s=r.Error||[];n=l.isArray(n)?n:[n],s=l.isArray(s)?s:[s];var o=l.clone(r);l.extend(o,{Error:s,Deleted:n,statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function me(e,t){var i=e.Headers;if(e["RestoreRequest"]){var r=e.RestoreRequest||{},n=l.json2xml({RestoreRequest:r});i["Content-Type"]="application/xml",i["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:RestoreObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,VersionId:e.VersionId,body:n,action:"restore",headers:i,tracker:e.tracker},t)}else t(l.error(new Error("missing param RestoreRequest")))}function ge(e,t){var i=e["Tagging"]||{},r=i.TagSet||i.Tags||e["Tags"]||[];r=l.clone(l.isArray(r)?r:[r]);var n=l.json2xml({Tagging:{TagSet:{Tag:r}}}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:PutObjectTagging",method:"PUT",Bucket:e.Bucket,Key:e.Key,Region:e.Region,body:n,action:"tagging",headers:s,VersionId:e.VersionId,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ye(e,t){De.call(this,{Action:"name/cos:GetObjectTagging",method:"GET",Key:e.Key,Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},(function(e,i){if(e)if(404!==e.statusCode||!e.error||"Not Found"!==e.error&&"NoSuchTagSet"!==e.error.Code)t(e);else{var r={Tags:[],statusCode:e.statusCode};e.headers&&(r.headers=e.headers),t(null,r)}else{var n=[];try{n=i.Tagging.TagSet.Tag||[]}catch(s){}n=l.clone(l.isArray(n)?n:[n]),t(null,{Tags:n,statusCode:i.statusCode,headers:i.headers})}}))}function _e(e,t){De.call(this,{Action:"name/cos:DeleteObjectTagging",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"tagging",VersionId:e.VersionId,tracker:e.tracker},(function(e,i){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:i.statusCode,headers:i.headers})}))}function ve(e,t){var i=e["SelectType"];if(!i)return t(l.error(new Error("missing param SelectType")));var r=e["SelectRequest"]||{},n=l.json2xml({SelectRequest:r}),s=e.Headers;s["Content-Type"]="application/xml",s["Content-MD5"]=l.b64(l.md5(n)),De.call(this,{Action:"name/cos:GetObject",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,action:"select",qs:{"select-type":e["SelectType"]},VersionId:e.VersionId,body:n,DataType:"arraybuffer",rawBody:!0,tracker:e.tracker},(function(e,i){if(e&&204===e.statusCode)return t(null,{statusCode:e.statusCode});if(e)return t(e);var r=l.parseSelectPayload(i.body);t(null,{statusCode:i.statusCode,headers:i.headers,Body:r.body,Payload:r.payload})}))}function be(e,t){var i=this,r=e.Headers,n=e.tracker;r["Cache-Control"]||r["cache-control"]||(r["Cache-Control"]=""),r["Content-Type"]||r["content-type"]||(r["Content-Type"]=e.Body&&e.Body.type||"");var s=e.Body&&(e.UploadAddMetaMd5||i.options.UploadAddMetaMd5);s&&n&&n.setParams({md5StartTime:(new Date).getTime()}),l.getBodyMd5(s,e.Body,(function(r){r&&(e.Headers["x-cos-meta-md5"]=r),s&&n&&n.setParams({md5EndTime:(new Date).getTime()}),De.call(i,{Action:"name/cos:InitiateMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:"uploads",headers:e.Headers,qs:e.Query,tracker:n},(function(e,i){return e?(n&&n.parent&&n.parent.setParams({errorNode:"multipartInit"}),t(e)):(i=l.clone(i||{}),i&&i.InitiateMultipartUploadResult?t(null,l.extend(i.InitiateMultipartUploadResult,{statusCode:i.statusCode,headers:i.headers})):void t(null,i))}))}),e.onHashProgress)}function Se(e,t){var i=this;l.getFileSize("multipartUpload",e,(function(){var r=e.tracker,n=i.options.UploadCheckContentMd5;n&&r&&r.setParams({md5StartTime:(new Date).getTime()}),l.getBodyMd5(n,e.Body,(function(s){s&&(e.Headers["Content-MD5"]=l.b64(s)),n&&r&&r.setParams({md5EndTime:(new Date).getTime()}),r&&r.setParams({partNumber:e.PartNumber}),De.call(i,{Action:"name/cos:UploadPart",TaskId:e.TaskId,method:"PUT",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{partNumber:e["PartNumber"],uploadId:e["UploadId"]},headers:e.Headers,onProgress:e.onProgress,body:e.Body||null,tracker:r},(function(e,i){if(e)return r&&r.parent&&r.parent.setParams({errorNode:"multipartUpload"}),t(e);t(null,{ETag:l.attr(i.headers,"etag",""),statusCode:i.statusCode,headers:i.headers})}))}))}))}function xe(e,t){for(var i=this,r=e.UploadId,n=e["Parts"],s=e.tracker,o=0,a=n.length;o<a;o++)n[o]["ETag"]&&0===n[o]["ETag"].indexOf('"')||(n[o]["ETag"]='"'+n[o]["ETag"]+'"');var c=l.json2xml({CompleteMultipartUpload:{Part:n}});c=c.replace(/\n\s*/g,"");var d=e.Headers;d["Content-Type"]="application/xml",d["Content-MD5"]=l.b64(l.md5(c)),De.call(this,{Action:"name/cos:CompleteMultipartUpload",method:"POST",Bucket:e.Bucket,Region:e.Region,Key:e.Key,qs:{uploadId:r},body:c,headers:d,tracker:s},(function(r,n){if(r)return s&&s.parent&&s.parent.setParams({errorNode:"multipartComplete"}),t(r);var o=Re({ForcePathStyle:i.options.ForcePathStyle,protocol:i.options.Protocol,domain:i.options.Domain,bucket:e.Bucket,region:i.options.UseAccelerate?"accelerate":e.Region,object:e.Key,isLocation:!0}),a=n.CompleteMultipartUploadResult||{};if(a.ProcessResults&&(a.UploadResult={OriginalInfo:{Key:a.Key,Location:o,ETag:a.ETag,ImageInfo:a.ImageInfo},ProcessResults:a.ProcessResults},delete a.ImageInfo,delete a.ProcessResults),a.CallbackResult){var c=a.CallbackResult;if("200"===c.Status&&c.CallbackBody)try{a.CallbackBody=JSON.parse(l.decodeBase64(c.CallbackBody))}catch(h){a.CallbackBody={}}else a.CallbackError=c.Error||{};delete a.CallbackResult}if(a.ReturnBodyResult){var d=a.ReturnBodyResult;if("200"===d.Status&&d.ReturnBody)try{a.ReturnBody=JSON.parse(l.decodeBase64(d.ReturnBody))}catch(h){a.ReturnBody={}}else a.ReturnError={Code:d.Code,Message:d.Message,Status:d.Status};delete a.ReturnBodyResult}var u=l.extend(a,{Location:o,statusCode:n.statusCode,headers:n.headers});t(null,u)}))}function ke(e,t){var i={};i["delimiter"]=e["Delimiter"],i["encoding-type"]=e["EncodingType"],i["prefix"]=e["Prefix"]||"",i["max-uploads"]=e["MaxUploads"],i["key-marker"]=e["KeyMarker"],i["upload-id-marker"]=e["UploadIdMarker"],i=l.clearKey(i);var r=e.tracker;r&&r.setParams({signStartTime:(new Date).getTime()}),De.call(this,{Action:"name/cos:ListMultipartUploads",ResourceKey:i["prefix"],method:"GET",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,qs:i,action:"uploads",tracker:r},(function(e,i){if(e)return r&&r.parent&&r.parent.setParams({errorNode:"multipartList"}),t(e);if(i&&i.ListMultipartUploadsResult){var n=i.ListMultipartUploadsResult.Upload||[];n=l.isArray(n)?n:[n],i.ListMultipartUploadsResult.Upload=n}var s=l.clone(i.ListMultipartUploadsResult||{});l.extend(s,{statusCode:i.statusCode,headers:i.headers}),t(null,s)}))}function Ce(e,t){var i={},r=e.tracker;i["uploadId"]=e["UploadId"],i["encoding-type"]=e["EncodingType"],i["max-parts"]=e["MaxParts"],i["part-number-marker"]=e["PartNumberMarker"],De.call(this,{Action:"name/cos:ListParts",method:"GET",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:i,tracker:r},(function(e,i){if(e)return r&&r.parent&&r.parent.setParams({errorNode:"multipartListPart"}),t(e);var n=i.ListPartsResult||{},s=n.Part||[];s=l.isArray(s)?s:[s],n.Part=s;var o=l.clone(n);l.extend(o,{statusCode:i.statusCode,headers:i.headers}),t(null,o)}))}function Ue(e,t){var i={};i["uploadId"]=e["UploadId"],De.call(this,{Action:"name/cos:AbortMultipartUpload",method:"DELETE",Bucket:e.Bucket,Region:e.Region,Key:e.Key,headers:e.Headers,qs:i,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,{statusCode:i.statusCode,headers:i.headers})}))}function Be(e,t){De.call(this,{method:e.Method,Bucket:e.Bucket,Region:e.Region,Key:e.Key,action:e.Action,headers:e.Headers,qs:e.Query,body:e.Body,Url:e.Url,rawBody:e.RawBody,DataType:e.DataType,tracker:e.tracker},(function(e,i){if(e)return t(e);i&&i.body&&(i.Body=i.body,delete i.body),t(e,i)}))}function we(e,t){var i=e.Headers;i["Cache-Control"]||i["cache-control"]||(i["Cache-Control"]=""),i["Content-Type"]||i["content-type"]||(i["Content-Type"]=e.Body&&e.Body.type||""),De.call(this,{Action:"name/cos:AppendObject",method:"POST",Bucket:e.Bucket,Region:e.Region,action:"append",Key:e.Key,body:e.Body,qs:{position:e.Position},headers:e.Headers,tracker:e.tracker},(function(e,i){if(e)return t(e);t(null,i)}))}function Ee(e){var t=this;return l.getAuth({SecretId:e.SecretId||this.options.SecretId||"",SecretKey:e.SecretKey||this.options.SecretKey||"",Bucket:e.Bucket,Region:e.Region,Method:e.Method,Key:e.Key,Query:e.Query,Headers:e.Headers,Expires:e.Expires,UseRawKey:t.options.UseRawKey,SystemClockOffset:t.options.SystemClockOffset})}function Te(e,t){var i=this,r=void 0===e.UseAccelerate?i.options.UseAccelerate:e.UseAccelerate,n=Re({ForcePathStyle:i.options.ForcePathStyle,protocol:e.Protocol||i.options.Protocol,domain:e.Domain||i.options.Domain,bucket:e.Bucket,region:r?"accelerate":e.Region,object:e.Key}),s="";e.Query&&(s+=l.obj2str(e.Query)),e.QueryString&&(s+=(s?"&":"")+e.QueryString);var o=n;if(void 0!==e.Sign&&!e.Sign)return s&&(o+="?"+s),t(null,{Url:o}),o;var a=Ie.call(this,{Bucket:e.Bucket,Region:e.Region,UseAccelerate:e.UseAccelerate,Url:n}),c=Oe.call(this,{Action:"PUT"===(e.Method||"").toUpperCase()?"name/cos:PutObject":"name/cos:GetObject",Bucket:e.Bucket||"",Region:e.Region||"",Method:e.Method||"get",Key:e.Key,Expires:e.Expires,Headers:e.Headers,Query:e.Query,SignHost:a,ForceSignHost:!1!==e.ForceSignHost&&i.options.ForceSignHost},(function(e,i){if(t)if(e)t(e);else{var r=function(e){var t=e.match(/q-url-param-list.*?(?=&)/g)[0],i="q-url-param-list="+encodeURIComponent(t.replace(/q-url-param-list=/,"")).toLowerCase(),r=new RegExp(t,"g"),n=e.replace(r,i);return n},o=n;o+="?"+(i.Authorization.indexOf("q-signature")>-1?r(i.Authorization):"sign="+encodeURIComponent(i.Authorization)),i.SecurityToken&&(o+="&x-cos-security-token="+i.SecurityToken),i.ClientIP&&(o+="&clientIP="+i.ClientIP),i.ClientUA&&(o+="&clientUA="+i.ClientUA),i.Token&&(o+="&token="+i.Token),s&&(o+="&"+s),setTimeout((function(){t(null,{Url:o})}))}}));return c?(o+="?"+c.Authorization+(c.SecurityToken?"&x-cos-security-token="+c.SecurityToken:""),s&&(o+="&"+s)):s&&(o+="?"+s),o}function Ae(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},i={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},r=e&&e.AccessControlList||{},n=r.Grant;n&&(n=l.isArray(n)?n:[n]);var s={READ:0,WRITE:0,FULL_CONTROL:0};return n&&n.length&&l.each(n,(function(r){var n=r.Grantee.URI&&r.Grantee.URI.endsWith("/groups/global/AllUsers");"qcs::cam::anyone:anyone"===r.Grantee.ID||n?s[r.Permission]=1:r.Grantee.ID!==e.Owner.ID&&t[i[r.Permission]].push('id="'+r.Grantee.ID+'"')})),s.FULL_CONTROL||s.WRITE&&s.READ?t.ACL="public-read-write":s.READ?t.ACL="public-read":t.ACL="private",l.each(i,(function(e){t[e]=Pe(t[e].join(","))})),t}function Pe(e){var t,i,r=e.split(","),n={};for(t=0;t<r.length;)i=r[t].trim(),n[i]?r.splice(t,1):(n[i]=!0,r[t]=i,t++);return r.join(",")}function Re(e){var t=e.region||"",i=e.bucket||"",r=i.substr(0,i.lastIndexOf("-")),s=i.substr(i.lastIndexOf("-")+1),o=e.domain,a=e.object;"function"===typeof o&&(o=o({Bucket:i,Region:t})),["http","https"].includes(e.protocol)&&(e.protocol=e.protocol+":");var c=e.protocol||(l.isBrowser&&"object"===("undefined"===typeof location?"undefined":n(location))&&"http:"===location.protocol?"http:":"https:");o||(o=["cn-south","cn-south-2","cn-north","cn-east","cn-southwest","sg"].indexOf(t)>-1?"{Region}.myqcloud.com":"cos.{Region}.myqcloud.com",e.ForcePathStyle||(o="{Bucket}."+o)),o=o.replace(/\{\{AppId\}\}/gi,s).replace(/\{\{Bucket\}\}/gi,r).replace(/\{\{Region\}\}/gi,t).replace(/\{\{.*?\}\}/gi,""),o=o.replace(/\{AppId\}/gi,s).replace(/\{BucketName\}/gi,r).replace(/\{Bucket\}/gi,i).replace(/\{Region\}/gi,t).replace(/\{.*?\}/gi,""),/^[a-zA-Z]+:\/\//.test(o)||(o=c+"//"+o),"/"===o.slice(-1)&&(o=o.slice(0,-1));var d=o;return e.ForcePathStyle&&(d+="/"+i),d+="/",a&&(d+=l.camSafeUrlEncode(a).replace(/%2F/g,"/")),e.isLocation&&(d=d.replace(/^https?:\/\//,"")),d}var Ie=function(e){var t=e.Url||e.Bucket&&e.Region;if(!t)return"";var i=void 0===e.UseAccelerate?this.options.UseAccelerate:e.UseAccelerate,r=e.Url||Re({ForcePathStyle:this.options.ForcePathStyle,protocol:this.options.Protocol,domain:this.options.Domain,bucket:e.Bucket,region:i?"accelerate":e.Region}),n=r.replace(/^https?:\/\/([^/]+)(\/.*)?$/,"$1");return n};function Oe(e,t){var i=l.clone(e.Headers),r="";l.each(i,(function(e,t){(""===e||["content-type","cache-control","expires"].indexOf(t.toLowerCase())>-1)&&delete i[t],"host"===t.toLowerCase()&&(r=e)}));var n=!1!==e.ForceSignHost;!r&&e.SignHost&&n&&(i.Host=e.SignHost);var s=!1,o=function(e,i){s||(s=!0,i&&i.XCosSecurityToken&&!i.SecurityToken&&(i=l.clone(i),i.SecurityToken=i.XCosSecurityToken,delete i.XCosSecurityToken),t&&t(e,i))},a=this,c=e.Bucket||"",d=e.Region||"",u=e.Key||"";a.options.ForcePathStyle&&c&&(u=c+"/"+u);var h="/"+u,p={},f=e.Scope;if(!f){var m=e.Action||"",g=e.ResourceKey||e.Key||"";f=e.Scope||[{action:m,bucket:c,region:d,prefix:g}]}var y=l.md5(JSON.stringify(f));a._StsCache=a._StsCache||[],function(){var e,t;for(e=a._StsCache.length-1;e>=0;e--){t=a._StsCache[e];var i=Math.round(l.getSkewTime(a.options.SystemClockOffset)/1e3)+30;if(t.StartTime&&i<t.StartTime||i>=t.ExpiredTime)a._StsCache.splice(e,1);else if(!t.ScopeLimit||t.ScopeLimit&&t.ScopeKey===y){p=t;break}}}();var _=function(){var t="";p.StartTime&&e.Expires?t=p.StartTime+";"+(p.StartTime+1*e.Expires):p.StartTime&&p.ExpiredTime&&(t=p.StartTime+";"+p.ExpiredTime);var r=l.getAuth({SecretId:p.TmpSecretId,SecretKey:p.TmpSecretKey,Method:e.Method,Pathname:h,Query:e.Query,Headers:i,Expires:e.Expires,UseRawKey:a.options.UseRawKey,SystemClockOffset:a.options.SystemClockOffset,KeyTime:t,ForceSignHost:n}),s={Authorization:r,SecurityToken:p.SecurityToken||p.XCosSecurityToken||"",Token:p.Token||"",ClientIP:p.ClientIP||"",ClientUA:p.ClientUA||"",SignFrom:"client"};o(null,s)},v=function(e){if(e.Authorization){var t=!1,i=e.Authorization;if(i)if(i.indexOf(" ")>-1)t=!1;else if(i.indexOf("q-sign-algorithm=")>-1&&i.indexOf("q-ak=")>-1&&i.indexOf("q-sign-time=")>-1&&i.indexOf("q-key-time=")>-1&&i.indexOf("q-url-param-list=")>-1)t=!0;else try{i=atob(i),i.indexOf("a=")>-1&&i.indexOf("k=")>-1&&i.indexOf("t=")>-1&&i.indexOf("r=")>-1&&i.indexOf("b=")>-1&&(t=!0)}catch(r){}if(!t)return l.error(new Error("getAuthorization callback params format error"))}else{if(!e.TmpSecretId)return l.error(new Error('getAuthorization callback params missing "TmpSecretId"'));if(!e.TmpSecretKey)return l.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));if(!e.SecurityToken&&!e.XCosSecurityToken)return l.error(new Error('getAuthorization callback params missing "SecurityToken"'));if(!e.ExpiredTime)return l.error(new Error('getAuthorization callback params missing "ExpiredTime"'));if(e.ExpiredTime&&10!==e.ExpiredTime.toString().length)return l.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));if(e.StartTime&&10!==e.StartTime.toString().length)return l.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'))}return!1};if(p.ExpiredTime&&p.ExpiredTime-l.getSkewTime(a.options.SystemClockOffset)/1e3>60)_();else if(a.options.getAuthorization)a.options.getAuthorization.call(a,{Bucket:c,Region:d,Method:e.Method,Key:u,Pathname:h,Query:e.Query,Headers:i,Scope:f,SystemClockOffset:a.options.SystemClockOffset,ForceSignHost:n},(function(e){"string"===typeof e&&(e={Authorization:e});var t=v(e);if(t)return o(t);e.Authorization?o(null,e):(p=e||{},p.Scope=f,p.ScopeKey=y,a._StsCache.push(p),_())}));else{if(!a.options.getSTS)return function(){var t="";if(a.options.StartTime&&e.Expires){if(10!==a.options.StartTime.toString().length)return o(l.error(new Error('params "StartTime" should be 10 digits')));t=a.options.StartTime+";"+(a.options.StartTime+1*e.Expires)}else if(a.options.StartTime&&a.options.ExpiredTime){if(10!==a.options.StartTime.toString().length)return o(l.error(new Error('params "StartTime" should be 10 digits')));if(10!==a.options.ExpiredTime.toString().length)return o(l.error(new Error('params "ExpiredTime" should be 10 digits')));t=a.options.StartTime+";"+1*a.options.ExpiredTime}var r=l.getAuth({SecretId:e.SecretId||a.options.SecretId,SecretKey:e.SecretKey||a.options.SecretKey,Method:e.Method,Pathname:h,Query:e.Query,Headers:i,Expires:e.Expires,KeyTime:t,UseRawKey:a.options.UseRawKey,SystemClockOffset:a.options.SystemClockOffset,ForceSignHost:n}),s={Authorization:r,SecurityToken:a.options.SecurityToken||a.options.XCosSecurityToken,SignFrom:"client"};return o(null,s),s}();a.options.getSTS.call(a,{Bucket:c,Region:d},(function(e){p=e||{},p.Scope=f,p.ScopeKey=y,p.TmpSecretId||(p.TmpSecretId=p.SecretId),p.TmpSecretKey||(p.TmpSecretKey=p.SecretKey);var t=v(p);if(t)return o(t);a._StsCache.push(p),_()}))}return""}function Le(e){var t=this,i=!1,r=!1,n=!1,s=e.headers&&(e.headers.date||e.headers.Date)||e.error&&e.error.ServerTime;try{var o=e.error.Code,a=e.error.Message;("RequestTimeTooSkewed"===o||"AccessDenied"===o&&"Request has expired"===a)&&(r=!0)}catch(d){}if(e)if(r&&s){var c=Date.parse(s);this.options.CorrectClockSkew&&Math.abs(l.getSkewTime(this.options.SystemClockOffset)-c)>=3e4&&(console.error("error: Local time is too skewed."),this.options.SystemClockOffset=c-Date.now(),i=!0)}else 5===Math.floor(e.statusCode/100)?(i=!0,n=!1):("timeout"===e.message||"CORS blocked or network error"===e.message)&&(i=!0,n=t.options.AutoSwitchHost);return{canRetry:i,networkError:n}}function ze(e){var t=e.requestUrl,i=e.clientCalcSign,r=e.networkError;if(!this.options.AutoSwitchHost)return!1;if(!t)return!1;if(!i)return!1;if(!r)return!1;var n=/^https?:\/\/[^\/]*\.cos\.[^\/]*\.myqcloud\.com(\/.*)?$/,s=/^https?:\/\/[^\/]*\.cos\.accelerate\.myqcloud\.com(\/.*)?$/,o=n.test(t)&&!s.test(t);return o}function De(e,t){var i=this;!e.headers&&(e.headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=l.clearKey(e.qs),e.headers&&(e.headers=l.clearKey(e.headers)),e.qs&&(e.qs=l.clearKey(e.qs));var r=l.clone(e.qs);e.action&&(r[e.action]="");var n=e.url||e.Url,s=e.SignHost||Ie.call(this,{Bucket:e.Bucket,Region:e.Region,Url:n}),a=e.tracker,c=function(n){var l=i.options.SystemClockOffset;a&&a.setParams({signStartTime:(new Date).getTime(),httpRetryTimes:n-1}),e.SwitchHost&&(s=s.replace(/myqcloud.com/,"tencentcos.cn"));var d=o(o({},e),{},{Query:r,SignHost:s,ForceSignHost:i.options.ForceSignHost});delete d.tracker,i.logger.debug({cate:"PROCESS",tag:"base",msg:"开始计算签名, opt=".concat(JSON.stringify(d))}),Oe.call(i,{Bucket:e.Bucket||"",Region:e.Region||"",Method:e.method,Key:e.Key,Query:r,Headers:e.headers,SignHost:s,Action:e.Action,ResourceKey:e.ResourceKey,Scope:e.Scope,ForceSignHost:i.options.ForceSignHost,SwitchHost:e.SwitchHost},(function(r,s){if(r)return i.logger.error({cate:"PROCESS",tag:"base",msg:"签名获取失败, err=".concat(JSON.stringify(r.message))}),void t(r);a&&a.setParams({signEndTime:(new Date).getTime(),httpStartTime:(new Date).getTime()}),e.AuthData=s,i.logger.debug({cate:"PROCESS",tag:"base",msg:"签名获取成功"}),i.logger.info({cate:"PROCESS",tag:"base",msg:"准备发起请求"}),Fe.call(i,e,(function(r,o){a&&a.setParams({httpEndTime:(new Date).getTime()});var d=!1,u=!1;if(r){var h=Le.call(i,r);d=h.canRetry||l!==i.options.SystemClockOffset,u=h.networkError,i.logger.error({cate:"PROCESS",tag:"network",msg:"请求失败, err=".concat(JSON.stringify(r),", canRetry=").concat(d,", networkError=").concat(u,", tryTimes=").concat(n)})}if(r&&n<4&&d){e.headers&&(delete e.headers.Authorization,delete e.headers["token"],delete e.headers["clientIP"],delete e.headers["clientUA"],e.headers["x-cos-security-token"]&&delete e.headers["x-cos-security-token"],e.headers["x-ci-security-token"]&&delete e.headers["x-ci-security-token"]);var p=ze.call(i,{requestUrl:(null===r||void 0===r?void 0:r.url)||"",clientCalcSign:"client"===s.SignFrom,networkError:u});e.SwitchHost=p,e.headers["x-cos-sdk-retry"]=!0,i.logger.info({cate:"PROCESS",tag:"base",msg:"重试请求, 重试第".concat(n,"次")}),c(n+1)}else i.logger.info({cate:"PROCESS",tag:"base",msg:"请求完成"}),t(r,o)}))}))};c(1)}function Fe(e,t){var i=this,r=e.TaskId;if(!r||i._isRunningTask(r)){var n=e.Bucket,s=e.Region,o=e.Key,c=e.method||"GET",d=e.Url||e.url,u=e.body,h=e.rawBody;i.options.UseAccelerate&&(s="accelerate"),d=d||Re({ForcePathStyle:i.options.ForcePathStyle,protocol:i.options.Protocol,domain:i.options.Domain,bucket:n,region:s,object:o}),e.SwitchHost&&(d=d.replace(/myqcloud.com/,"tencentcos.cn"));var p=o?d:"";e.action&&(d=d+"?"+(l.isIOS_QQ?"".concat(e.action,"="):e.action)),e.qsStr&&(d=d.indexOf("?")>-1?d+"&"+e.qsStr:d+"?"+e.qsStr);var f={method:c,url:d,headers:e.headers,qs:e.qs,body:u},m="x-cos-security-token";if(l.isCIHost(d)&&(m="x-ci-security-token"),f.headers.Authorization=e.AuthData.Authorization,e.AuthData.Token&&(f.headers["token"]=e.AuthData.Token),e.AuthData.ClientIP&&(f.headers["clientIP"]=e.AuthData.ClientIP),e.AuthData.ClientUA&&(f.headers["clientUA"]=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(f.headers[m]=e.AuthData.SecurityToken),e.Action&&(f.action=e.Action),f.key=e.Key||e.ResourceKey,f.headers&&(f.headers=l.clearKey(f.headers)),f=l.clearKey(f),e.onProgress&&"function"===typeof e.onProgress){var g=u&&(u.size||u.length)||0;f.onProgress=function(t){if(!r||i._isRunningTask(r)){var n=t?t.loaded:0;e.onProgress({loaded:n,total:g})}}}e.onDownloadProgress&&(f.onDownloadProgress=e.onDownloadProgress),e.DataType&&(f.dataType=e.DataType),this.options.Timeout&&(f.timeout=this.options.Timeout),i.options.ForcePathStyle&&(f.pathStyle=i.options.ForcePathStyle);var y=l.uuid();i.logger.info({cate:"PROCESS",tag:"network",msg:"[Request] ".concat(y,", requestOpt=").concat(JSON.stringify(f))}),i.emit("before-send",f);var _,v=f.url.includes("accelerate."),b=f.qs?Object.keys(f.qs).map((function(e){return"".concat(e,"=").concat(f.qs[e])})).join("&"):"",S=b?f.url+"?"+b:f.url;if(e.tracker)e.tracker.setParams({url:S,httpMethod:f.method,accelerate:v,httpSize:(null===(_=f.body)||void 0===_?void 0:_.size)||0}),e.tracker.parent&&!e.tracker.parent.params.url&&e.tracker.parent.setParams({url:p,accelerate:v});var x=(i.options.Request||a)(f,(function(n){if(!n||"abort"!==n.error){var s={options:f,error:n&&n.error,statusCode:n&&n.statusCode||0,statusMessage:n&&n.statusMessage||"",headers:n&&n.headers||{},body:n&&n.body};i.emit("after-receive",s);var o,a=s.error,c=s.body,d={statusCode:s.statusCode,statusMessage:s.statusMessage,headers:s.headers},u=a?"[error]":"[success]";i.logger.info({cate:"PROCESS",tag:"network",msg:"[Response] ".concat(y,", ").concat(u,", response=").concat(JSON.stringify(d))});var p=function(n,s){if(r&&i.off("inner-kill-task",k),!o){o=!0;var a={};if(d&&d.statusCode&&(a.statusCode=d.statusCode),d&&d.headers&&(a.headers=d.headers),n)f.url&&(a.url=f.url),f.method&&(a.method=f.method),n=l.extend(n||{},a),t(n,null);else{if("name/cos:PutObject"===e.Action){var c={};for(var u in e.headers){var h=u.toLowerCase();c[h]=e.headers[u]}c["x-cos-callback"]?s.Error?(s.CallbackError=l.clone(s.Error),delete s.Error):s.CallbackBody=l.clone(s):c["x-cos-return-body"]&&(s.Error?(s.ReturnError=l.clone(s.Error),delete s.Error):s.ReturnBody=l.clone(s))}s=l.extend(s||{},a),t(null,s)}x=null}};if(a)return p(l.error(a));var m=d.statusCode,g=2===Math.floor(m/100);if(h){if(g)return p(null,{body:c});if(c instanceof Blob)return void l.readAsBinaryString(c,(function(e){var t=l.parseResBody(e),i=t.Error||t;return p(l.error(new Error(i.Message||"response body error"),{code:i.Code,error:i}))}))}var _=l.parseResBody(c),v=_.Error||_;g?p(null,_):v?p(l.error(new Error(v.Message),{code:v.Code,error:v})):m?p(l.error(new Error(d.statusMessage),{code:""+m})):m&&p(l.error(new Error("statusCode error")))}})),k=function(e){e.TaskId===r&&(x&&x.abort&&x.abort(),i.off("inner-kill-task",k))};r&&i.on("inner-kill-task",k)}}var Ne={getService:c,putBucket:d,headBucket:u,getBucket:h,deleteBucket:p,putBucketAcl:f,getBucketAcl:m,putBucketCors:g,getBucketCors:y,deleteBucketCors:_,getBucketLocation:v,getBucketPolicy:S,putBucketPolicy:b,deleteBucketPolicy:x,putBucketTagging:k,getBucketTagging:C,deleteBucketTagging:U,putBucketLifecycle:B,getBucketLifecycle:w,deleteBucketLifecycle:E,putBucketVersioning:T,getBucketVersioning:A,putBucketReplication:P,getBucketReplication:R,deleteBucketReplication:I,putBucketWebsite:O,getBucketWebsite:L,deleteBucketWebsite:z,putBucketReferer:D,getBucketReferer:F,putBucketDomain:N,getBucketDomain:M,deleteBucketDomain:j,putBucketOrigin:H,getBucketOrigin:G,deleteBucketOrigin:K,putBucketLogging:V,getBucketLogging:Y,putBucketInventory:W,postBucketInventory:X,getBucketInventory:Q,listBucketInventory:$,deleteBucketInventory:J,putBucketAccelerate:Z,getBucketAccelerate:ee,putBucketEncryption:te,getBucketEncryption:ie,deleteBucketEncryption:re,getObject:oe,headObject:ne,listObjectVersions:se,putObject:ae,deleteObject:le,getObjectAcl:ce,putObjectAcl:de,optionsObject:ue,putObjectCopy:he,deleteMultipleObject:fe,restoreObject:me,putObjectTagging:ge,getObjectTagging:ye,deleteObjectTagging:_e,selectObjectContent:ve,appendObject:we,uploadPartCopy:pe,multipartInit:be,multipartUpload:Se,multipartComplete:xe,multipartList:ke,multipartListPart:Ce,multipartAbort:Ue,request:Be,getObjectUrl:Te,getAuth:Ee};function Me(e,t,i){l.each(["Cors","Acl"],(function(r){if(e.slice(-r.length)===r){var n=e.slice(0,-r.length)+r.toUpperCase(),s=l.apiWrapper(e,t),o=!1;i[n]=function(){!o&&console.warn("warning: cos."+n+" has been deprecated. Please Use cos."+e+" instead."),o=!0,s.apply(this,arguments)}}}))}e.exports.init=function(e,t){t.transferToTaskMethod(Ne,"putObject"),l.each(Ne,(function(t,i){e.prototype[i]=l.apiWrapper(i,t),Me(i,t,e.prototype)}))}},"./src/cos.js":
/*!********************!*\
  !*** ./src/cos.js ***!
  \********************/
/*! no static exports found */function(e,t,i){"use strict";var r=i(/*! ./util */"./src/util.js"),n=i(/*! ./event */"./src/event.js"),s=i(/*! ./task */"./src/task.js"),o=i(/*! ./base */"./src/base.js"),a=i(/*! ./advance */"./src/advance.js"),l=i(/*! ./logger */"./src/logger.js"),c=i(/*! ../package.json */"./package.json"),d={AppId:"",SecretId:"",SecretKey:"",SecurityToken:"",StartTime:0,ExpiredTime:0,ChunkRetryTimes:2,FileParallelLimit:3,ChunkParallelLimit:3,ChunkSize:1048576,SliceSize:1048576,CopyChunkParallelLimit:20,CopyChunkSize:10485760,CopySliceSize:10485760,MaxPartNumber:1e4,ProgressInterval:1e3,Domain:"",ServiceDomain:"",Protocol:"",CompatibilityMode:!1,ForcePathStyle:!1,UseRawKey:!1,Timeout:0,CorrectClockSkew:!0,SystemClockOffset:0,UploadCheckContentMd5:!1,UploadQueueSize:1e4,UploadAddMetaMd5:!1,UploadIdCacheLimit:50,UseAccelerate:!1,ForceSignHost:!0,AutoSwitchHost:!0,CopySourceParser:null,ObjectKeySimplifyCheck:!0,DeepTracker:!1,TrackerDelay:5e3,CustomId:"",BeaconReporter:null,ClsReporter:null,EnableLog:!1,EnableLogcat:!1,LogLevel:"VERBOSE",ClsLogger:null,LogExtras:{}},u=function(e){var t,i,o=this;if(this.options=r.extend(r.clone(d),e||{}),this.options.FileParallelLimit=Math.max(1,this.options.FileParallelLimit),this.options.ChunkParallelLimit=Math.max(1,this.options.ChunkParallelLimit),this.options.ChunkRetryTimes=Math.max(0,this.options.ChunkRetryTimes),this.options.ChunkSize=Math.max(1048576,this.options.ChunkSize),this.options.CopyChunkParallelLimit=Math.max(1,this.options.CopyChunkParallelLimit),this.options.CopyChunkSize=Math.max(1048576,this.options.CopyChunkSize),this.options.CopySliceSize=Math.max(0,this.options.CopySliceSize),this.options.MaxPartNumber=Math.max(1024,Math.min(1e4,this.options.MaxPartNumber)),this.options.Timeout=Math.max(0,this.options.Timeout),this.options.EnableReporter=this.options.BeaconReporter||this.options.ClsReporter,this.options.AppId&&console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g: "test-1250000000").'),this.options.SecretId&&this.options.SecretId.indexOf(" ")>-1&&(console.error("error: SecretId格式错误，请检查"),console.error("error: SecretId format is incorrect. Please check")),this.options.SecretKey&&this.options.SecretKey.indexOf(" ")>-1&&(console.error("error: SecretKey格式错误，请检查"),console.error("error: SecretKey format is incorrect. Please check")),r.isNode()&&(console.log("Tip: Next.js、Nuxt.js 等服务端渲染技术可正常使用JavaScript SDK，请忽略下方 nodejs 环境警告"),console.warn("warning: cos-js-sdk-v5 不支持 nodejs 环境使用，请改用 cos-nodejs-sdk-v5，参考文档： https://cloud.tencent.com/document/product/436/8629"),console.warn("warning: cos-js-sdk-v5 does not support nodejs environment. Please use cos-nodejs-sdk-v5 instead. See: https://cloud.tencent.com/document/product/436/8629")),this.options.ForcePathStyle)throw console.warn("cos-js-sdk-v5不再支持使用path-style，仅支持使用virtual-hosted-style，参考文档：https://cloud.tencent.com/document/product/436/96243"),new Error("ForcePathStyle is not supported");n.init(this),s.init(this),this.logger=new l({enableLog:this.options.EnableLog,enableLogcat:this.options.EnableLogcat,level:null!==(t=this.options.LogLevel)&&void 0!==t?t:"VERBOSE",clsLogger:this.options.ClsLogger,logExtras:null!==(i=this.options.LogExtras)&&void 0!==i?i:{}}),this.options.EnableLog&&(n.init(this.logger),this.logger.on("log-message",(function(e){o.emit("log-message",e)})))};o.init(u,s),a.init(u,s),u.util={md5:r.md5,xml2json:r.xml2json,json2xml:r.json2xml,encodeBase64:r.encodeBase64},u.getAuthorization=r.getAuth,u.version=c.version,e.exports=u},"./src/event.js":
/*!**********************!*\
  !*** ./src/event.js ***!
  \**********************/
/*! no static exports found */function(e,t){var i=function(e){var t={},i=function(e){return!t[e]&&(t[e]=[]),t[e]};e.on=function(e,t){"task-list-update"===e&&console.warn('warning: Event "'+e+'" has been deprecated. Please use "list-update" instead.'),i(e).push(t)},e.off=function(e,t){for(var r=i(e),n=r.length-1;n>=0;n--)t===r[n]&&r.splice(n,1)},e.emit=function(e,t){for(var r=i(e).map((function(e){return e})),n=0;n<r.length;n++)r[n](t)}},r=function(){i(this)};e.exports.init=i,e.exports.EventProxy=r},"./src/logger.js":
/*!***********************!*\
  !*** ./src/logger.js ***!
  \***********************/
/*! no static exports found */function(e,t,i){var r=i(/*! @babel/runtime/helpers/classCallCheck */"./node_modules/@babel/runtime/helpers/classCallCheck.js"),n=i(/*! @babel/runtime/helpers/createClass */"./node_modules/@babel/runtime/helpers/createClass.js"),s=i(/*! @babel/runtime/helpers/defineProperty */"./node_modules/@babel/runtime/helpers/defineProperty.js"),o=i(/*! ../package.json */"./package.json"),a=o.version,l=["VERBOSE","DEBUG","INFO","WARN","ERROR"],c=function(){"use strict";function e(t){var i;r(this,e),s(this,"level","VERBOSE"),s(this,"clsLogger",null),s(this,"logExtras",{}),this.enableLog=null!==(i=t.enableLog)&&void 0!==i&&i,this.level=t.level||"VERBOSE",l.includes(this.level)||(this.level="VERBOSE"),this.enableLogcat=t.enableLogcat,this.clsLogger=t.clsLogger,this.logExtras=t.logExtras}return n(e,[{key:"info",value:function(){if(["VERBOSE","INFO"].includes(this.level)){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this.log.apply(this,["info"].concat(t))}}},{key:"debug",value:function(){if(["VERBOSE","DEBUG"].includes(this.level)){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this.log.apply(this,["debug"].concat(t))}}},{key:"warn",value:function(){if(["VERBOSE","WARN"].includes(this.level)){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this.log.apply(this,["warn"].concat(t))}}},{key:"error",value:function(){if(["VERBOSE","ERROR"].includes(this.level)){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];this.log.apply(this,["error"].concat(t))}}},{key:"log",value:function(){if(this.enableLog){var e=arguments.length<=0?void 0:arguments[0],t=arguments.length<=1?void 0:arguments[1],i=t.cate,r=void 0===i?"base":i,n=t.tag,s=void 0===n?"base":n,o=t.msg,l={version:"cos-js-sdk-v5-".concat(a),timestamp:(new Date).toISOString(),cate:"[".concat(r.toUpperCase(),"]"),tag:"[".concat(s.toUpperCase(),"]"),msg:o,extras:this.logExtras};this.enableLogcat&&console[e]("[".concat(l.version,"] ").concat(l.timestamp," ").concat(l.cate," ").concat(l.tag," ").concat(l.msg," ").concat(l.extras?JSON.stringify(l.extras):"")),this.clsLogger&&this.clsLogger.log(l,!1),this.emit("log-message",l)}}}])}();e.exports=c},"./src/session.js":
/*!************************!*\
  !*** ./src/session.js ***!
  \************************/
/*! no static exports found */function(e,t,i){var r,n,s=i(/*! ./util */"./src/util.js"),o="cos_sdk_upload_cache",a=2592e3,l=function(){try{var e=JSON.parse(localStorage.getItem(o))}catch(t){}e||(e=[]),r=e},c=function(){try{r.length?localStorage.setItem(o,JSON.stringify(r)):localStorage.removeItem(o)}catch(e){}},d=function(){if(!r){l.call(this);for(var e=!1,t=Math.round(Date.now()/1e3),i=r.length-1;i>=0;i--){var n=r[i][2];(!n||n+a<t)&&(r.splice(i,1),e=!0)}e&&c()}},u=function(){n||(n=setTimeout((function(){c(),n=null}),400))},h={using:{},setUsing:function(e){h.using[e]=!0},removeUsing:function(e){delete h.using[e]},getFileId:function(e,t,i,r){return e.name&&e.size&&e.lastModifiedDate&&t?s.md5([e.name,e.size,e.lastModifiedDate,t,i,r].join("::")):null},getCopyFileId:function(e,t,i,r,n){var o=t["content-length"],a=t.etag||"",l=t["last-modified"];return e&&i?s.md5([e,o,a,l,i,r,n].join("::")):null},getUploadIdList:function(e){if(!e)return null;d.call(this);for(var t=[],i=0;i<r.length;i++)r[i][0]===e&&t.push(r[i][1]);return t.length?t:null},saveUploadId:function(e,t,i){if(d.call(this),e){for(var n=r.length-1;n>=0;n--){var s=r[n];s[0]===e&&s[1]===t&&r.splice(n,1)}r.unshift([e,t,Math.round(Date.now()/1e3)]),r.length>i&&r.splice(i),u()}},removeUploadId:function(e){d.call(this),delete h.using[e];for(var t=r.length-1;t>=0;t--)r[t][1]===e&&r.splice(t,1);u()}};e.exports=h},"./src/task.js":
/*!*********************!*\
  !*** ./src/task.js ***!
  \*********************/
/*! no static exports found */function(e,t,i){var r=i(/*! ./session */"./src/session.js"),n=i(/*! ./util */"./src/util.js"),s={},o=function(e,t){s[t]=e[t],e[t]=function(e,i){e.SkipTask?s[t].call(this,e,i):this._addTask(t,e,i)}},a=function(e){var t=[],i={},o=0,a=0,l=function(e){var t={id:e.id,Bucket:e.Bucket,Region:e.Region,Key:e.Key,FilePath:e.FilePath,state:e.state,loaded:e.loaded,size:e.size,speed:e.speed,percent:e.percent,hashPercent:e.hashPercent,error:e.error};return e.FilePath&&(t.FilePath=e.FilePath),e._custom&&(t._custom=e._custom),t},c=function(){var i,r=function(){i=0,e.emit("task-list-update",{list:n.map(t,l)}),e.emit("list-update",{list:n.map(t,l)})};return function(){i||(i=setTimeout(r))}}(),d=function(){if(!(t.length<=e.options.UploadQueueSize)){for(var r=0;r<a&&r<t.length&&t.length>e.options.UploadQueueSize;){var n="waiting"===t[r].state||"checking"===t[r].state||"uploading"===t[r].state;t[r]&&n?r++:(i[t[r].id]&&delete i[t[r].id],t.splice(r,1),a--)}c()}},u=function(){if(!(o>=e.options.FileParallelLimit)){while(t[a]&&"waiting"!==t[a].state)a++;if(!(a>=t.length)){var i=t[a];a++,o++,i.state="checking",i.params.onTaskStart&&i.params.onTaskStart(l(i)),!i.params.UploadData&&(i.params.UploadData={});var r=n.formatParams(i.api,i.params);s[i.api].call(e,r,(function(t,r){e._isRunningTask(i.id)&&("checking"!==i.state&&"uploading"!==i.state||(i.state=t?"error":"success",t&&(i.error=t),o--,c(),u(),i.callback&&i.callback(t,r),"success"===i.state&&(i.params&&(delete i.params.UploadData,delete i.params.Body,delete i.params),delete i.callback)),d())})),c(),setTimeout(u)}}},h=function(t,n){var s=i[t];if(s){var a=s&&"waiting"===s.state,l=s&&("checking"===s.state||"uploading"===s.state);if("canceled"===n&&"canceled"!==s.state||"paused"===n&&a||"paused"===n&&l){s.state=n,e.emit("inner-kill-task",{TaskId:t,toState:n});try{var h=s&&s.params&&s.params.UploadData.UploadId}catch(p){}"canceled"===n&&h&&r.removeUsing(h),c(),l&&(o--,u()),"canceled"===n&&(s.params&&(delete s.params.UploadData,delete s.params.Body,delete s.params),delete s.callback)}d()}};e._addTasks=function(t){n.each(t,(function(t){e._addTask(t.api,t.params,t.callback,!0)})),c()};var p=!0;e._addTask=function(r,s,o,a){s=n.formatParams(r,s);var l=n.uuid();s.TaskId=l,s.onTaskReady&&s.onTaskReady(l),s.TaskReady&&(s.TaskReady(l),p&&console.warn('warning: Param "TaskReady" has been deprecated. Please use "onTaskReady" instead.'),p=!1);var h={params:s,callback:o,api:r,index:t.length,id:l,Bucket:s.Bucket,Region:s.Region,Key:s.Key,FilePath:s.FilePath||"",state:"waiting",loaded:0,size:0,speed:0,percent:0,hashPercent:0,error:null,_custom:s._custom},f=s.onHashProgress;s.onHashProgress=function(t){e._isRunningTask(h.id)&&(h.hashPercent=t.percent,f&&f(t),c())};var m=s.onProgress;return s.onProgress=function(t){e._isRunningTask(h.id)&&("checking"===h.state&&(h.state="uploading"),h.loaded=t.loaded,h.speed=t.speed,h.percent=t.percent,m&&m(t),c())},n.getFileSize(r,s,(function(e,r){if(e)return o(n.error(e));i[l]=h,t.push(h),h.size=r,!a&&c(),u(),d()})),l},e._isRunningTask=function(e){var t=i[e];return!(!t||"checking"!==t.state&&"uploading"!==t.state)},e.getTaskList=function(){return n.map(t,l)},e.cancelTask=function(e){h(e,"canceled")},e.pauseTask=function(e){h(e,"paused")},e.restartTask=function(e){var t=i[e];!t||"paused"!==t.state&&"error"!==t.state||(t.state="waiting",c(),a=Math.min(a,t.index),u())},e.isUploadRunning=function(){return o||a<t.length}};e.exports.transferToTaskMethod=o,e.exports.init=a},"./src/tracker.js":
/*!************************!*\
  !*** ./src/tracker.js ***!
  \************************/
/*! no static exports found */function(e,t,i){var r=i(/*! @babel/runtime/helpers/classCallCheck */"./node_modules/@babel/runtime/helpers/classCallCheck.js"),n=i(/*! @babel/runtime/helpers/createClass */"./node_modules/@babel/runtime/helpers/createClass.js"),s=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js"),o=i(/*! ../package.json */"./package.json"),a=null,l=function(e,t){if(!a){if("function"!==typeof e)throw new Error("Beacon not found");a=new e({appkey:"0WEB05PY6MHRGK0U",versionCode:o.version,channelID:"js_sdk",openid:"openid",unionid:"unid",strictMode:!1,delay:t,sessionDuration:6e4})}return a},c=function(e){return!e||e<0?0:(e/1e3).toFixed(3)},d={getUid:function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},getNetType:function(){if("object"===("undefined"===typeof navigator?"undefined":s(navigator))){var e=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return(null===e||void 0===e?void 0:e.type)||(null===e||void 0===e?void 0:e.effectiveType)||"unknown"}return"unknown"},getProtocol:function(){return"object"===("undefined"===typeof location?"undefined":s(location))?location.protocol.replace(/:/,""):"unknown protocol"},getOsType:function(){if("object"!==("undefined"===typeof navigator?"undefined":s(navigator)))return"unknown os";var e=navigator.userAgent.toLowerCase(),t=/macintosh|mac os x/i.test(navigator.userAgent);return e.indexOf("win32")>=0||e.indexOf("wow32")>=0?"win32":e.indexOf("win64")>=0||e.indexOf("wow64")>=0?"win64":t?"mac":"unknown os"},isMobile:function(){var e=/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;return!("object"!==("undefined"===typeof navigator?"undefined":s(navigator))||!navigator.userAgent.match(e))},isAndroid:function(){var e=/(Android|Adr|Linux)/i;return!("object"!==("undefined"===typeof navigator?"undefined":s(navigator))||!navigator.userAgent.match(e))},isIOS:function(){var e=/(iPhone|iPod|iPad|iOS)/i;return!("object"!==("undefined"===typeof navigator?"undefined":s(navigator))||!navigator.userAgent.match(e))},isOtherMobile:function(){return u&&!isAndroid&&!isIOS},getUA:function(){if("object"!==("undefined"===typeof navigator?"undefined":s(navigator)))return"unknown device";var e=navigator.userAgent;return e}},u=d.isMobile(),h=d.isAndroid()?"android":d.isIOS?"ios":"other_mobile",p=d.getOsType(),f=u?h:p,m=d.getUA(),g=d.getProtocol(),y=function(e){return["putObject","sliceUploadFile","uploadFile","uploadFiles"].includes(e)?"UploadTask":"getObject"===e?"DownloadTask":["putObjectCopy","sliceCopyFile"].includes(e)?"CopyTask":e};function _(e){return e.replace(/([A-Z])/g,"_$1").toLowerCase()}function v(e){var t={},i=["sdkVersionName","sdkVersionCode","osName","networkType","requestName","requestResult","bucket","region","appid","accelerate","url","host","requestPath","userAgent","networkProtocol","httpMethod","httpSize","httpSpeed","httpTookTime","httpMd5","httpSign","httpFullTime","httpDomain","partNumber","httpRetryTimes","customId","traceId","realApi"],r=[].concat(i,["errorNode","errorCode","errorName","errorMessage","errorRequestId","errorHttpCode","errorServiceName","errorType","fullError"]),n="Success"===e.requestResult?i:r;for(var s in e)if(n.includes(s)){var o=_(s);t[o]=e[s]}return t["request_name"]=e.realApi?y(e.realApi):e.requestName,t}var b=function(){"use strict";function e(t){r(this,e);var i=t.parent,n=t.traceId,s=t.bucket,a=t.region,c=t.apiName,u=t.realApi,h=t.httpMethod,p=t.fileKey,y=t.fileSize,_=t.accelerate,v=t.customId,b=t.delay,S=t.deepTracker,x=t.Beacon,k=t.clsReporter,C=s&&s.substr(s.lastIndexOf("-")+1)||"";this.parent=i,this.deepTracker=S,this.delay=b,k&&!this.clsReporter&&(this.clsReporter=k),this.params={sdkVersionName:"cos-js-sdk-v5",sdkVersionCode:o.version,osName:f,networkType:"",requestName:c||"",requestResult:"",realApi:u,bucket:s,region:a,accelerate:_,httpMethod:h,url:"",host:"",httpDomain:"",requestPath:p||"",userAgent:m,networkProtocol:g,errorType:"",errorCode:"",errorName:"",errorMessage:"",errorRequestId:"",errorHttpCode:0,errorServiceName:"",errorNode:"",httpTookTime:0,httpSize:y||0,httpMd5:0,httpSign:0,httpFullTime:0,httpSpeed:0,md5StartTime:0,md5EndTime:0,signStartTime:0,signEndTime:0,httpStartTime:0,httpEndTime:0,startTime:(new Date).getTime(),endTime:0,traceId:n||d.getUid(),appid:C,partNumber:0,httpRetryTimes:0,customId:v||"",partTime:0},x&&(this.beacon=l(x,b))}return n(e,[{key:"formatResult",value:function(e,t){var i,r,n,s,o,a,l=(new Date).getTime(),u=d.getNetType(),h=e?(null===e||void 0===e?void 0:e.code)||(null===e||void 0===e||null===(i=e.error)||void 0===i?void 0:i.code)||(null===e||void 0===e||null===(r=e.error)||void 0===r?void 0:r.Code):"",p=e?(null===e||void 0===e?void 0:e.message)||(null===e||void 0===e||null===(n=e.error)||void 0===n?void 0:n.message)||(null===e||void 0===e||null===(s=e.error)||void 0===s?void 0:s.Message):"",f=p,m=e?(null===e||void 0===e?void 0:e.resource)||(null===e||void 0===e||null===(o=e.error)||void 0===o?void 0:o.resource)||(null===e||void 0===e||null===(a=e.error)||void 0===a?void 0:a.Resource):"",g=e?null===e||void 0===e?void 0:e.statusCode:t.statusCode,y=e?(null===e||void 0===e?void 0:e.headers)&&(null===e||void 0===e?void 0:e.headers["x-cos-request-id"]):(null===t||void 0===t?void 0:t.headers)&&(null===t||void 0===t?void 0:t.headers["x-cos-request-id"]),_=e?y?"Server":"Client":"";"getObject"===this.params.requestName&&(this.params.httpSize=t?t.headers&&t.headers["content-length"]:0);var v="sliceUploadFile"===this.params.realApi,b="sliceCopyFile"===this.params.realApi;if(v||b){var S=this.params.httpSize/1024/this.params.partTime;Object.assign(this.params,{httpSpeed:S<0?0:S.toFixed(3)})}else{var x=l-this.params.startTime,k=this.params.httpEndTime-this.params.httpStartTime,C=this.params.httpSize/1024/(k/1e3),U=this.params.md5EndTime-this.params.md5StartTime,B=this.params.signEndTime-this.params.signStartTime;this.parent&&(this.parent.addParamValue("httpTookTime",c(k)),this.parent.addParamValue("httpFullTime",c(x)),this.parent.addParamValue("httpMd5",c(U)),this.parent.addParamValue("httpSign",c(B)),["multipartUpload","uploadPartCopy","putObjectCopy"].includes(this.params.requestName)&&this.parent.addParamValue("partTime",c(k))),Object.assign(this.params,{httpFullTime:c(x),httpMd5:c(U),httpSign:c(B),httpTookTime:c(k),httpSpeed:C<0?0:C.toFixed(3)})}if(Object.assign(this.params,{networkType:u,requestResult:e?"Failure":"Success",errorType:_,errorCode:h,errorHttpCode:g,errorName:f,errorMessage:p,errorServiceName:m,errorRequestId:y}),!e||h&&p||(this.params.fullError=e?JSON.stringify(e):""),this.params.url){try{var w=/^http(s)?:\/\/(.*?)\//.exec(this.params.url);this.params.host=w[2]}catch(E){this.params.host=this.params.url}this.params.httpDomain=this.params.host}}},{key:"report",value:function(e,t){if(this.beacon||this.clsReporter){this.formatResult(e,t);var i=v(this.params);this.beacon&&this.sendEventsToBeacon(i),this.clsReporter&&this.sendEventsToCLS(i)}}},{key:"setParams",value:function(e){Object.assign(this.params,e)}},{key:"addParamValue",value:function(e,t){this.params[e]=(+this.params[e]+ +t).toFixed(3)}},{key:"sendEventsToBeacon",value:function(e){var t="sliceUploadFile"===this.params.requestName||"sliceUploadFile"===this.params.realApi;if(!t||this.deepTracker){var i="qcloud_track_cos_sdk";0===this.delay?this.beacon&&this.beacon.onDirectUserAction(i,e):this.beacon&&this.beacon.onUserAction(i,e)}}},{key:"sendEventsToCLS",value:function(e){var t=!(0!==this.delay);this.clsReporter.log(e,t)}},{key:"generateSubTracker",value:function(t){return Object.assign(t,{parent:this,deepTracker:this.deepTracker,traceId:this.params.traceId,bucket:this.params.bucket,region:this.params.region,accelerate:this.params.accelerate,fileKey:this.params.requestPath,customId:this.params.customId,delay:this.delay,clsReporter:this.clsReporter}),new e(t)}}])}();e.exports=b},"./src/util.js":
/*!*********************!*\
  !*** ./src/util.js ***!
  \*********************/
/*! no static exports found */function(e,t,i){"use strict";(function(t){var r=i(/*! @babel/runtime/helpers/typeof */"./node_modules/@babel/runtime/helpers/typeof.js");function n(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=s(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var r=0,n=function(){};return{s:n,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,l=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return a=e.done,e},e:function(e){l=!0,o=e},f:function(){try{a||null==i.return||i.return()}finally{if(l)throw o}}}}function s(e,t){if(e){if("string"==typeof e)return o(e,t);var i={}.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,r=Array(t);i<t;i++)r[i]=e[i];return r}var a=i(/*! ../lib/md5 */"./lib/md5.js"),l=i(/*! ../lib/crypto */"./lib/crypto.js"),c=i(/*! fast-xml-parser */"./node_modules/fast-xml-parser/src/fxp.js"),d=c.XMLParser,u=c.XMLBuilder,h=new d({ignoreDeclaration:!0,ignoreAttributes:!0,parseTagValue:!1,trimValues:!1}),p=new u,f=i(/*! ../lib/base64 */"./lib/base64.js"),m=i(/*! ./tracker */"./src/tracker.js"),g="#text",y=function(e){if(H(e))for(var t in e){var i=e[t];"string"===typeof i?t===g&&delete e[t]:Array.isArray(i)?i.forEach((function(e){y(e)})):H(i)&&y(i)}},_=function(e){var t=h.parse(e);return y(t),t},v=function(e){var t=p.build(e);return t};function b(e){return encodeURIComponent(e).replace(/!/g,"%21").replace(/'/g,"%27").replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\*/g,"%2A")}function S(e,t){var i=[];for(var r in e)e.hasOwnProperty(r)&&i.push(t?b(r).toLowerCase():r);return i.sort((function(e,t){return e=e.toLowerCase(),t=t.toLowerCase(),e===t?0:e>t?1:-1}))}var x=function(e,t){var i,r,n,s=[],o=S(e);for(i=0;i<o.length;i++)r=o[i],n=void 0===e[r]||null===e[r]?"":""+e[r],r=t?b(r).toLowerCase():b(r),n=b(n)||"",s.push(r+"="+n);return s.join("&")},k=["cache-control","content-disposition","content-encoding","content-length","content-md5","expect","expires","host","if-match","if-modified-since","if-none-match","if-unmodified-since","origin","range","transfer-encoding","pic-operations"],C=function(e){var t={};for(var i in e){var r=i.toLowerCase();(r.indexOf("x-cos-")>-1||r.indexOf("x-ci-")>-1||k.indexOf(r)>-1)&&(t[i]=e[i])}return t},U=function(e){e=e||{};var t,i=e.SecretId,r=e.SecretKey,n=e.KeyTime,s=(e.method||e.Method||"get").toLowerCase(),o=F(e.Query||e.params||{}),a=C(F(e.Headers||e.headers||{})),c=e.Key||"";e.UseRawKey?t=e.Pathname||e.pathname||"/"+c:(t=e.Pathname||e.pathname||c,0!==t.indexOf("/")&&(t="/"+t));var d=!1!==e.ForceSignHost;if(!a.Host&&!a.host&&e.Bucket&&e.Region&&d&&(a.Host=e.Bucket+".cos."+e.Region+".myqcloud.com"),!i)throw new Error("missing param SecretId");if(!r)throw new Error("missing param SecretKey");var u=Math.round(te(e.SystemClockOffset)/1e3)-1,h=u,p=e.Expires||e.expires;h+=void 0===p?900:1*p||0;var f="sha1",m=i,g=n||u+";"+h,y=n||u+";"+h,_=S(a,!0).join(";").toLowerCase(),v=S(o,!0).join(";").toLowerCase(),b=l.HmacSHA1(y,r).toString(),x=[s,t,he.obj2str(o,!0),he.obj2str(a,!0),""].join("\n"),k=["sha1",g,l.SHA1(x).toString(),""].join("\n"),U=l.HmacSHA1(k,b).toString(),B=["q-sign-algorithm="+f,"q-ak="+m,"q-sign-time="+g,"q-key-time="+y,"q-header-list="+_,"q-url-param-list="+v,"q-signature="+U].join("&");return B},B=function(e,t,i){var r=t/8,n=e.slice(i,i+r);return new Uint8Array(n).reverse(),new{8:Uint8Array,16:Uint16Array,32:Uint32Array}[t](n)[0]},w=function(e,t,i,r){var n=e.slice(t,i),s="";return new Uint8Array(n).forEach((function(e){s+=String.fromCharCode(e)})),r&&(s=decodeURIComponent(escape(s))),s},E=function(e){var t={},i=w(e),r={records:[]};while(e.byteLength){var n,s=B(e,32,0),o=B(e,32,4),a=s-o-16,l=0;e=e.slice(12);while(l<o){var c=B(e,8,l),d=w(e,l+1,l+1+c),u=B(e,16,l+c+2),h=w(e,l+c+4,l+c+4+u);t[d]=h,l+=c+4+u}if("Records"===t[":event-type"])n=w(e,l,l+a,!0),r.records.push(n);else if("Stats"===t[":event-type"])n=w(e,l,l+a,!0),r.stats=he.xml2json(n).Stats;else if("error"===t[":event-type"]){var p=t[":error-code"],f=t[":error-message"],m=new Error(f);m.message=f,m.name=m.code=p,r.error=m}else["Progress","Continuation","End"].includes(t[":event-type"]);e=e.slice(l+a+4)}return{payload:r.records.join(""),body:i}},T=function(e){var t=this.options.CopySourceParser;if(t)return t(e);var i=e.match(/^([^.]+-\d+)\.cos(v6|-cdc|-cdz|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);return i?{Bucket:i[1],Region:i[3],Key:i[7]}:null},A=function(){},P=function(e){var t={};for(var i in e)e.hasOwnProperty(i)&&void 0!==e[i]&&null!==e[i]&&(t[i]=e[i]);return t},R=function(e,t){var i,r=new FileReader;FileReader.prototype.readAsBinaryString?(i=FileReader.prototype.readAsBinaryString,r.onload=function(){t(this.result)}):FileReader.prototype.readAsArrayBuffer?i=function(e){var i="",r=new FileReader;r.onload=function(e){for(var n=new Uint8Array(r.result),s=n.byteLength,o=0;o<s;o++)i+=String.fromCharCode(n[o]);t(i)},r.readAsArrayBuffer(e)}:console.error("FileReader not support readAsBinaryString"),i.call(r,e)},I=function(){var e=function(e,t){e=e.split("."),t=t.split(".");for(var i=0;i<t.length;i++)if(e[i]!==t[i])return parseInt(e[i])>parseInt(t[i])?1:-1;return 0},t=function(t){if(!t)return!1;var i=(t.match(/Chrome\/([.\d]+)/)||[])[1],r=(t.match(/QBCore\/([.\d]+)/)||[])[1],n=(t.match(/QQBrowser\/([.\d]+)/)||[])[1],s=i&&e(i,"53.0.2785.116")<0&&r&&e(r,"3.53.991.400")<0&&n&&e(n,"9.0.2524.400")<=0||!1;return s};return t("undefined"!==typeof navigator&&navigator.userAgent)}(),O=function(e,t,i,r,n){var s;if(e.slice?s=e.slice(t,i):e.mozSlice?s=e.mozSlice(t,i):e.webkitSlice&&(s=e.webkitSlice(t,i)),r&&I){var o=new FileReader;o.onload=function(e){s=null,n(new Blob([o.result]))},o.readAsArrayBuffer(s)}else n(s)},L=function(e,t,i,r){i=i||A,e?"string"===typeof t?i(he.md5(t,!0)):Blob&&t instanceof Blob?he.getFileMd5(t,(function(e,t){i(t)}),r):i():i()},z=1048576,D=function(e,t,i){var r=e.size,n=0,s=a.getCtx(),o=function(a){if(a>=r){var l=s.digest("hex");t(null,l)}else{var c=Math.min(r,a+z);he.fileSlice(e,a,c,!1,(function(e){R(e,(function(t){e=null,s=s.update(t,!0),n+=t.length,t=null,i&&i({loaded:n,total:r,percent:Math.round(n/r*1e4)/1e4}),o(a+z)}))}))}};o(0)};function F(e){return Y(e,(function(e){return"object"===r(e)&&null!==e?F(e):e}))}function N(e,t,i){return e&&t in e?e[t]:i}function M(e,t){return V(t,(function(i,r){e[r]=t[r]})),e}function j(e){return e instanceof Array}function H(e){return"[object Object]"===Object.prototype.toString.call(e)}function G(e,t){for(var i=!1,r=0;r<e.length;r++)if(t===e[r]){i=!0;break}return i}function K(e){return j(e)?e:[e]}function V(e,t){for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)}function Y(e,t){var i=j(e)?[]:{};for(var r in e)e.hasOwnProperty(r)&&(i[r]=t(e[r],r));return i}function q(e,t){var i=j(e),r=i?[]:{};for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)&&(i?r.push(e[n]):r[n]=e[n]);return r}var W=function(e){var t,i,r,n="";for(t=0,i=e.length/2;t<i;t++)r=parseInt(e[2*t]+e[2*t+1],16),n+=String.fromCharCode(r);return btoa(n)},X=function(){var e=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)};return e()+e()+"-"+e()+"-"+e()+"-"+e()+"-"+e()+e()+e()},Q=function(e,t){var i=t.Bucket,r=t.Region,n=t.Key,s=this.options.Domain,o=!s||"string"===typeof s&&s.indexOf("{Bucket}")>-1,a=!s||"string"===typeof s&&s.indexOf("{Region}")>-1;if(e.indexOf("Bucket")>-1||"deleteMultipleObject"===e||"multipartList"===e||"listObjectVersions"===e){if(o&&!i)return"Bucket";if(a&&!r)return"Region"}else if(e.indexOf("Object")>-1||e.indexOf("multipart")>-1||"sliceUploadFile"===e||"abortUploadTask"===e||"uploadFile"===e){if(o&&!i)return"Bucket";if(a&&!r)return"Region";if(!n)return"Key"}return!1},$=function(e,t){if(t=M({},t),"getAuth"!==e&&"getV4Auth"!==e&&"getObjectUrl"!==e){var i=t.Headers||{};if(t&&"object"===r(t)){(function(){for(var e in t)t.hasOwnProperty(e)&&e.indexOf("x-cos-")>-1&&(i[e]=t[e])})();var n={"x-cos-mfa":"MFA","Content-MD5":"ContentMD5","Content-Length":"ContentLength","Content-Type":"ContentType",Expect:"Expect",Expires:"Expires","Cache-Control":"CacheControl","Content-Disposition":"ContentDisposition","Content-Encoding":"ContentEncoding",Range:"Range","If-Modified-Since":"IfModifiedSince","If-Unmodified-Since":"IfUnmodifiedSince","If-Match":"IfMatch","If-None-Match":"IfNoneMatch","x-cos-copy-source":"CopySource","x-cos-copy-source-Range":"CopySourceRange","x-cos-metadata-directive":"MetadataDirective","x-cos-copy-source-If-Modified-Since":"CopySourceIfModifiedSince","x-cos-copy-source-If-Unmodified-Since":"CopySourceIfUnmodifiedSince","x-cos-copy-source-If-Match":"CopySourceIfMatch","x-cos-copy-source-If-None-Match":"CopySourceIfNoneMatch","x-cos-acl":"ACL","x-cos-grant-read":"GrantRead","x-cos-grant-write":"GrantWrite","x-cos-grant-full-control":"GrantFullControl","x-cos-grant-read-acp":"GrantReadAcp","x-cos-grant-write-acp":"GrantWriteAcp","x-cos-storage-class":"StorageClass","x-cos-traffic-limit":"TrafficLimit","x-cos-mime-limit":"MimeLimit","x-cos-server-side-encryption-customer-algorithm":"SSECustomerAlgorithm","x-cos-server-side-encryption-customer-key":"SSECustomerKey","x-cos-server-side-encryption-customer-key-MD5":"SSECustomerKeyMD5","x-cos-server-side-encryption":"ServerSideEncryption","x-cos-server-side-encryption-cos-kms-key-id":"SSEKMSKeyId","x-cos-server-side-encryption-context":"SSEContext","Pic-Operations":"PicOperations","x-cos-callback":"Callback","x-cos-callback-var":"CallbackVar","x-cos-return-body":"ReturnBody"};he.each(n,(function(e,r){void 0!==t[e]&&(i[r]=t[e])})),t.Headers=P(i)}}return t},J=function(e,t){return function(i,r){var n,s=this;if("function"===typeof i&&(r=i,i={}),i=$(e,i),s.options.EnableReporter)if("sliceUploadFile"===i.calledBySdk||"sliceCopyFile"===i.calledBySdk)n=i.tracker&&i.tracker.generateSubTracker({apiName:e});else if(["uploadFile","uploadFiles"].includes(e))n=null;else{var o=0;i.Body&&(o="string"===typeof i.Body?i.Body.length:i.Body.size||i.Body.byteLength||0);var a=s.options.UseAccelerate||"string"===typeof s.options.Domain&&s.options.Domain.includes("accelerate.");n=new m({Beacon:s.options.BeaconReporter,clsReporter:s.options.ClsReporter,bucket:i.Bucket,region:i.Region,apiName:e,realApi:e,accelerate:a,fileKey:i.Key,fileSize:o,deepTracker:s.options.DeepTracker,customId:s.options.CustomId,delay:s.options.TrackerDelay})}i.tracker=n;var l=function(e){return e&&e.headers&&(e.headers["x-ci-request-id"]&&(e.RequestId=e.headers["x-ci-request-id"]),e.headers["x-cos-request-id"]&&(e.RequestId=e.headers["x-cos-request-id"]),e.headers["x-cos-version-id"]&&(e.VersionId=e.headers["x-cos-version-id"]),e.headers["x-cos-delete-marker"]&&(e.DeleteMarker=e.headers["x-cos-delete-marker"])),e},c=function(e,t){n&&n.report(e,t),r&&r(l(e),l(t))},d=function(){if("getService"!==e&&"abortUploadTask"!==e){var t=Q.call(s,e,i);if(t)return"missing param "+t;if(i.Region){if(s.options.CompatibilityMode){if(!/^([a-z\d-.]+)$/.test(i.Region))return"Region format error."}else{if(i.Region.indexOf("cos.")>-1)return'param Region should not be start with "cos."';if(!/^([a-z\d-]+)$/.test(i.Region))return"Region format error."}s.options.CompatibilityMode||-1!==i.Region.indexOf("-")||"yfb"===i.Region||"default"===i.Region||"accelerate"===i.Region||console.warn("warning: param Region format error, find help here: https://cloud.tencent.com/document/product/436/6224")}if(i.Bucket){if(!/^([a-z\d-]+)-(\d+)$/.test(i.Bucket))if(i.AppId)i.Bucket=i.Bucket+"-"+i.AppId;else{if(!s.options.AppId)return'Bucket should format as "test-1250000000".';i.Bucket=i.Bucket+"-"+s.options.AppId}i.AppId&&(console.warn('warning: AppId has been deprecated, Please put it at the end of parameter Bucket(E.g Bucket:"test-1250000000" ).'),delete i.AppId)}!s.options.UseRawKey&&i.Key&&"/"===i.Key.substr(0,1)&&(i.Key=i.Key.substr(1))}},u=d(),h=["getAuth","getObjectUrl"].includes(e);if("function"===typeof Promise&&!h&&!r)return new Promise((function(e,n){if(r=function(t,i){t?n(t):e(i)},u)return c(he.error(new Error(u)));t.call(s,i,c)}));if(u)return c(he.error(new Error(u)));var p=t.call(s,i,c);return h?p:void 0}},Z=function(e,t){var i,r,n=this,s=0,o=0,a=Date.now();function l(){if(r=0,t&&"function"===typeof t){i=Date.now();var n,l=Math.max(0,Math.round((o-s)/((i-a)/1e3)*100)/100)||0;n=0===o&&0===e?1:Math.floor(o/e*100)/100||0,a=i,s=o;try{t({loaded:o,total:e,speed:l,percent:n})}catch(c){}}}return function(t,i){if(t&&(o=t.loaded,e=t.total),i)clearTimeout(r),l();else{if(r)return;r=setTimeout(l,n.options.ProgressInterval)}}},ee=function(e,t,i){var r;"string"===typeof t.Body?t.Body=new Blob([t.Body],{type:"text/plain"}):t.Body instanceof ArrayBuffer&&(t.Body=new Blob([t.Body])),t.Body&&(t.Body instanceof Blob||"[object File]"===t.Body.toString()||"[object Blob]"===t.Body.toString())?(r=t.Body.size,t.ContentLength=r,i(null,r)):i(he.error(new Error("params body format error, Only allow File|Blob|String.")))},te=function(e){return Date.now()+(e||0)},ie=function(e,t){var i=e;return e.message=e.message||null,"string"===typeof t?(e.error=t,e.message=t):"object"===r(t)&&null!==t&&(M(e,t),(t.code||t.name)&&(e.code=t.code||t.name),t.message&&(e.message=t.message),t.stack&&(e.stack=t.stack)),"function"===typeof Object.defineProperty&&(Object.defineProperty(e,"name",{writable:!0,enumerable:!1}),Object.defineProperty(e,"message",{enumerable:!0})),e.name=t&&t.name||e.name||e.code||"Error",e.code||(e.code=e.name),e.error||(e.error=F(i)),e},re=function(){return"object"===("undefined"===typeof globalThis?"undefined":r(globalThis))&&("DedicatedWorkerGlobalScope"===globalThis.constructor.name||globalThis.FileReaderSync)},ne=function(){return"object"!==("undefined"===typeof window?"undefined":r(window))&&"object"===("undefined"===typeof t?"undefined":r(t))&&!re()},se=function(e){return/^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(e)},oe=function(){if("object"!==("undefined"===typeof navigator?"undefined":r(navigator)))return!1;var e=navigator.userAgent,t=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return t}(),ae=function(){return"object"===("undefined"===typeof navigator?"undefined":r(navigator))&&/\sQQ/i.test(navigator.userAgent)}(),le=function(e,t){var i=f.encode(e);return t&&(i=i.replaceAll("+","-").replaceAll("/","_").replaceAll("=","")),i},ce=function(e){return e?f.decode(e):""},de=function(e){var t,i=e.split("/"),r=[],s=n(i);try{for(s.s();!(t=s.n()).done;){var o=t.value;".."===o?r.length&&r.pop():o.length&&"."!==o&&r.push(o)}}catch(a){s.e(a)}finally{s.f()}return"/"+r.join("/")},ue=function(e){var t;if(e&&"string"===typeof e){var i=e.trim(),r=0===i.indexOf("<"),n=0===i.indexOf("{");if(r)t=he.xml2json(e)||{};else if(n)try{var s=e.replace(/\n/g," "),o=JSON.parse(s);t="[object Object]"===Object.prototype.toString.call(o)?o:e}catch(a){t=e}else t=e}else t=e||{};return t},he={noop:A,formatParams:$,apiWrapper:J,xml2json:_,json2xml:v,md5:a,clearKey:P,fileSlice:O,getBodyMd5:L,getFileMd5:D,b64:W,extend:M,isArray:j,isInArray:G,makeArray:K,each:V,map:Y,filter:q,clone:F,attr:N,uuid:X,camSafeUrlEncode:b,throttleOnProgress:Z,getFileSize:ee,getSkewTime:te,error:ie,obj2str:x,getAuth:U,parseSelectPayload:E,getSourceParams:T,isBrowser:!0,isNode:ne,isCIHost:se,isIOS_QQ:oe&&ae,encodeBase64:le,decodeBase64:ce,simplifyPath:de,readAsBinaryString:R,parseResBody:ue};e.exports=he}).call(this,i(/*! ./../node_modules/process/browser.js */"./node_modules/process/browser.js"))}})}))},5320:function(e,t,i){"use strict";e.exports=function(e){try{var t=new Set,i={size:0,has:function(){return!0},keys:function(){return Object.defineProperty({},"next",{get:function(){return t.clear(),t.add(4),function(){return{done:!0}}}})}},r=t[e](i);return 1!==r.size||4!==r.values().next().value}catch(n){return!1}}},5388:function(e,t,i){"use strict";var r=i("c65b");e.exports=function(e,t,i){var n,s,o=i?e:e.iterator,a=e.next;while(!(n=r(a,o)).done)if(s=t(n.value),void 0!==s)return s}},"68df":function(e,t,i){"use strict";var r=i("dc19"),n=i("8e16"),s=i("384f"),o=i("7f65");e.exports=function(e){var t=r(this),i=o(e);return!(n(t)>i.size)&&!1!==s(t,(function(e){if(!i.includes(e))return!1}),!0)}},"72c3":function(e,t,i){"use strict";var r=i("23e7"),n=i("e9bc"),s=i("5320"),o=i("dad2"),a=!o("union")||!s("union");r({target:"Set",proto:!0,real:!0,forced:a},{union:n})},"79a4":function(e,t,i){"use strict";var r=i("23e7"),n=i("d039"),s=i("953b"),o=i("dad2"),a=!o("intersection",(function(e){return 2===e.size&&e.has(1)&&e.has(2)}))||n((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}));r({target:"Set",proto:!0,real:!0,forced:a},{intersection:s})},"7f65":function(e,t,i){"use strict";var r=i("59ed"),n=i("825a"),s=i("c65b"),o=i("5926"),a=i("46c4"),l="Invalid size",c=RangeError,d=TypeError,u=Math.max,h=function(e,t){this.set=e,this.size=u(t,0),this.has=r(e.has),this.keys=r(e.keys)};h.prototype={getIterator:function(){return a(n(s(this.keys,this.set)))},includes:function(e){return s(this.has,this.set,e)}},e.exports=function(e){n(e);var t=+e.size;if(t!==t)throw new d(l);var i=o(t);if(i<0)throw new c(l);return new h(e,i)}},"83b9e":function(e,t,i){"use strict";var r=i("cb27"),n=i("384f"),s=r.Set,o=r.add;e.exports=function(e){var t=new s;return n(e,(function(e){o(t,e)})),t}},"8b00":function(e,t,i){"use strict";var r=i("23e7"),n=i("68df"),s=i("dad2"),o=!s("isSubsetOf",(function(e){return e}));r({target:"Set",proto:!0,real:!0,forced:o},{isSubsetOf:n})},"8e16":function(e,t,i){"use strict";var r=i("7282"),n=i("cb27");e.exports=r(n.proto,"size","get")||function(e){return e.size}},"953b":function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27"),s=i("8e16"),o=i("7f65"),a=i("384f"),l=i("5388"),c=n.Set,d=n.add,u=n.has;e.exports=function(e){var t=r(this),i=o(e),n=new c;return s(t)>i.size?l(i.getIterator(),(function(e){u(t,e)&&d(n,e)})):a(t,(function(e){i.includes(e)&&d(n,e)})),n}},9961:function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27"),s=i("83b9e"),o=i("7f65"),a=i("5388"),l=n.add,c=n.has,d=n.remove;e.exports=function(e){var t=r(this),i=o(e).getIterator(),n=s(t);return a(i,(function(e){c(t,e)?d(n,e):l(n,e)})),n}},a4e7:function(e,t,i){"use strict";var r=i("23e7"),n=i("395e"),s=i("dad2"),o=!s("isSupersetOf",(function(e){return!e}));r({target:"Set",proto:!0,real:!0,forced:o},{isSupersetOf:n})},a5f7:function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27"),s=i("83b9e"),o=i("8e16"),a=i("7f65"),l=i("384f"),c=i("5388"),d=n.has,u=n.remove;e.exports=function(e){var t=r(this),i=a(e),n=s(t);return o(t)<=i.size?l(t,(function(e){i.includes(e)&&u(n,e)})):c(i.getIterator(),(function(e){d(n,e)&&u(n,e)})),n}},a732:function(e,t,i){"use strict";var r=i("23e7"),n=i("c65b"),s=i("2266"),o=i("59ed"),a=i("825a"),l=i("46c4"),c=i("2a62"),d=i("f99f"),u=d("some",TypeError);r({target:"Iterator",proto:!0,real:!0,forced:u},{some:function(e){a(this);try{o(e)}catch(r){c(this,"throw",r)}if(u)return n(u,this,e);var t=l(this),i=0;return s(t,(function(t,r){if(e(t,i++))return r()}),{IS_RECORD:!0,INTERRUPTED:!0}).stopped}})},b4bc:function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27").has,s=i("8e16"),o=i("7f65"),a=i("384f"),l=i("5388"),c=i("2a62");e.exports=function(e){var t=r(this),i=o(e);if(s(t)<=i.size)return!1!==a(t,(function(e){if(i.includes(e))return!1}),!0);var d=i.getIterator();return!1!==l(d,(function(e){if(n(t,e))return c(d,"normal",!1)}))}},c1a1:function(e,t,i){"use strict";var r=i("23e7"),n=i("b4bc"),s=i("dad2"),o=!s("isDisjointFrom",(function(e){return!e}));r({target:"Set",proto:!0,real:!0,forced:o},{isDisjointFrom:n})},cb27:function(e,t,i){"use strict";var r=i("e330"),n=Set.prototype;e.exports={Set:Set,add:r(n.add),has:r(n.has),remove:r(n["delete"]),proto:n}},dad2:function(e,t,i){"use strict";var r=i("d066"),n=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}},s=function(e){return{size:e,has:function(){return!0},keys:function(){throw new Error("e")}}};e.exports=function(e,t){var i=r("Set");try{(new i)[e](n(0));try{return(new i)[e](n(-1)),!1}catch(a){if(!t)return!0;try{return(new i)[e](s(-1/0)),!1}catch(l){var o=new i;return o.add(1),o.add(2),t(o[e](s(1/0)))}}}catch(l){return!1}}},dc19:function(e,t,i){"use strict";var r=i("cb27").has;e.exports=function(e){return r(e),e}},e9bc:function(e,t,i){"use strict";var r=i("dc19"),n=i("cb27").add,s=i("83b9e"),o=i("7f65"),a=i("5388");e.exports=function(e){var t=r(this),i=o(e).getIterator(),l=s(t);return a(i,(function(e){n(l,e)})),l}}}]);