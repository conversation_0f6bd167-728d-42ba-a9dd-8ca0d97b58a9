<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCategoryMapper">
    <resultMap type="org.scrm.domain.material.entity.WeCategory" id="WeCategoryResult">
        <result property="id" column="id"/>
        <result property="mediaType" column="media_type"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="flag" column="flag"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="categoryList" resultMap="WeCategoryResult">
        select wc.id,
        wc.media_type,
        wc.name,
        wc.parent_id,
        wc.flag,
        wc.del_flag,
        wc.create_by,
        wc.create_by_id,
        wc.create_time,
        wc.update_by,
        wc.update_by_id,
        wc.update_time
        from we_category wc
        <where>
            and wc.del_flag = 0
            <if test="mediaType != null and mediaType != ''">
              and wc.media_type = #{mediaType}
            </if>
        </where>
        order by wc.create_time desc
    </select>
</mapper>
