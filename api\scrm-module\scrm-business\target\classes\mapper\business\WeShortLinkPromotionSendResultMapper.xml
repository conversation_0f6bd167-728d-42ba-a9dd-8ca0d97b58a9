<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeShortLinkPromotionSendResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.WeShortLinkPromotionSendResult">
        <id column="id" property="id"/>
        <result column="template_type" property="templateType"/>
        <result column="template_id" property="templateId"/>
        <result column="user_promotion_id" property="userPromotionId"/>
        <result column="user_id" property="userId"/>
        <result column="external_userid" property="externalUserid"/>
        <result column="chat_id" property="chatId"/>
        <result column="status" property="status"/>
        <result column="send_time" property="sendTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , template_type, template_id, user_id, external_userid, chat_id, status, send_time, create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag,user_promotion_id
    </sql>

</mapper>
