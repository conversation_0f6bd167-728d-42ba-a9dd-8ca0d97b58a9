(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-42c1e14c"],{"7f4b":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"header"},[e("span",{staticClass:"title"},[t._v(" 社群关系 ")]),e("van-icon",{attrs:{name:"cross",color:"#9c9c9c",size:"16"},on:{click:function(e){return t.$router.back()}}})],1),e("div",{staticClass:"labelPge"},[e("van-tabs",{attrs:{sticky:"",color:"#2C8CF0","line-height":"1.4px","title-active-color":"#2C8CF0"},model:{value:t.active,callback:function(e){t.active=e},expression:"active"}},[e("van-tab",{attrs:{title:"添加的员工("+t.staff.length+")"}},[e("van-list",[t.staff.length?e("van-cell-group",t._l(t.staff,(function(n,a){return e("van-cell",{key:a},[e("div",{staticClass:"li"},[e("div",{staticClass:"name"},[e("span",[t._v(t._s(n.userName))])]),e("div",{staticClass:"c9"},[e("span",[t._v("添加时间：")]),e("span",[t._v(t._s(t.getTime(n.createTime)))])])])])})),1):e("van-empty",{attrs:{"image-size":"50",description:"暂无数据"}})],1)],1),e("van-tab",{attrs:{title:"添加的群聊("+t.groupChat.length+")"}},[e("div",{staticClass:"ar"},[e("div",{staticClass:"common mar10",class:t.commonActive&&"active",on:{click:t.conGroup}},[t._v(" 只看与我共同的群聊 ")])]),e("van-list",[t.groupChat.length?e("van-cell-group",t._l(t.groupChat,(function(n,a){return e("van-cell",{key:a},[e("div",{staticClass:"li"},[e("div",{staticClass:"fxbw aic name"},[e("div",{},[t._v(" "+t._s(n.groupName+"("+n.groupMemberNum+")")+" ")]),n.commonGroup?t._e():e("div",{staticClass:"common active"},[t._v(" 共同群聊 ")])]),e("div",{staticClass:"fxbw"},[e("div",[e("span",{staticClass:"c9"},[t._v("群主：")]),e("span",{staticClass:"c9"},[t._v(t._s(n.ownerName))])]),e("div",{staticClass:"c9"},[e("span",[t._v("入群时间：")]),e("span",[t._v(t._s(t.getTime(n.joinTime)))])])])])])})),1):e("van-empty",{attrs:{"image-size":"50",description:"暂无数据"}})],1)],1)],1)],1)])},r=[],i=(n("e9f5"),n("910d"),n("c2eb")),s=n("ed08"),c={data(){return{active:0,list:[],staff:[],allGroup:[],groupChat:[],commonGroup:[],commonActive:!1,externalUserid:"",type:"1"}},computed:{userId(){return this.$store.state.userId}},created(){this.externalUserid=this.$route.query.customerId,this.type=this.$route.query.type,2==this.type?this.active=1:3==this.type&&(this.active=1,this.conGroup()),this.findAddaddEmployes(),this.findAddGroupNum()},methods:{conGroup(){this.commonActive=!this.commonActive,this.groupChat=this.commonActive?this.commonGroup:this.allGroup},findAddaddEmployes(){this.$toast.loading(),Object(i["d"])(this.externalUserid).then(({data:t})=>{this.staff=t}).catch(t=>{console.log(t)}).finally(()=>{this.$toast.clear()})},findAddGroupNum(){Object(i["c"])({externalUserid:this.externalUserid,userId:this.userId}).then(({data:t})=>{this.groupChat=this.allGroup=t,this.commonGroup=t.filter(t=>1==t.groupMemberNum)}).catch(t=>{console.log(t)})},getTime(t){return Object(s["b"])(t,"yyyy-MM-dd hh:mm")}}},o=c,u=(n("d5c4"),n("2877")),d=Object(u["a"])(o,a,r,!1,null,"4bb88547",null);e["default"]=d.exports},abb6:function(t,e,n){},c2eb:function(t,e,n){"use strict";n.d(e,"g",(function(){return s})),n.d(e,"k",(function(){return c})),n.d(e,"f",(function(){return o})),n.d(e,"l",(function(){return u})),n.d(e,"d",(function(){return d})),n.d(e,"c",(function(){return l})),n.d(e,"e",(function(){return m})),n.d(e,"a",(function(){return p})),n.d(e,"b",(function(){return f})),n.d(e,"i",(function(){return h})),n.d(e,"h",(function(){return v})),n.d(e,"j",(function(){return b}));var a=n("b775");const r=window.sysConfig.services.wecom,i=r+"/portrait";function s(t){return Object(a["a"])({url:i+"/findWeCustomerInfo",params:t})}function c(t){return Object(a["a"])({url:i+"/updateWeCustomerInfo",method:"post",data:t})}function o(t){return Object(a["a"])({url:i+"/findAllTags",params:t})}function u(t){return Object(a["a"])({url:i+"/updateWeCustomerPorTraitTag",method:"post",data:t})}function d(t){return Object(a["a"])({url:i+"/findAddaddEmployes/"+t})}function l(t){return Object(a["a"])({url:i+"/findAddGroupNum",params:t})}function m(t){return Object(a["a"])({url:r+"/trajectory/findTrajectory",params:t})}function p(t){return Object(a["a"])({url:i+"/addOrEditWaitHandle",method:"post",data:t})}function f(t){return Object(a["a"])({url:i+"/addOrUpdatePersonTags",method:"post",data:t})}function h(t){return Object(a["a"])({url:i+"/synchMomentsInteracte/"+t,method:"get"})}function v(t){return Object(a["a"])({url:i+"/findSysFieldTemplate",params:t})}function b(t){return Object(a["a"])({url:i+"/updateRemarkCorpInfo",method:"post",data:t})}},d5c4:function(t,e,n){"use strict";n("abb6")}}]);