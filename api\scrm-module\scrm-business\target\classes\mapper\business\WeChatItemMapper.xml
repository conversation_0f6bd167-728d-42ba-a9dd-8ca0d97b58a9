<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeChatItemMapper">
    <insert id="addItem">
        INSERT INTO
        we_chat_item(id,side_id,material_id,create_by,create_time,update_by,update_time)
        VALUES
        <if test="items!=null and items.size()>0">
            <foreach collection="items" separator="," item="item" >
                (#{item.id},#{item.sideId},#{item.materialId},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime})
            </foreach>
        </if>
    </insert>
    <delete id="dropItem">
        DELETE FROM
        we_chat_item
        <where>
            side_id=#{sideId}
        </where>
    </delete>

    <select id="findChatItems" resultType="org.scrm.domain.side.vo.WeChatSideVo">
        SELECT
        wci.material_id,
        wm.material_name,
        wm.material_url,
        wm.content,
        wm.media_type,
        wm.cover_url,
        (
        SELECT
        COUNT(1)
        FROM
        we_chat_collection
        WHERE
        material_id = wci.material_id
        AND user_id = #{userId}
        ) collection,
        wci.create_time
        FROM
        we_chat_item wci
        LEFT JOIN we_material wm ON wci.material_id = wm.id
        LEFT JOIN we_category wc ON wm.category_id = wc.id
        <where>
            wci.side_id=#{sideId}
            <if test="keyword!=null and keyword!=''">
                <if test="mediaType!=null and mediaType=='0'.toString()">
                    AND wm.content LIKE CONCAT('%',#{keyword},'%')
                </if>
                <if test="mediaType!=null and mediaType=='1'.toString()">
                    AND wm.material_name LIKE CONCAT('%',#{keyword},'%')
                </if>
                <if test="mediaType!=null and mediaType=='2'.toString()">
                    AND wm.material_name LIKE CONCAT('%',#{keyword},'%')
                </if>
                <if test="mediaType!=null and mediaType=='3'.toString()">
                    AND wm.material_name LIKE CONCAT('%',#{keyword},'%')
                </if>
                <if test="mediaType!=null and mediaType=='4'.toString()">
                    AND wm.content LIKE CONCAT('%',#{keyword},'%')
                </if>
                <if test="mediaType!=null and mediaType=='5'.toString()">
                    AND wm.material_name LIKE CONCAT('%',#{keyword},'%')
                </if>
            </if>
        </where>
    </select>


</mapper>