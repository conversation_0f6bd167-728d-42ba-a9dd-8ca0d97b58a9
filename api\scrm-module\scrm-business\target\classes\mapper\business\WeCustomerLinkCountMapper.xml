<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCustomerLinkCountMapper">

    <resultMap id="BaseResultMap" type="org.scrm.domain.WeCustomerLinkCount">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="externalUserid" column="external_userid" jdbcType="VARCHAR"/>
            <result property="linkId" column="link_id" jdbcType="VARCHAR"/>
            <result property="userid" column="userid" jdbcType="VARCHAR"/>
            <result property="chatStatus" column="chat_status" jdbcType="TINYINT"/>
            <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
            <result property="state" column="state" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <select id="selectLinkCountTrend" resultType="org.scrm.domain.customer.vo.WeCustomerLinkCountTrendVo">
        SELECT
            sd.date,
            (
                SELECT
                    COUNT( DISTINCT wcl.external_userid )
                FROM
                    we_customer_link_count wcl
                WHERE
                    wcl.link_id = #{linkId} AND
                DATE ( wcl.add_time )= sd.date
                AND wcl.del_flag = 0
                ) addWeCustomerNumber,
            (
        SELECT
            COUNT( DISTINCT wcl.external_userid )
        FROM
            we_customer_link_count wcl
        WHERE
            wcl.link_id = #{linkId} AND
            DATE ( wcl.add_time )= sd.date
          AND wcl.del_flag = 0
          AND wcl.chat_status = 1
            ) AS weCustomerActiveNumber
        FROM
            sys_dim_date sd
        WHERE
            DATE_FORMAT( sd.date, '%Y-%m-%d' ) BETWEEN #{beginTime} AND #{endTime}
        ORDER BY sd.date ASC
    </select>

    <select id="selectLinkCountTable" resultType="org.scrm.domain.customer.vo.WeCustomerLinkCountTableVo">
        SELECT
         sd.date,
        (
            SELECT
            COUNT( DISTINCT wcl.external_userid )
            FROM
            we_customer_link_count wcl
            WHERE
            wcl.link_id = #{linkId} AND
            DATE ( wcl.add_time )= sd.date
            AND wcl.del_flag = 0
        ) tdAddWeCustomerNumber,
        (
            SELECT
            COUNT( DISTINCT wcl.external_userid )
            FROM
            we_customer_link_count wcl
            WHERE
            wcl.link_id = #{linkId} AND
            DATE ( wcl.add_time )= sd.date
            AND wcl.del_flag = 0
            AND wcl.chat_status = 1
        ) AS tdWeCustomerActiveNumber,
        (
            SELECT
            COUNT( DISTINCT wcl.external_userid )
            FROM
            we_customer_link_count wcl
            WHERE
            wcl.link_id = #{linkId} AND
            DATE ( wcl.add_time ) &lt;= sd.date
            AND wcl.del_flag = 0
            AND wcl.chat_status = 1
        ) AS weCustomerActiveNumber,
        (
            SELECT
            COUNT( DISTINCT wcl.external_userid )
            FROM
            we_customer_link_count wcl
            WHERE
            wcl.link_id = #{linkId} AND
            DATE ( wcl.add_time ) &lt;= sd.date
            AND wcl.del_flag = 0
        ) addWeCustomerNumber
        FROM
        sys_dim_date sd
        WHERE
        DATE_FORMAT( sd.date, '%Y-%m-%d' ) BETWEEN #{beginTime} AND #{endTime}
        ORDER BY sd.date ASC
    </select>



    <insert id="batchAddOrUpdate">
        INSERT INTO we_customer_link_count(
            id,
            external_userid,
            link_id,
            we_user_id,
            customer_name,
            customer_type,
            gender,
            avatar,
            user_name,
            chat_status,
            add_time,
            state,
            create_by,
            create_by_id,
            create_time,
            update_by,
            update_by_id,
            update_time
        ) values
        <foreach collection="weCustomerLinkCounts" item="item" index="index" separator=",">
            (#{item.id},#{item.externalUserid},#{item.linkId},#{item.weUserId},#{item.customerName},#{item.customerType},
            #{item.gender},#{item.avatar},#{item.userName},#{item.chatStatus},#{item.addTime},#{item.state},
            #{item.createBy},#{item.createById},now(),#{item.updateBy},#{item.updateById},now()
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        link_id=IFNULL(VALUES(link_id),we_customer_link_count.link_id),
        customer_name=IFNULL(VALUES(customer_name),we_customer_link_count.customer_name),
        customer_type=IFNULL(VALUES(customer_type),we_customer_link_count.customer_type),
        gender=IFNULL(VALUES(gender),we_customer_link_count.gender),
        avatar=IFNULL(VALUES(avatar),we_customer_link_count.avatar),
        user_name=IFNULL(VALUES(user_name),we_customer_link_count.user_name),
        chat_status=IFNULL(VALUES(chat_status),we_customer_link_count.chat_status),
        add_time=IFNULL(VALUES(add_time),we_customer_link_count.add_time),
        state=IFNULL(VALUES(state),we_customer_link_count.state),
        update_by=IFNULL(VALUES(update_by),we_customer_link_count.update_by),
        update_by_id=IFNULL(VALUES(update_by_id),we_customer_link_count.update_by_id),
        update_time=IFNULL(VALUES(update_time),we_customer_link_count.update_time);

    </insert>
</mapper>
