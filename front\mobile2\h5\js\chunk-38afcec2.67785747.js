(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-38afcec2"],{"227e":function(e,t,i){"use strict";i.r(t);var a=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[e.code&&e.code.codeUrl?t("div",{staticClass:"code-wrapper"},[t("div",{staticClass:"header"},[e._v(" "+e._s(e.code.activityName)+" ")]),t("div",{staticClass:"welcome"},[e._v(" "+e._s(e.code.guide)+" ")]),e.code.groupName?t("div",{staticClass:"code-name"},[e._v(" "+e._s(e.code.groupName)+" ")]):e._e(),t("div",{staticClass:"code-image"},[t("van-image",{attrs:{width:"300",src:e.code.codeUrl}})],1),"1"===e.code.isOpenTip?t("div",{staticClass:"service"},[t("van-button",{attrs:{type:"danger"},on:{click:function(t){e.dialog=!0}}},[e._v("无法加群?")])],1):e._e(),t("van-dialog",{staticClass:"service-dialog",attrs:{showConfirmButton:!1,closeOnClickOverlay:!0},model:{value:e.dialog,callback:function(t){e.dialog=t},expression:"dialog"}},[t("div",[t("van-image",{attrs:{src:e.code.serviceQrCode,width:"200"}}),t("div",{staticClass:"service-message"},[e._v(" "+e._s(e.code.tipMsg)+" ")]),t("div",{staticClass:"tip"},[e._v(" 请长按二维码添加客服微信 ")])],1),t("van-icon",{staticClass:"close",attrs:{name:"cross"},on:{click:function(t){e.dialog=!1}}})],1)],1):t("div",[t("van-empty",{attrs:{description:e.message}})],1)])},s=[],o=i("4e79"),c={data(){return{loading:!1,code:{activityName:"",guide:"",codeUrl:"",isOpenTip:"",tipMsg:"",serviceQrCode:"",groupName:""},dialog:!1,message:"抱歉，暂无可用实际群码"}},created(){this.getDetail()},methods:{getDetail(){let e=this.$route.query.id;e&&(this.loading=!0,"newCustomerGroup"===this.$route.query.type?Object(o["d"])(e).then(({data:e,msg:t})=>{this.code=Object.assign(e,{activityName:e.codeName,guide:e.welcomeMsg,codeUrl:e.groupCodeUrl}),document.title=e.activityName,this.loading=!1,t&&(this.message=t)}):"oldCustomerGroup"===this.$route.query.type?Object(o["e"])(e).then(({data:e,msg:t})=>{this.code=Object.assign(e,{activityName:e.taskName,guide:e.welcomeMsg,codeUrl:e.groupCodeUrl}),document.title=e.activityName,this.loading=!1,t&&(this.message=t)}):Object(o["c"])(e).then(({data:e,msg:t})=>{this.code=Object.assign(this.code,e),document.title=e.activityName,this.loading=!1,t&&(this.message=t)}).catch(()=>{}))}}},n=c,d=(i("9e1c"),i("2877")),r=Object(d["a"])(n,a,s,!1,null,"8f93f550",null);t["default"]=r.exports},"4e79":function(e,t,i){"use strict";i.d(t,"c",(function(){return o})),i.d(t,"d",(function(){return c})),i.d(t,"e",(function(){return n})),i.d(t,"b",(function(){return d})),i.d(t,"a",(function(){return r}));var a=i("b775");const s=window.sysConfig.services.weChat;function o(e){return Object(a["a"])({url:s+"/groupCode/getActualCode/"+e})}function c(e){return Object(a["a"])({url:s+"/groupCode/findWeCommunityNewGroupById/"+e})}function n(e){return Object(a["a"])({url:s+"/groupCode/findWePresTagGroupById/"+e})}function d(e){return Object(a["a"])({url:s+"/storeCode/findStoreCode",params:e})}function r(e){return Object(a["a"])({url:s+"/storeCode/findWeStoreCodeConfig",params:e})}},"9e1c":function(e,t,i){"use strict";i("f643")},f643:function(e,t,i){}}]);