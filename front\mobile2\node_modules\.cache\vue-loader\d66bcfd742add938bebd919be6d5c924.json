{"remainingRequest": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\project\\scrm\\front\\mobile2\\src\\views\\Applications\\communityOperations\\swarmsSOP\\swarmsSOP.vue", "mtime": 1753236965456}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751130701171}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751130691202}, {"path": "D:\\project\\scrm\\front\\mobile2\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751130703090}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}