<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeLeadsImportRecordMapper">

    <select id="importRecord" resultType="org.scrm.domain.leads.leads.vo.WeLeadsImportRecordVO">
        SELECT t1.id,
        t1.sea_id,
        t2.`name` as seaName,
        t1.import_source_file_name,
        t1.success_num as num,
        t1.create_time as importTime
        FROM we_leads_import_record t1
        LEFT JOIN we_leads_sea t2 on t1.sea_id = t2.id
        <where>
            <if test="name!=null and name!=''">
                AND t1.import_source_file_name like CONCAT('%',#{name},'%')
            </if>
        </where>
        ORDER BY t1.create_time DESC
    </select>
</mapper>
