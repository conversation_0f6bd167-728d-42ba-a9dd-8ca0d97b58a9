<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeStrategicCrowdCustomerRelMapper">



    <resultMap type="org.scrm.domain.WeStrategicCrowdCustomerRel" id="WeStrategicCrowdCustomerRelResult">
                <result property="id" column="id" jdbcType="INTEGER"/>
                <result property="crowdId" column="crowd_id" jdbcType="INTEGER"/>
                <result property="customerId" column="customer_id" jdbcType="INTEGER"/>
                <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
                <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
                <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
                <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
                <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
                <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
                <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
            </resultMap>

    <sql id="selectWeStrategicCrowdCustomerRelVo">
        select id,  crowd_id, customer_id, create_by, create_by_id, create_time, update_by, update_by_id, update_time, del_flag from we_strategic_crowd_customer_rel
    </sql>

</mapper>
