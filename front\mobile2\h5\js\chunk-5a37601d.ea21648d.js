(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5a37601d"],{"0925":function(t,a,i){"use strict";i("acf4")},"23ba":function(t,a,i){"use strict";i.d(a,"c",(function(){return r})),i.d(a,"e",(function(){return u})),i.d(a,"g",(function(){return o})),i.d(a,"h",(function(){return c})),i.d(a,"a",(function(){return d})),i.d(a,"f",(function(){return l})),i.d(a,"b",(function(){return p})),i.d(a,"d",(function(){return g}));var e=i("b775");const n=window.sysConfig.services.wecom,s=n+"/groupchat";function r(t){return Object(e["a"])({url:s+"/get/"+t})}function u(t){return Object(e["a"])({url:s+"/member/page/list",params:t})}function o(t){return Object(e["a"])({url:s+"/makeGroupTag",method:"post",data:t})}function c(){return Object(e["a"])({url:s+"/synch"})}function d({pageNum:t,pageSize:a,chatId:i}){return Object(e["a"])({url:s+"/findGroupTrajectory/"+i,params:{pageNum:t,pageSize:a}})}function l(t){return Object(e["a"])({url:n+"/tag/list",params:t})}function p(t){return Object(e["a"])({url:n+"/customer/findWeCustomerListByApp",params:t})}function g(t){return Object(e["a"])({url:n+"/groupchat/page/listByApp",params:t})}},5657:function(t,a,i){"use strict";i.r(a);var e=function(){var t=this,a=t._self._c;return a("div",{staticClass:"apps"},[a("div",{staticClass:"apps-bg"}),a("div",{staticClass:"apps-content"},[a("van-list",{attrs:{finished:t.finished,"finished-text":"暂无更多数据","loading-text":"上划加载更多"},on:{load:t.onLoad},model:{value:t.loading,callback:function(a){t.loading=a},expression:"loading"}},t._l(t.dataList,(function(i,e){return a("div",{key:e,staticClass:"list",on:{click:function(a){return t.gotoDetail(i.externalUserid)}}},[a("img",{staticClass:"avatar-img",attrs:{src:i.avatar,alt:""}}),a("div",{staticClass:"list-text"},[a("div",{staticClass:"text-top"},[a("span",[t._v(t._s(i.customerName))]),1===i.gender?a("svg-icon",{staticClass:"man-icon",attrs:{name:"man"}}):t._e(),2===i.gender?a("svg-icon",{staticClass:"man-icon",attrs:{name:"woman"}}):t._e()],1),a("div",{staticClass:"text-bottom"},[a("span",[t._v("流失时间："+t._s(i.updateTime))])])])])})),0)],1)])},n=[],s=(i("14d9"),i("23ba")),r={data(){return{loading:!1,finished:!1,query:{name:"",tagIds:"",customerType:null,trackState:5,pageNum:1,pageSize:10,delFlag:0,dataScope:!1},dataList:[],total:0}},methods:{onLoad(){this.total>=this.dataList.length?(this.query.pageNum++,this.getList()):this.finished=!0},getList(t){t&&(this.query.pageNum=t),this.loading=!0,this.finished=!1,Object(s["b"])(this.query).then(t=>{200===t.code&&(this.query.pageNum>1?this.dataList=[...this.dataList,...t.rows]:this.dataList=t.rows,this.total=Number(t.total),this.total<=this.dataList.length&&(this.finished=!0)),this.loading=!1})},gotoDetail(t){this.$router.push({name:"portrait",query:{id:t}})}},created(){this.getList()}},u=r,o=(i("0925"),i("2877")),c=Object(o["a"])(u,e,n,!1,null,"9a474232",null);a["default"]=c.exports},acf4:function(t,a,i){}}]);