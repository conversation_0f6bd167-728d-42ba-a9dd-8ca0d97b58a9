<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMomentsUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.moments.entity.WeMomentsUser">
        <id column="id" property="id"/>
        <result column="moment_task_id" property="momentsTaskId"/>
        <result column="moments_id" property="momentsId"/>
        <result column="user_id" property="userId"/>
        <result column="we_user_id" property="weUserId"/>
        <result column="user_name" property="userName"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="execute_count" property="executeCount"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="create_by_id" property="createById"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        moment_task_id,
        moments_id,
        user_id,
        we_user_id,
        user_name,
        dept_id,
        dept_name,
        execute_status,
        execute_count,
        create_by,
        update_by,
        update_by_id,
        create_by_id,
        create_time,
        update_time,
        del_flag
    </sql>


    <resultMap id="MobileResult" type="org.scrm.domain.moments.vo.WeMomentsTaskMobileVO">
        <result column="id" property="weMomentsTaskId"/>
        <result column="moments_id" property="momentsId"/>
        <result column="name" property="name"/>
        <result column="create_by" property="createBy"/>
        <result column="execute_time" property="executeTime"/>
        <result column="execute_end_time" property="executeEndTime"/>
        <result column="content" property="content"/>
        <result column="customer_tag" property="customerTag"/>
        <result column="execute_status" property="executeStatus"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="mobileList" resultMap="MobileResult">
        SELECT ifnull(t4.execute_status, 0) AS execute_status,
               t4.moments_id,
               t3.id,
               t3.NAME,
               t3.create_by,
               t3.execute_time,
               t3.execute_end_time,
               t3.content,
               t3.customer_tag,
               t3.STATUS
        FROM we_moments_task t3
                 JOIN (
            SELECT t1.moments_task_id,
                   t1.we_user_id,
                   t1.execute_status,
                   t2.moments_id
            FROM we_moments_estimate_user t1
                     LEFT JOIN we_moments_user t2 ON t1.moments_task_id = t2.moments_task_id
                AND t1.we_user_id = t2.we_user_id
        ) AS t4 ON t3.id = t4.moments_task_id
        WHERE t3.del_flag = 0
          AND t3.send_type = 2
          AND t3.status = #{status}
          AND t4.we_user_id = #{weUserId}
        ORDER BY t3.create_time DESC
    </select>


    <select id="mobileGet" resultType="org.scrm.domain.moments.vo.WeMomentsTaskMobileVO">
        SELECT ifnull(t4.execute_status, 0) AS execute_status,
               t3.id as weMomentsTaskId,
               t3.NAME,
               t3.create_by,
               t3.execute_time,
               t3.execute_end_time,
               t3.content,
               t3.customer_tag,
               t3.STATUS
        FROM we_moments_task t3
                 JOIN (
            SELECT t1.moments_task_id,
                   t1.we_user_id,
                   t1.execute_status,
                   t2.moments_id
            FROM we_moments_estimate_user t1
                     LEFT JOIN we_moments_user t2 ON t1.moments_task_id = t2.moments_task_id
                AND t1.we_user_id = t2.we_user_id
        ) AS t4 ON t3.id = t4.moments_task_id
        WHERE t3.del_flag = 0
          AND t4.we_user_id = #{weUserId}
          AND t3.id = #{weMomentsTaskId}
          GROUP BY t4.we_user_id
    </select>


    <select id="count" resultType="java.lang.Integer">
        SELECT count(t3.id)
        FROM we_moments_task t3
                 JOIN (
            SELECT t1.moments_task_id,
                   t1.we_user_id,
                   t2.execute_status
            FROM we_moments_estimate_user t1
                     LEFT JOIN we_moments_user t2 ON t1.moments_task_id = t2.moments_task_id
                AND t1.we_user_id = t2.we_user_id
        ) AS t4 ON t3.id = t4.moments_task_id
        WHERE t3.del_flag = 0
          AND t3.send_type = 2
          AND t3.status = #{status}
          AND t4.we_user_id = #{weUserId}
    </select>


    <select id="getExecuteUsers" resultType="org.scrm.domain.moments.vo.WeMomentsUserVO">
        SELECT t1.id,
               t1.moments_task_id,
               t1.user_id,
               t1.we_user_id as weUserId,
               t1.user_name as userName,
               t1.dept_id as deptId,
               GROUP_CONCAT(DISTINCT d.dept_name) as deptName,
               t1.execute_count as executeCount,
               t1.execute_status as executeStatus
        FROM we_moments_user t1
                 LEFT JOIN sys_user su on su.user_id=t1.user_id
                 LEFT JOIN sys_user_dept sud ON sud.user_id=su.user_id and sud.del_flag=0
                 left join sys_dept d on sud.dept_id = d.dept_id
        WHERE t1.moments_task_id=#{weMomentsTaskId}
        <if test="weUserIds!=null and weUserIds !=''">
            and t1.we_user_id in
            <foreach collection="weUserIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds !=''">
            and d.dept_id in
            <foreach collection="deptIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
            <choose>
                <when test="status == 0">
                    AND (t1.execute_status = #{status} or t1.execute_status is null)
                </when>
                <when test="status == 1">
                    AND t1.execute_status = #{status}
                </when>
            </choose>
        </if>
        GROUP BY t1.we_user_id
    </select>


</mapper>
