<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeQrScopeMapper">



    <resultMap type="org.scrm.domain.qr.WeQrScope" id="WeQrScopeResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
        <result property="scopeId" column="scope_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="scopeType" column="scope_type" jdbcType="INTEGER"/>
        <result property="party" column="party" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="workCycle" column="work_cycle" jdbcType="VARCHAR"/>
        <result property="beginTime" column="begin_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="QrScopeResult" type="org.scrm.domain.qr.vo.WeQrScopeVo">
        <result property="qrId" column="qr_id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="scopeId" column="scope_id" jdbcType="VARCHAR"/>
        <result property="beginTime" column="begin_time" jdbcType="VARCHAR"/>
        <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
        <result property="workCycle" column="work_cycle" jdbcType="VARCHAR"/>
        <collection property="weQrUserList" ofType="org.scrm.domain.qr.vo.WeQrScopeUserVo">
            <result property="userId" column="user_id" jdbcType="VARCHAR"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="schedulingNum" column="scheduling_num" jdbcType="INTEGER"/>
            <result property="isSpareUser" column="is_spare_user" jdbcType="INTEGER"/>
        </collection>
        <collection property="weQrPartyList" ofType="org.scrm.domain.qr.vo.WeQrScopePartyVo">
            <result property="party" column="party" jdbcType="VARCHAR"/>
            <result property="partyName" column="party_name" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="selectWeQrScopeVo">
        select id, qr_id, scope_id, type, scope_type, party, user_id, work_cycle, begin_time, end_time, create_by, create_time, update_by, update_time, del_flag from we_qr_scope
    </sql>

    <update id="updateScopeStatusByQrId">
        update we_qr_scope set status = (case when scope_id = #{scopeId} then 1 else 0 end) where qr_id = #{qrId}
    </update>

    <select id="getWeQrScopeByQrIds" resultMap="QrScopeResult">
        select
        wqs.qr_id,
        wqs.scope_id,
        wqs.scope_type,
        wqs.`type`,
        wqs.begin_time,
        wqs.end_time,
        wqs.work_cycle,
        wqs.user_id,
        if(wqs.user_id is null,null,wu.user_name) as user_name,
        wqs.scheduling_num,
        wqs.is_spare_user,
        wqs.party,
        if(wqs.party is null,null,wd.dept_name) as party_name
        from we_qr_scope wqs
        left join sys_user wu on wu.we_user_id = wqs.user_id and wu.del_flag = 0
        left join sys_dept wd on wd.dept_id = wqs.party and wd.del_flag = 0
        <where>
            <if test="qrIds != null and qrIds.size > 0">
                and wqs.qr_id in
                <foreach item="qrId" collection="qrIds" index="index" open="(" separator="," close=")">
                    #{qrId}
                </foreach>
            </if>
            and wqs.del_flag = 0
        </where>

    </select>

    <select id="getWeQrScopeByTime" resultMap="QrScopeResult">
        select
        wqs.qr_id,
        wqs.scope_id,
        wqs.scope_type,
        wqs.`type`,
        wqs.begin_time,
        wqs.end_time,
        wqs.work_cycle,
        wqs.user_id,
        wqs.party
        from we_qr_scope wqs
        inner join we_qr_code wqc on wqc.id = wqs.qr_id and wqc.del_flag = 0
        where  find_in_set(dayofweek(curdate())-1,wqs.work_cycle)
        and date_format(concat(date_format(curdate(),'%Y-%m-%d '),wqs.begin_time), '%H:%i:%s') &lt;= date_format(#{formatTime},'%H:%i:%s')
        and date_format(concat(date_format(curdate(),'%Y-%m-%d '),wqs.end_time), '%H:%i:%s') &gt;= date_format(#{formatTime},'%H:%i:%s')
        <if test="qrId != null and qrId != ''">
            and wqs.qr_id = #{qrId}
        </if>
        and wqc.rule_type = 2
        and wqs.del_flag = 0
    </select>

<!--    <select id="findLoopUser" resultType="org.scrm.domain.qr.WeQrScope">-->
<!--        SELECT-->
<!--        wqs.*-->
<!--        FROM-->
<!--        we_qr_scope wqs-->
<!--        LEFT JOIN we_qr_code wqc ON wqc.id = wqs.qr_id-->
<!--        AND wqs.del_flag = 0-->
<!--        WHERE-->
<!--        wqc.del_flag = 0-->
<!--        <choose>-->
<!--            <when test="loopNumber != null and loopNumber !=0">-->
<!--                AND (wqs.scheduling_num * #{loopNumber}) >= (-->
<!--                SELECT-->
<!--                count(*)-->
<!--                FROM-->
<!--                we_customer wc-->
<!--                WHERE-->
<!--                wc.add_user_id = wqs.user_id and wc.state=wqc.state-->
<!--                AND date_format(wc.add_time, '%Y-%m-%d') = CURDATE()-->
<!--                )-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                AND wqs.scheduling_num >= (-->
<!--                SELECT-->
<!--                count(*)-->
<!--                FROM-->
<!--                we_customer wc-->
<!--                WHERE-->
<!--                wc.add_user_id = wqs.user_id and wc.state=wqc.state-->
<!--                AND date_format( wc.add_time, '%Y-%m-%d' ) = CURDATE()-->
<!--                )-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--        AND wqc.id = #{qrId} and wqs.is_spare_user=0-->
<!--    </select>-->

    <select id="findLoopUser" resultType="org.scrm.domain.qr.WeQrScope">
        SELECT
        wqs.*,
        (SELECT
             count(*)
         FROM
             we_customer wc
         WHERE
             wc.add_user_id = wqs.user_id and wc.state=wqc.state
           AND date_format( wc.add_time, '%Y-%m-%d' ) = CURDATE()) as tdAddCustomerNumber
        FROM
        we_qr_scope wqs
        LEFT JOIN we_qr_code wqc ON wqc.id = wqs.qr_id
        AND wqs.del_flag = 0
        WHERE
        wqc.del_flag = 0
        AND wqc.id = #{qrId} and wqs.is_spare_user=0
    </select>

</mapper>
