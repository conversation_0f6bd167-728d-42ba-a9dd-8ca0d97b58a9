<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupMemberMapper">

    <insert id="insertBatch">
        insert into we_group_member (id,chat_id, user_id, union_id, join_time, join_scene, `type`,
        group_nick_name,`name`,invitor_user_id,
        create_by, create_time,update_by, update_time,del_flag,create_by_id, update_by_id,state,owner)
        values
        <foreach collection="weGroupMembers" item="weGroupMember" index="index" separator=",">
            (#{weGroupMember.id},#{weGroupMember.chatId},#{weGroupMember.userId},#{weGroupMember.unionId},
            #{weGroupMember.joinTime},#{weGroupMember.joinScene},#{weGroupMember.type},
            #{weGroupMember.groupNickName},#{weGroupMember.name},#{weGroupMember.invitorUserId},
            #{weGroupMember.createBy},now(),
            #{weGroupMember.updateBy},now(),#{weGroupMember.delFlag},#{weGroupMember.createById},#{weGroupMember.updateById},
             #{weGroupMember.state}, #{weGroupMember.owner})
        </foreach>
        on duplicate key update
        join_time= values(join_time),join_scene= values(join_scene),
        group_nick_name= values(group_nick_name),`name`= values(`name`),invitor_user_id= values(invitor_user_id),
        update_time= now(),
        owner= values(owner),
        union_id=values(union_id),
        update_by= values(update_by),
        update_by_id= values(update_by_id),
        state= values(state),del_flag= values(del_flag),
        `type`= values(`type`);
    </insert>


    <select id="getPageList" resultType="org.scrm.domain.WeGroupMember">
       SELECT
            a.user_id,
            a.join_time,
            a.type,
            a.customerType,
            a.join_scene,
            a.name,
            a.group_nick_name,
            a.invitor_user_id,
            a.invitor_user_name,
            a.avatar
        FROM
       (
        select
            wgm.user_id,
            wgm.join_time,
            if(wgm.user_id=wg.`owner`,3,wgm.type) as type,
            wgm.join_scene,
            wgm.name,
            wgm.group_nick_name,
            wgm.invitor_user_id,
            (select su.user_name from sys_user su where su.we_user_id = wgm.invitor_user_id and su.del_flag = 0 limit 1)  as invitor_user_name,
            if(wgm.type = 1,
            (SELECT sur.avatar FROM sys_user sur WHERE sur.we_user_id = wgm.user_id limit 1),
            (SELECT wc.avatar FROM we_customer wc WHERE wc.external_userid = wgm.user_id limit 1))  AS avatar,
            if(wgm.type=1,2,1) AS customerType
        from
        we_group_member wgm
        LEFT JOIN we_group wg on wg.chat_id=wgm.chat_id and wg.del_flag = 0
        where wgm.del_flag =0
            <if test="chatId != null and chatId != ''">
                and wgm.chat_id = #{chatId}
            </if>
            <if test="name != null  and name != ''">
                AND wgm.name like concat('%', #{name}, '%')
            </if>
            GROUP BY wgm.user_id
        ) a
    </select>

    <select id="selectGroupMemberListByChatId" resultType="org.scrm.domain.groupchat.vo.WeGroupMemberVo">
        SELECT
        distinct
        wgm.chat_id,
        wgm.join_time,
        wgm.type,
        wgm.join_scene,
        wgm.name,
        (case when su.avatar is null then wc.avatar else su.avatar end) as avatar
        FROM
        we_group_member wgm
        left join sys_user su on su.we_user_id = wgm.user_id and wgm.type = 1
        left join we_customer wc on wc.external_userid = wgm.user_id and wgm.type = 2
        <where>
            and wgm.chat_id = #{chatId}
        </where>
    </select>

    <select id="selectGroupMemberListByChatIds"
            resultType="org.scrm.domain.groupchat.vo.WeGroupMemberVo">
        SELECT
        distinct
        wgm.chat_id,
        wgm.join_time,
        wgm.type,
        wgm.join_scene,
        wgm.name,
        (case when su.avatar is null then wc.avatar else su.avatar end) as avatar
        FROM
        we_group_member wgm
        left join sys_user su on su.we_user_id = wgm.user_id and wgm.type = 1
        left join we_customer wc on wc.external_userid = wgm.user_id and wgm.type = 2
        <where>
            <if test="chatIds != null and chatIds.size > 0">
                and wgm.chat_id in
                <foreach item="chatId" collection="chatIds" index="index" open="(" separator="," close=")">
                    #{chatId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getMemberNumByState" resultType="org.scrm.domain.groupchat.vo.WeGroupChannelCountVo">
        select
        date_format(join_time,'%Y-%m-%d') as `date`,
        count(distinct user_id) as member_number,
        count(distinct case when del_flag = 0 then user_id end) as  efficient_number
        from we_group_member
        where id in (select id
        from we_group_member where state=#{state}
        <if test="startTime != null and endTime !=null "><!-- 开始时间检索 -->
            AND  date_format(join_time,'%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        )
        group by join_time
    </select>

    <update id="quitGroup">
        UPDATE we_group_member
        SET quit_scene = #{quitScene},
            quit_time = now(),
            update_time = now(),
            del_flag = 1
        WHERE
            user_id = #{userId}
          AND chat_id = #{chatId}
          and del_flag = 0
    </update>

    <delete id="physicalDelete">
        DELETE FROM we_group_member where chat_id=#{chatId} AND user_id=#{userId}
    </delete>

    <select id="findWeCustomerDeduplication" resultType="org.scrm.domain.groupchat.vo.WeCustomerDeduplicationVo">
        SELECT
            wc.external_userid,
            wc.customer_name,
            wc.avatar,
            wc.gender,
            wc.customer_type,
            wc.is_join_blacklist,
            wc.update_time,
            GROUP_CONCAT(DISTINCT su.we_user_id) AS weUserIds,
            GROUP_CONCAT(DISTINCT su.user_name) AS addWeUserNames,
            GROUP_CONCAT(DISTINCT wg.group_name) AS joinGroupNames,
            count(DISTINCT wg.chat_id) as groupNumber
        FROM
            we_group_member wgm
                LEFT JOIN we_customer wc ON wgm.user_id = wc.external_userid
                LEFT JOIN sys_user su ON su.we_user_id = wc.add_user_id
                LEFT JOIN we_group wg ON wg.chat_id = wgm.chat_id
        WHERE
            wc.external_userid IS NOT NULL and wgm.quit_scene is NULL
        <if test="customerName != null and customerName !=''">
           and  wc.customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        GROUP BY
            wc.external_userid
        HAVING
            groupNumber > 1
        ORDER BY wc.update_time DESC
    </select>


</mapper>
