CREATE TABLE `we_phone_call_record` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                        `sop_base_id` varchar(64) NOT NULL COMMENT 'SOP基础ID',
                                        `execute_target_id` varchar(64) DEFAULT NULL COMMENT 'SOP执行目标ID',
                                        `external_userid` varchar(64) NOT NULL COMMENT '客户外部ID',
                                        `customer_name` varchar(100) DEFAULT NULL COMMENT '客户姓名',
                                        `customer_phone` varchar(30) NOT NULL COMMENT '客户电话号码',
                                        `we_user_id` varchar(64) NOT NULL COMMENT '执行员工企微ID',
                                        `call_method` varchar(20) DEFAULT 'unknown' COMMENT '拨打方式(mobile/unknown)',
                                        `remark` varchar(500) DEFAULT NULL COMMENT '备注',
                                        `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
                                        `create_by_id` bigint DEFAULT NULL COMMENT '创建人id',
                                        `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
                                        `update_by_id` bigint DEFAULT NULL COMMENT '更新人id',
                                        `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                        `del_flag` tinyint DEFAULT '0' COMMENT '删除标志(0:正常,1:删除)',
                                        PRIMARY KEY (`id`),
                                        -- 唯一索引选项（根据需要选择其中一种）：
                                        -- 选项1：包含时间段ID的唯一索引（推荐，支持多时间段且防止重复）
                                        UNIQUE KEY `uk_sop_customer_user_attach` (`sop_base_id`,`external_userid`,`we_user_id`,`execute_target_attach_id`)
                                        -- 选项2：如果不需要唯一性约束，可以完全删除上面这行
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='拨打电话记录表'
