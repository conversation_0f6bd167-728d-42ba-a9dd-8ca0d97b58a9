package org.scrm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.scrm.domain.phone.WePhoneCallRecord;
import org.scrm.mapper.WePhoneCallRecordMapper;
import org.scrm.service.IWePhoneCallRecordService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 拨打电话记录Service实现类
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
@Slf4j
@Service
public class WePhoneCallRecordServiceImpl extends ServiceImpl<WePhoneCallRecordMapper, WePhoneCallRecord> 
        implements IWePhoneCallRecordService {

    @Autowired
    private WePhoneCallRecordMapper phoneCallRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordPhoneCall(WePhoneCallRecord record) {
        try {
            // 脱敏处理电话号码用于日志记录
            String maskedPhone = record.getCustomerPhone();
            if (maskedPhone != null && maskedPhone.length() > 7) {
                maskedPhone = maskedPhone.substring(0, 3) + "****" + maskedPhone.substring(maskedPhone.length() - 4);
            }
            log.info("[PHONE_CALL_RECORD] 记录拨打电话: 客户={}, 电话={}, 员工={}, executeTargetAttachId={}, callMethod={}",
                    record.getCustomerName(), maskedPhone, record.getWeUserId(),
                    record.getExecuteTargetAttachId(), record.getCallMethod());

            // 检查是否已存在记录（必须包含executeTargetAttachId以区分不同时间段）
            if (record.getExecuteTargetAttachId() == null) {
                log.error("[PHONE_CALL_RECORD] executeTargetAttachId不能为空，无法区分SOP时间段");
                throw new IllegalArgumentException("executeTargetAttachId不能为空");
            }

            LambdaQueryWrapper<WePhoneCallRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WePhoneCallRecord::getSopBaseId, record.getSopBaseId())
                   .eq(WePhoneCallRecord::getExternalUserid, record.getExternalUserid())
                   .eq(WePhoneCallRecord::getWeUserId, record.getWeUserId());

            // 精确匹配executeTargetAttachId字段（用于区分不同时间段）
            wrapper.eq(WePhoneCallRecord::getExecuteTargetAttachId, record.getExecuteTargetAttachId());
            log.info("[PHONE_CALL_RECORD] 检查重复记录时包含executeTargetAttachId: {}", record.getExecuteTargetAttachId());

            WePhoneCallRecord existingRecord = this.getOne(wrapper);
            if (existingRecord != null) {
                log.warn("[PHONE_CALL_RECORD] 拨打记录已存在，跳过重复创建: 客户={}, 员工={}, executeTargetAttachId={}",
                        record.getCustomerName(), record.getWeUserId(), record.getExecuteTargetAttachId());
                return true; // 返回true表示操作成功（记录已存在）
            }

            // 设置默认值（仅支持手机拨打）
            if (record.getCallMethod() == null) {
                record.setCallMethod("mobile");
            }

            // 验证拨打方式
            if (!"mobile".equals(record.getCallMethod()) &&
                    !"unknown".equals(record.getCallMethod()) &&
                    !"wechat_voice".equals(record.getCallMethod())) {
                log.warn("[PHONE_CALL_RECORD] 不支持的拨打方式: {}, 设置为mobile", record.getCallMethod());
                record.setCallMethod("mobile");
            }

            boolean result = this.save(record);
            log.info("[PHONE_CALL_RECORD] 记录拨打电话结果: {}", result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            log.error("[PHONE_CALL_RECORD] 记录拨打电话失败", e);
            // 如果是唯一性约束冲突，也认为是成功的
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                log.warn("[PHONE_CALL_RECORD] 检测到重复记录，视为成功");
                return true;
            }
            throw e;
        }
    }

    @Override
    public List<WePhoneCallRecord> findCallRecordsByWeUserId(String weUserId, String sopBaseId) {
        return phoneCallRecordMapper.findCallRecordsByWeUserId(weUserId, sopBaseId);
    }

    @Override
    public List<WePhoneCallRecord> findCallRecordsByCustomer(String externalUserid, String weUserId) {
        return phoneCallRecordMapper.findCallRecordsByCustomer(externalUserid, weUserId);
    }

    @Override
    public boolean hasCalledCustomer(String externalUserid, String weUserId, String sopBaseId) {
        return phoneCallRecordMapper.hasCalledCustomer(externalUserid, weUserId, sopBaseId);
    }

    @Override
    public Map<String, Object> getCallStatistics(String weUserId, String sopBaseId) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总拨打次数
            LambdaQueryWrapper<WePhoneCallRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WePhoneCallRecord::getWeUserId, weUserId);
            if (sopBaseId != null && !sopBaseId.isEmpty()) {
                wrapper.eq(WePhoneCallRecord::getSopBaseId, sopBaseId);
            }
            
            long totalCalls = this.count(wrapper);
            statistics.put("totalCalls", totalCalls);
            
            // 已拨打的客户数量（去重）
            int calledCustomers = phoneCallRecordMapper.countCallRecordsBySop(weUserId, sopBaseId);
            statistics.put("calledCustomers", calledCustomers);
            
            // 按拨打方式统计
            List<WePhoneCallRecord> records = this.list(wrapper);
            Map<String, Long> methodStats = records.stream()
                    .collect(Collectors.groupingBy(
                            record -> {
                                String method = record.getCallMethod();
                                if ("mobile".equals(method)) {
                                    return "手机拨打";
                                } else if ("wechat_voice".equals(method)) {
                                    return "企业微信语音通话";
                                } else {
                                    return "未知方式";
                                }
                            },
                            Collectors.counting()));
            statistics.put("methodStats", methodStats);
            
            log.info("[PHONE_CALL_RECORD] 获取统计信息: 员工={}, SOP={}, 统计={}", weUserId, sopBaseId, statistics);
            
        } catch (Exception e) {
            log.error("[PHONE_CALL_RECORD] 获取统计信息失败", e);
            statistics.put("totalCalls", 0);
            statistics.put("calledCustomers", 0);
            statistics.put("methodStats", new HashMap<>());
        }
        
        return statistics;
    }

}
