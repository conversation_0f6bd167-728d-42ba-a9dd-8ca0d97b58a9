(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-99ade3ee"],{"2a7b":function(t,e,s){},3739:function(t,e,s){"use strict";s("2a7b")},"753e":function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t._self._c;return e("div",{staticClass:"list"},[e("PullRefreshScrollLoadList",{ref:"loadList",attrs:{request:t.getList,dealQueryFun:t.dealQueryFun},scopedSlots:t._u([{key:"default",fn:function(s){return["unread"==t.type&&s.length?e("van-button",{staticClass:"mb10",attrs:{type:"info",round:""},on:{click:function(e){t.read(),t.$emit("read")}}},[t._v(" 全部已读 ")]):t._e(),t._l(s,(function(s,i){return e("div",{key:i,staticClass:"list-item"},[e("div",{staticClass:"list-title"},[t._v(t._s(s.title))]),e("div",{staticClass:"list-info"},[t._v(" "+t._s(s.content)+" ")]),e("div",{staticClass:"list-time"},[t._v(t._s(s.notificationTime))])])}))]}}])})],1)},a=[],n=s("764a"),u={props:{type:{type:String,default:"read"}},data(){return{getList:n["a"],read:n["c"]}},computed:{},watch:{},created(){},mounted(){},methods:{dealQueryFun(t){t.type=this.type}}},l=u,r=(s("3739"),s("2877")),c=Object(r["a"])(l,i,a,!1,null,"3a4e6533",null);e["default"]=c.exports}}]);