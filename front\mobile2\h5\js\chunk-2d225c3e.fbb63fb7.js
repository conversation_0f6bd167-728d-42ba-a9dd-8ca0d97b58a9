(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d225c3e"],{e690:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("keep-alive",[t("router-view")],1),t("van-tabbar",{attrs:{value:e.active,fixed:""}},e._l(e.routes,(function(n,a){return t("van-tabbar-item",{key:a,attrs:{replace:"",to:{name:n.name},icon:n.meta.icon}},[e._v(" "+e._s(n.meta.title)+" ")])})),1)],1)},r=[],i=(n("e9f5"),n("f665"),{name:"",components:{},data(){return{routes:[]}},computed:{active(){return this.routes.findIndex(e=>e.name===this.$route.name)}},watch:{},created(){this.routes=this.$router.options.routes.find(e=>"/indexParent"===e.path).children},mounted(){},methods:{}}),o=i,u=n("2877"),s=Object(u["a"])(o,a,r,!1,null,"5fb1853c",null);t["default"]=s.exports}}]);