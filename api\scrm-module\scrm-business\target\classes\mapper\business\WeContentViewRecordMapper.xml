<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeContentViewRecordMapper">

    <select id="getList" resultType="org.scrm.domain.material.entity.WeContentViewRecord">
        SELECT r.id,
        r.view_by,
        r.view_openid,
        r.is_customer,
        r.view_time,
        r.view_watch_time,
        r.external_user_name,
        r.external_avatar,
        r.external_user_id,
        r.view_unionid
--         (select unionid from we_customer c where c.unionid = r.view_unionid and c.unionid is not null limit 1) as unionid
        FROM
        we_content_view_record r
        <where>
            and r.del_flag = 0
            <if test="talkId!=null">
                and r.talk_id = #{talkId}
            </if>
            <if test="contentId!=null">
                and r.content_id = #{contentId}
            </if>
            <if test="resourceType!=null">
                and r.resource_type = #{resourceType}
            </if>
            <if test="beginTime!=null and beginTime!=''">
                and r.view_time &gt;= #{beginTime}
            </if>
            <if test="endTime!=null and endTime!=''">
                and r.view_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="findContentDataDetailVos" resultType="org.scrm.domain.material.vo.ContentDataDetailVo">
        SELECT
            wcvr.view_unionid,
            COUNT(wcvr.id) AS viewTotalNum,
            COALESCE(c.avatar, wcvr.external_avatar) AS viewAvatar,
            CASE
            WHEN c.unionid IS NOT NULL THEN c.customer_name
            ELSE IFNULL(wcvr.external_user_name, "@微信客户")
            END AS viewBy,
            IF(c.unionid IS NOT NULL, 1, 0) AS isCustomer,
            CASE
            WHEN c.unionid IS NOT NULL THEN c.external_userid
            ELSE wcvr.view_unionid
            END AS externalUserid,
            ROUND(SUM(wcvr.view_watch_time) / 1000, 0) AS viewDuration
        FROM
        we_content_view_record wcvr
        LEFT JOIN
        we_customer c
        ON wcvr.view_unionid = c.unionid
        WHERE wcvr.content_id=#{contentId}
        <if test="startTime !=null and startTime !='' and endTime !=null and endTime !='' ">
            and date_format(wcvr.create_time,'%Y-%m-%d') BETWEEN #{startTime} AND #{endTime}
        </if>
        GROUP BY wcvr.view_openid
    </select>


</mapper>