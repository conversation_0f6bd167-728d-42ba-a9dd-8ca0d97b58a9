(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0976fc04"],{"252d":function(t,e,n){"use strict";n.r(e);n("14d9");var r=function(){var t=this,e=t._self._c;return e("div",{staticClass:"list"},[e("van-button",{staticClass:"mb10",attrs:{round:"",type:"primary",size:"small"},on:{click:t.getAddTemplate}},[t._v("新增线索")]),e("PullRefreshScrollLoadList",{ref:"list",attrs:{request:t.getAddList},scopedSlots:t._u([{key:"default",fn:function(n){return t._l(n,(function(n,r){return e("div",{key:r,staticClass:"list-item",on:{click:function(e){return t.$router.push({name:"clueDetail",query:{id:n.id}})}}},[e("div",{staticClass:"list-title"},[t._v(t._s(n.name))]),e("div",{staticClass:"list-info"},[t._v(" "+t._s(n.phone)+" ")])])}))}}])}),e("van-action-sheet",{attrs:{title:"新增线索"},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[e("van-form",{on:{submit:t.onSubmit}},[e("van-field",{attrs:{label:"姓名",placeholder:"请输入姓名",required:"",rules:[{required:!0,message:""}]},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}}),e("van-field",{attrs:{required:"",rules:[{required:!0,message:""}],label:"联系方式",placeholder:"请输入联系方式",type:"tel",maxlength:"11","show-word-limit":""},model:{value:t.form.phone,callback:function(e){t.$set(t.form,"phone",e)},expression:"form.phone"}}),e("van-field",{attrs:{name:"radio",label:"性别"},scopedSlots:t._u([{key:"input",fn:function(){return[e("van-radio-group",{attrs:{direction:"horizontal"},model:{value:t.form.sex,callback:function(e){t.$set(t.form,"sex",e)},expression:"form.sex"}},[e("van-radio",{attrs:{name:"1"}},[t._v("男")]),e("van-radio",{attrs:{name:"2"}},[t._v("女")]),e("van-radio",{attrs:{name:"0"}},[t._v("未知")])],1)]},proxy:!0}])}),t._l(t.form.propertiesList,(function(n,r){return[1==n.tableEntryAttr||2==n.dataAttr?e("van-field",{attrs:{readonly:"",name:n.labelName,label:n.tableEntryName,placeholder:"请选择",required:1==n.required,rules:[{required:1==n.required,message:""}]},on:{click:function(e){return t.setSelectOpen(n)}},model:{value:n.value,callback:function(e){t.$set(n,"value",e)},expression:"item.value"}}):0==n.tableEntryAttr?e("van-field",{attrs:{type:1==n.dataAttr?"number":"text",name:n.tableEntryName,label:n.tableEntryName,placeholder:"请输入",maxlength:n.maxInputLen,"show-word-limit":"",required:1==n.required,rules:[{required:1==n.required,message:""}]},model:{value:n.value,callback:function(e){t.$set(n,"value",e)},expression:"item.value"}}):t._e()]})),e("van-popup",{attrs:{round:"",position:"bottom","close-on-click-overlay":!1},model:{value:t.currentEditObj.show,callback:function(e){t.$set(t.currentEditObj,"show",e)},expression:"currentEditObj.show"}},[2==t.currentEditObj.dataAttr?e("van-datetime-picker",{attrs:{type:1==t.currentEditObj.datetimeType?"datetime":"date",title:"选择时间"},on:{confirm:t.datetimeConfirm,cancel:function(e){t.currentEditObj.show=!1}},model:{value:t.currentEditObj._valueDatetime,callback:function(e){t.$set(t.currentEditObj,"_valueDatetime",e)},expression:"currentEditObj._valueDatetime"}}):1==t.currentEditObj.tableEntryAttr?e("van-picker",{attrs:{"value-key":"content","show-toolbar":"",columns:t.currentEditObj.tableEntryContent||[]},on:{cancel:function(e){t.currentEditObj.show=!1},confirm:t.selectChange}}):t._e()],1),e("div",{staticClass:"flex",staticStyle:{margin:"20px"}},[e("van-button",{attrs:{round:"",block:"",plain:"","native-type":"button"},on:{click:t.cancel}},[t._v("取消")]),e("van-button",{staticClass:"ml10",attrs:{round:"",block:"",type:"info","native-type":"submit"}},[t._v("提交")])],1)],2)],1)],1)},a=[],i=(n("e9f5"),n("ab43"),n("2b91")),o=n("ed08");const s={name:"",phone:"",sex:"1",propertiesList:[]};var l={name:"",components:{},data(){return{getAddList:i["c"],show:!1,form:JSON.parse(JSON.stringify(s)),templateList:void 0,columns:[],currentEditObj:{}}},computed:{},watch:{},created(){},mounted(){},methods:{async getAddTemplate(){this.templateList||(this.templateList=(await Object(i["d"])()).data),this.form.propertiesList=JSON.parse(JSON.stringify(this.templateList)),this.show=!0},setSelectOpen(t){this.currentEditObj=t,2==t.dataAttr&&(this.currentEditObj._valueDatetime=new Date(this.currentEditObj.value||new Date)),this.$set(this.currentEditObj,"show",!0)},datetimeConfirm(t){this.currentEditObj.value=Object(o["b"])(t,"yyyy/MM/dd hh:mm:ss"),this.currentEditObj.show=!1},selectChange(t){this.currentEditObj.value=t.content,this.currentEditObj.show=!1},cancel(){this.show=!1},onSubmit(){let t=JSON.parse(JSON.stringify(this.form));t.propertiesList=t.propertiesList.map(t=>({id:t.id,key:t.tableEntryName,name:t.tableEntryName,keyEn:t.tableEntryId,value:t.value}));const e=this.$toast.loading();Object(i["a"])(t).then(t=>{this.$toast.success("操作成功"),this.$refs.list.getList(),this.cancel(),this.form=JSON.parse(JSON.stringify(s))}).catch(t=>{console.log(t)}).finally(()=>{e.clear()})}}},u=l,c=(n("f225"),n("2877")),d=Object(c["a"])(u,r,a,!1,null,"5dc571c6",null);e["default"]=d.exports},"2b91":function(t,e,n){"use strict";n.d(e,"l",(function(){return u})),n.d(e,"j",(function(){return c})),n.d(e,"d",(function(){return d})),n.d(e,"a",(function(){return m})),n.d(e,"c",(function(){return b})),n.d(e,"k",(function(){return f})),n.d(e,"i",(function(){return p})),n.d(e,"m",(function(){return h})),n.d(e,"o",(function(){return v})),n.d(e,"n",(function(){return O})),n.d(e,"p",(function(){return g})),n.d(e,"e",(function(){return j})),n.d(e,"b",(function(){return y})),n.d(e,"h",(function(){return w})),n.d(e,"g",(function(){return E})),n.d(e,"f",(function(){return k}));var r=n("b775");const{get:a,post:i,put:o,del:s}=r["b"],l="/leads";function u(){return Object(r["b"])({url:"/sea/manager/list",method:"get"})}function c(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(r["b"])({url:l+"/sea/list",params:t})}function d(){return Object(r["b"])({url:l+"/template/settings/editable"})}function m(t){return t.leadsId=t.leadId,Object(r["b"])({url:l+"/manual/add",method:"post",data:t})}function b(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(r["b"])({url:l+"/manual/list",params:t})}function f(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(r["b"])({url:l+"/my/follow",params:t})}function p(t){return Object(r["b"])({url:l+"/get/"+t,method:"get"})}function h(t){return Object(r["b"])({url:l+"/receive",method:"get",params:{leadsId:t}})}function v(t){return t.leadsId=t.leadId,Object(r["b"])({url:l+"/user/return",data:t,method:"post"})}function O(t){return t.leadsId=t.leadId,Object(r["b"])({url:l+"/update",data:t,method:"put"})}function g(t){return t.leadsId=t.leadId,Object(r["b"])({url:l+"/bind/customer",method:"post",data:t})}function j(){return Object(r["b"])({url:l+"/follow/record/getFollowMode",method:"get"})}function y(t){return Object(r["b"])({url:l+"/follow/record/addFollow",data:t,method:"post"})}function w(t){return Object(r["b"])({url:l+"/follow/record/getFollowUpList/"+t,method:"get"})}function E(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(r["b"])({url:l+"/follow/record/list",params:t,method:"get"})}function k(t){return Object(r["b"])({url:l+"/follow/record/"+t})}},"7a28":function(t,e,n){},f225:function(t,e,n){"use strict";n("7a28")}}]);