(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e1890e6"],{"34a8":function(t,e,i){},"365c":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return c})),i.d(e,"a",(function(){return d})),i.d(e,"d",(function(){return l}));var a=i("b775");const s=window.sysConfig.services.wecom,n=window.sysConfig.services.weChat,o=n+"/live";function r(t){return Object(a["a"])({url:s+"/operation/getCustomerAnalysisForApp",params:t})}function c(t){return Object(a["a"])({url:s+"/operation/getGroupAnalysisByApp",params:t})}function d(t){return Object(a["a"])({url:s+"/trajectory/findCompanyDynamics",params:t})}function l(t){return Object(a["a"])({url:o+"/getLivingCode",params:t})}},4134:function(t,e,i){"use strict";i("34a8")},"8d46":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[e("div",{staticClass:"banner"},[e("div",{staticClass:"avater"},[t.detail.avatar?e("img",{attrs:{src:t.detail.avatar,alt:""}}):e("svg-icon",{staticClass:"user-icon",attrs:{name:"portrait"}})],1)]),e("div",{staticClass:"body-card"},[e("div",{staticClass:"title"},[t._v(t._s(t.detail.liveTitle))]),e("div",{staticClass:"content-one"},[t._v("直播讲师："+t._s(t.detail.liveUserName))]),e("div",{staticClass:"content-one"},[t._v(" 直播开始时间： "),t.detail.liveStartDate?e("span",[t._v(t._s(t.detail.liveStartDate+" "+t.detail.liveStartTime))]):t._e()]),e("div",{staticClass:"content-one"},[t._v("直播简介：")]),e("div",{staticClass:"content-two"},[t._v(" "+t._s(t.detail.liveDesc)+" ")]),t.isPC?e("div",{staticClass:"footer"},[e("button",{staticClass:"buttom"},[t._v(t._s(t.buttomText))])]):e("div",{staticClass:"footer"},[e("wx-open-launch-weapp",{attrs:{id:"launch-btn",username:"gh_25e071b83ee0",path:"pages/watch/index?living_code="+t.liveCode}},[e("script",{attrs:{type:"text/wxtag-template"}},[t._v(' <style>.buttom { width: 343px; height: 50px; color: #fff; background: #0079de; text-align: center; border-radius: 50px; line-height: 50px; font-size: 16px; }</style> <div class="buttom">'+t._s(t.buttonText)+"</div> ")])])],1)])])},s=[],n=i("365c"),o=i("ed08"),r=i("2934"),c={data(){return{detail:{},liveCode:"",query:{openid:"",livingId:""},buttonText:"",isPC:!0,loading:!1,buttomText:"请在手机端打开"}},created(){},mounted(){navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)?this.isPC=!1:this.isPC=!0,this.query.livingId=this.$route.query.id;var t=navigator.userAgent.toLowerCase();this.userAgent=navigator.userAgent,"micromessenger"==t.match(/micromessenger/i)&&"wxwork"==t.match(/wxwork/i)?(this.isPC=!0,this.buttomText="请在手机微信端打开"):"micromessenger"==t.match(/micromessenger/i)&&Object(o["d"])().then(({openId:t,unionId:e,avatar:i})=>{this.query.openid=t,this.interLive();let a=this;this.loading=!0,Object(r["k"])(window.location.href.split("#")[0]).then(t=>{if(200===t.code){let{timestamp:e,nonceStr:i,signature:s}=t.data;wx.ready((function(){console.log("成功")})),wx.error((function(t){a.loading=!1,a.$toast.clear(),a.$dialog({message:"config失败"+JSON.stringify(t)})})),wx.config({appId:sessionStorage.getItem("weAppId"),timestamp:e,nonceStr:i,signature:s,jsApiList:["showOptionMenu","chooseImage","previewImage"],openTagList:["wx-open-launch-weapp"]})}})}).catch(()=>{})},methods:{interLive(){Object(n["d"])(this.query).then(t=>{200===t.code&&(0===t.data.weLive.replayStatus&&1===t.data.weLive.openReplay?(this.liveCode=t.data.liveCode+"&replay=1",this.buttonText="查看回放"):(this.liveCode=t.data.liveCode,this.buttonText="进入直播"),this.detail=t.data.weLive)})}}},d=c,l=(i("4134"),i("2877")),u=Object(l["a"])(d,a,s,!1,null,"082fcfc6",null);e["default"]=u.exports}}]);