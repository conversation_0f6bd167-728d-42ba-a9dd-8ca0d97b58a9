<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeGroupSopChatMapper">


    <insert id="batchBindsSopChat">
        insert into we_group_sop_chat(id,rule_id, chat_id, is_done,create_by,create_time,update_by,update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},#{item.ruleId},#{item.chatId}, #{item.isDone},#{item.createBy},#{item.createTime},#{item.updateBy},#{item.updateTime})
        </foreach>
    </insert>




    <select id="getScopeListByRuleId" parameterType="Long" resultType="org.scrm.domain.community.vo.WeCommunityTaskEmplVo">
        SELECT
            wu.user_id as userId,
            wu.user_name as name,
            wu.avatar,
            wgsc.is_done as isDone
        FROM
            we_group_sop_chat wgsc
                LEFT JOIN we_group_sop wgs ON wgsc.rule_id = wgs.id
                LEFT JOIN we_group wg ON wg.chat_id = wgsc.chat_id
                LEFT JOIN sys_user wu ON wu.user_id = wg.`owner`
        WHERE
            wgsc.rule_id = #{ruleId}
    </select>


</mapper>