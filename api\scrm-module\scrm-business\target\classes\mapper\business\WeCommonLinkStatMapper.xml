<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCommonLinkStatMapper">

    <select id="getLine" resultType="org.scrm.domain.shortlink.dto.WeShortLinkLineDto">
        SELECT
            date as xTime,
            IFNULL((select sum(d1.pv_num) from we_common_link_stat d1 where
            DATE_FORMAT(d1.create_time,'%Y-%m-%d')=date
        <if test="query.promotionId !=null">
            AND d1.short_id = #{query.promotionId}
        </if>
        <if test="query.type !=null and query.type !=''">
            AND d1.type = #{query.type}
        </if>
        and d1.del_flag = 0
        ),0) as pv_num,
        IFNULL((select sum(d1.uv_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d')=date
        <if test="query.promotionId !=null">
            AND d1.short_id = #{query.promotionId}
        </if>
        <if test="query.type !=null and query.type !=''">
            AND d1.type = #{query.type}
        </if>
        and d1.del_flag = 0
        ),0) as uv_num,
        IFNULL((select sum(d1.open_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d')=date
        <if test="query.promotionId !=null">
            AND d1.short_id = #{query.promotionId}
        </if>
        <if test="query.type !=null and query.type !=''">
            AND d1.type = #{query.type}
        </if>
        and d1.del_flag = 0
        ),0) as open_num
        FROM
        sys_dim_date
        <where>
            <if test="query.beginTime !=null and query.beginTime !='' and query.endTime !='' and  query.endTime != null ">
                DATE_FORMAT(date,'%Y-%m-%d') BETWEEN #{query.beginTime} and #{query.endTime}
            </if>
        </where>
        ORDER BY date ASC
    </select>


    <select id="statistics" resultType="org.scrm.domain.shortlink.vo.WeShortLinkCountVo">
        SELECT
        IFNULL((select sum(d1.pv_num) from we_common_link_stat d1 where
        DATE(d1.create_time) &lt;= #{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as pv,

        IFNULL((select sum(d1.pv_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d')=#{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as todayPv,


        IFNULL((select sum(d1.uv_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d') &lt;= #{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as uv,

        IFNULL((select sum(d1.uv_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d') = #{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as todayUv,


        IFNULL((select sum(d1.open_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d') &lt;= #{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as open,

        IFNULL((select sum(d1.open_num) from we_common_link_stat d1 where
        DATE_FORMAT(d1.create_time,'%Y-%m-%d') = #{queryTime}
        <if test="promotionId !=null">
            AND d1.short_id = #{promotionId}
        </if>
        <if test="type !=null and type !=''">
            AND d1.type = #{type}
        </if>
        and d1.del_flag = 0
        ),0) as todayOpen
    </select>


</mapper>
