(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34b1d9f2"],{3912:function(t,e,s){"use strict";s("8a70")},"8a70":function(t,e,s){},9585:function(t,e,s){"use strict";s.d(e,"d",(function(){return c})),s.d(e,"c",(function(){return d})),s.d(e,"a",(function(){return l})),s.d(e,"b",(function(){return p}));var r=s("b775");const{get:a,post:i,put:o,del:n}=r["b"],u="/svipGroup",c=t=>a(u+"/findSvipGroupTpls",{status:"1",...t}),d=t=>a(u+"/findSvipGroupTplByIdForApp",{externalUserid:t.externalUserId,...t}),l=t=>a(u+"/checkSvipGroup",{externalUserid:t.externalUserId,...t}),p=t=>i(u+"/completeSvipGroup",t)},c882:function(t,e,s){"use strict";s.r(e);var r=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{"padding-bottom":"60px"}},[e("div",{staticClass:"card"},[e("div",{staticClass:"tplName"},[t._v(t._s(t.form.tplName))]),t.form.tplDesc?e("div",{staticClass:"desc mt10"},[t._v(t._s(t.form.tplDesc))]):t._e()]),e("div",{staticClass:"card"},[t._m(0),e("van-field",{staticClass:"textarea",attrs:{type:"textarea",autosize:{maxHeight:100,minHeight:50},placeholder:"群聊名称","label-align":"top"},model:{value:t.form.groupName,callback:function(e){t.$set(t.form,"groupName",e)},expression:"form.groupName"}})],1),e("div",{staticClass:"card pb0"},[e("div",{staticClass:"title bold"},[t._v("默认员工")]),t._l(t.form.sysUserList,(function(s,r){return e("div",{key:r,staticClass:"user-li"},[e("van-image",{staticClass:"avatar",attrs:{src:t.avatar}}),e("span",[t._v(t._s(s.userName))])],1)}))],2),e("div",{staticClass:"action"},[e("van-button",{attrs:{type:"info",plain:"",round:""},on:{click:function(e){return t.$router.back()}}},[t._v(" 取消 ")]),e("van-button",{attrs:{type:"info",round:""},on:{click:t.concat}},[t._v(" 确定创建 ")])],1)])},a=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"title bold"},[e("span",{staticStyle:{color:"red"}},[t._v("*")]),t._v(" 群聊名称 ")])}],i=(s("e9f5"),s("ab43"),s("9585")),o={props:{},components:{},data(){return{form:""}},computed:{},watch:{},created(){this.getVipGroup()},mounted(){},methods:{getVipGroup(){let t=this.$route.query.userId||sessionStorage.userId;Object(i["c"])({svipGroupIds:this.$route.query.id,weUserId:t,externalUserid:this.$route.query.externalUserId}).then(t=>{this.form=t.data})},concat(){if(!this.form.groupName)return void this.$toast("请输入群聊名称");this.$toast.loading({message:"loading...",duration:0,forbidClick:!0});let t=this.$route.query.userId||sessionStorage.userId;Object(i["a"])({weUserId:t,externalUserId:this.$route.query.externalUserId}).then(({data:e})=>{let s=this.form.sysUserList.map(t=>t.weUserId).join(";");wx.openEnterpriseChat({userIds:s,externalUserIds:this.$route.query.externalUserId,groupName:this.form.groupName,chatId:e.chatId||"",success:e=>{var s=e.chatId;Object(i["b"])({svipGroupIds:this.$route.query.id,chatId:s,weUserId:t,externalUserid:this.$route.query.externalUserId}).then(t=>{}).finally(()=>{this.$toast.clear()})},fail:t=>{this.$toast.clear(),t.errMsg.indexOf("function not exist")>-1?alert("版本过低请升级"):this.$dialog({message:"创建失败："+JSON.stringify(t)})}})})}}},n=o,u=(s("3912"),s("2877")),c=Object(u["a"])(n,r,a,!1,null,"54abc81b",null);e["default"]=c.exports}}]);