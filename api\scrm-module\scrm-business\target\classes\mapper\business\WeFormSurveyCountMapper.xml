<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeFormSurveyCountMapper">

    <select id="getStatistics" resultType="org.scrm.domain.WeFormSurveyStatistics">
        SELECT
        (
            SELECT
             IFNULL(sum( wfsc.total_visits ),0)
            FROM
            we_form_survey_count wfsc
            WHERE
            DATE(wfsc.create_time) &lt;= #{weFormSurveyCount.endTime} and wfsc.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsc.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalVisits,
        (
            SELECT
            count(*)
            FROM
            we_form_survey_count wfsc
            WHERE
            DATE(wfsc.create_time)  &lt;= #{weFormSurveyCount.endTime} and wfsc.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsc.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalUser,
        (
            SELECT
            count(*)
            FROM
            we_form_survey_answer wfsa
            WHERE
            DATE(wfsa.create_time) &lt;= #{weFormSurveyCount.endTime} and wfsa.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsa.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as collectionVolume,
        (SELECT
            IFNULL(SUM( wfsa.total_time ),0)
            FROM
            we_form_survey_answer wfsa
            WHERE
            DATE ( wfsa.create_time ) &lt;= #{weFormSurveyCount.endTime} and wfsa.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsa.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalTime,
        CONCAT(
        ROUND(
        (
        SELECT COUNT(*)
        FROM we_form_survey_answer wfsa
        WHERE DATE(wfsa.create_time) &lt;= #{weFormSurveyCount.endTime}
        AND wfsa.belong_id = #{weFormSurveyCount.belongId}
        <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName != ''">
            AND wfsa.data_source = #{weFormSurveyCount.channelsName}
        </if>
        ) / IFNULL((
            SELECT COUNT(*)
            FROM we_form_survey_count wfsc
            WHERE DATE(wfsc.create_time) &lt;= #{weFormSurveyCount.endTime}
            AND wfsc.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName != ''">
                AND wfsc.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ), 1) * 100,
        2
        ),
        '%'
        ) AS collectionRate
    </select>

    <select id="findDataList" resultType="org.scrm.domain.WeFormSurveyStatistics">
        SELECT
        sd.DATE AS DATE,
        (
            SELECT
             IFNULL(sum( wfsc.total_visits ),0)
            FROM
            we_form_survey_count wfsc
            WHERE
            DATE(wfsc.create_time) = sd.date and wfsc.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsc.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalVisits,
        (
            SELECT
            count(*)
            FROM
            we_form_survey_count wfsc
            WHERE
            DATE(wfsc.create_time)  = sd.date and wfsc.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsc.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalUser,
        (
            SELECT
            count(*)
            FROM
            we_form_survey_answer wfsa
            WHERE
            DATE(wfsa.create_time) = sd.date and wfsa.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsa.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as collectionVolume,

        (SELECT
             IFNULL(SUM( wfsa.total_time ),0)
           FROM
            we_form_survey_answer wfsa
            WHERE
            DATE ( wfsa.create_time ) = sd.DATE and wfsa.belong_id = #{weFormSurveyCount.belongId}
            <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
                AND wfsa.data_source = #{weFormSurveyCount.channelsName}
            </if>
        ) as totalTime,

        CONCAT(
            ROUND(
            (
                SELECT COUNT(*)
                FROM we_form_survey_answer wfsa
                WHERE DATE(wfsa.create_time) = sd.DATE
                AND wfsa.belong_id = #{weFormSurveyCount.belongId}
                <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName != ''">
                    AND wfsa.data_source = #{weFormSurveyCount.channelsName}
                </if>
                ) / IFNULL((
                SELECT COUNT(*)
                FROM we_form_survey_count wfsc
                WHERE DATE(wfsc.create_time) = sd.DATE
                AND wfsc.belong_id = #{weFormSurveyCount.belongId}
                <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName != ''">
                    AND wfsc.data_source = #{weFormSurveyCount.channelsName}
                </if>
                ), 1) * 100,
                2
            ),
        '%'
        ) AS collectionRate
        FROM
        sys_dim_date sd
        <where>
            <if test="weFormSurveyCount.beginTime != null and weFormSurveyCount.beginTime !='' and weFormSurveyCount.endTime !=''  and weFormSurveyCount.endTime !=null">
                DATE_FORMAT( sd.DATE, '%Y-%m-%d' ) BETWEEN #{weFormSurveyCount.beginTime} and #{weFormSurveyCount.endTime}
            </if>
        </where>
        ORDER BY sd.DATE ASC
    </select>

    <select id="lineChart" resultType="org.scrm.domain.WeFormSurveyStatistics">
        SELECT
        date_format( sdd.date, '%Y-%m-%d' ) AS  date,
        IFNULL(SUM(wfsc.total_visits), 0) as totalVisits,
        count(wfsc.id) as totalUser,
        IFNULL((SELECT count(wfsa.id) from we_form_survey_answer wfsa WHERE wfsa.belong_id=wfsc.belong_id
        <if test="weFormSurveyCount.beginTime != null and weFormSurveyCount.beginTime !='' and weFormSurveyCount.endTime !=''  and weFormSurveyCount.endTime !=null">
            AND date_format(wfsa.create_time, '%Y-%m-%d' ) BETWEEN #{weFormSurveyCount.beginTime} and #{weFormSurveyCount.endTime}
        </if>
        ), 0) as collectionVolume
        FROM
        sys_dim_date sdd
        LEFT JOIN 	we_form_survey_count wfsc ON date_format( sdd.date, '%Y-%m-%d' ) =date_format( wfsc.create_time, '%Y-%m-%d' )
        <if test="weFormSurveyCount.belongId !=null">
            and wfsc.belong_id = #{weFormSurveyCount.belongId}
        </if>

        <if test="weFormSurveyCount.channelsName != null and weFormSurveyCount.channelsName !=''">
            AND wfsc.data_source=#{weFormSurveyCount.channelsName}
        </if>
        <where>
            <if test="weFormSurveyCount.beginTime != null and weFormSurveyCount.beginTime !='' and weFormSurveyCount.endTime !=''  and weFormSurveyCount.endTime !=null">
                date_format( sdd.date, '%Y-%m-%d' )    BETWEEN #{weFormSurveyCount.beginTime} and #{weFormSurveyCount.endTime}
            </if>
        </where>
        GROUP BY date_format( sdd.date, '%Y-%m-%d' )
    </select>

    <select id="sumTotalVisits" resultType="int">
        SELECT
        sum(total_visits)
        FROM
        we_form_survey_count
        <where>
            belong_id=#{belongId}
        </where>
    </select>

</mapper>
