(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-524018e3"],{"74c7":function(t,e,s){"use strict";s("f2d5")},d956:function(t,e,s){"use strict";s.r(e);var n=function(){var t=this,e=t._self._c;return e("div",{staticStyle:{padding:"10px"}},[e("index",{attrs:{type:"history"}})],1)},a=[],u=s("fe26"),i={name:"",components:{index:u["a"]},data(){return{}},computed:{},watch:{},created(){},mounted(){},methods:{}},r=i,c=s("2877"),o=Object(c["a"])(r,n,a,!1,null,"753af0e0",null);e["default"]=o.exports},f21d:function(t,e,s){"use strict";s.d(e,"b",(function(){return o})),s.d(e,"a",(function(){return l}));var n=s("b775");const{get:a,post:u,put:i,del:r}=n["b"],c="/weTasks";function o(t){return t=Object.assign({pageNum:1,pageSize:10,type:"list"},t),a(`${c}/${t.type}`,t)}function l(t){return i(`${c}/finish/${t}`)}},f2d5:function(t,e,s){},fe26:function(t,e,s){"use strict";var n=function(){var t=this,e=t._self._c;return e("PullRefreshScrollLoadList",{attrs:{request:t.getList,dealQueryFun:t.dealQueryFun,dealDataFun:t.dealDataFun},scopedSlots:t._u([{key:"default",fn:function(s){return t._l(s,(function(s,n){return e("div",{key:n,class:["task",0!=s.status&&"history"],on:{click:function(e){return t.goRoute(s)}}},[e("div",[s.statusStr?e("van-tag",{attrs:{type:"primary",size:"medium"}},[t._v(t._s(s.statusStr))]):t._e(),e("span",{staticClass:"task-title"},[t._v(t._s(s.title))])],1),s.contentIsObj?t._l(s.content,(function(s,n){return e("div",{key:n,staticClass:"task-info"},[e("span",{staticClass:"key"},[t._v(t._s(n)+":")]),e("span",{staticClass:"value"},[t._v(t._s(s))])])})):e("div",{staticClass:"task-info"},[t._v(" "+t._s(t.content)+" ")]),e("div",{staticClass:"task-time"},[t._v(t._s(s.sendTime))])],2)}))}}])})},a=[],u=(s("14d9"),s("e9f5"),s("7d54"),s("f21d")),i={name:"",props:{type:{type:String,default:"list"}},data(){return{getList:u["b"],queryType:"list"}},computed:{},watch:{},created(){},mounted(){},methods:{dealQueryFun(t){let e=this.$route.query.type;this.queryType=t.type=e||this.type},dealDataFun(t){t.forEach(t=>{let e=t.content;e&&e.includes("{")&&(t.content=JSON.parse(e),t.contentIsObj=!0)})},goRoute(t){/^http/.test(t.url)?location.href=t.url:this.$router.push(`${t.url}${t.url.includes("?")?"&":"?"}tasksId=${t.id}&status=${t.status}`)}}},r=i,c=(s("74c7"),s("2877")),o=Object(c["a"])(r,n,a,!1,null,"2e8294ac",null);e["a"]=o.exports}}]);