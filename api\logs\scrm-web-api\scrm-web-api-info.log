2025-07-23 10:23:30.389 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-23 10:23:30.432 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-23 10:23:30.433 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-23 10:23:32.152 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 10:23:32.154 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:23:32.316 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 145 ms. Found 0 Redis repository interfaces.
2025-07-23 10:23:32.781 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-23 10:23:33.214 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$8326aa5f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.243 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.251 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.254 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.259 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.267 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/125992315] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.277 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.286 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.334 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.348 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:23:33.735 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-23 10:23:34.079 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-23 10:23:34.088 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-23 10:23:34.088 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:23:34.088 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-23 10:23:34.350 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:23:34.350 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3916 ms
2025-07-23 10:23:34.841 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:23:35.363 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:23:36.773 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:23:44.117 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:23:44.132 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:23:44.517 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:23:45.442 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:23:46.296 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-23 10:23:49.275 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentController': Unsatisfied dependency expressed through field 'weAgentInfoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
2025-07-23 10:23:49.309 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-23 10:23:49.332 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-23 10:23:49.340 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-23 10:23:49.355 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-23 10:23:49.494 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentController': Unsatisfied dependency expressed through field 'weAgentInfoService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:953)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:740)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:415)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:303)
	at org.scrm.ScrmWeApiApplication.main(ScrmWeApiApplication.java:24)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weAgentInfoServiceImpl': Unsatisfied dependency expressed through field 'weMaterialService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 18 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weMaterialServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 32 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'weContentViewRecordServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:332)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 43 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'weCustomerServiceImpl': Unsatisfied dependency expressed through field 'redissonClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:639)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.autowireResource(CommonAnnotationBeanPostProcessor.java:544)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.getResource(CommonAnnotationBeanPostProcessor.java:520)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor$ResourceElement.getResourceToInject(CommonAnnotationBeanPostProcessor.java:673)
	at org.springframework.beans.factory.annotation.InjectionMetadata$InjectedElement.inject(InjectionMetadata.java:228)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.context.annotation.CommonAnnotationBeanPostProcessor.postProcessProperties(CommonAnnotationBeanPostProcessor.java:329)
	... 59 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'singleServerConfig' defined in class path resource [org/scrm/config/redisson/RedissonConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:486)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1389)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1309)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:656)
	... 78 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.redisson.api.RedissonClient]: Factory method 'singleServerConfig' threw exception; nested exception is org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 91 common frames omitted
Caused by: org.redisson.client.RedisConnectionException: Unable to connect to Redis server: 127.0.0.1/127.0.0.1:6379
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$0(ConnectionPool.java:154)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at org.redisson.connection.pool.ConnectionPool.promiseFailure(ConnectionPool.java:318)
	at org.redisson.connection.pool.ConnectionPool.lambda$createConnection$6(ConnectionPool.java:277)
	at java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:760)
	at java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:736)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:474)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1977)
	at org.redisson.client.RedisClient$1$2.run(RedisClient.java:257)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:164)
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java)
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:469)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:500)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.CompletionException: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: 127.0.0.1/127.0.0.1:6379
	at java.util.concurrent.CompletableFuture.encodeRelay(CompletableFuture.java:326)
	at java.util.concurrent.CompletableFuture.completeRelay(CompletableFuture.java:338)
	at java.util.concurrent.CompletableFuture.uniRelay(CompletableFuture.java:911)
	at java.util.concurrent.CompletableFuture$UniRelay.tryFire(CompletableFuture.java:899)
	... 11 common frames omitted
Caused by: io.netty.channel.AbstractChannel$AnnotatedConnectException: Connection refused: no further information: 127.0.0.1/127.0.0.1:6379
Caused by: java.net.ConnectException: Connection refused: no further information
	at sun.nio.ch.SocketChannelImpl.checkConnect(Native Method)
	at sun.nio.ch.SocketChannelImpl.finishConnect(SocketChannelImpl.java:717)
	at io.netty.channel.socket.nio.NioSocketChannel.doFinishConnect(NioSocketChannel.java:330)
	at io.netty.channel.nio.AbstractNioChannel$AbstractNioUnsafe.finishConnect(AbstractNioChannel.java:334)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:707)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
2025-07-23 10:23:50.252 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-23 10:26:05.080 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-23 10:26:05.105 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-23 10:26:05.106 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-23 10:26:06.700 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 10:26:06.703 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:26:06.855 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 139 ms. Found 0 Redis repository interfaces.
2025-07-23 10:26:07.277 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-23 10:26:07.624 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$2a7f3c91] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.650 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.657 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.660 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.665 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.671 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.674 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.675 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.682 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.692 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.734 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:07.746 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:26:08.129 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-23 10:26:08.456 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-23 10:26:08.463 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-23 10:26:08.464 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:26:08.464 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-23 10:26:08.727 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:26:08.727 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3621 ms
2025-07-23 10:26:09.239 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:09.738 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:26:11.234 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:26:17.602 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:17.617 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:17.981 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:18.659 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:19.159 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-23 10:26:19.502 [redisson-netty-4-7] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:26:19.509 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:26:19.818 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:19.830 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:20.434 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:21.440 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:21.858 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:22.081 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:23.814 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:23.993 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:24.776 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:24.852 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:25.799 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:26.459 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:26:27.704 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-23 10:26:27.705 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-23 10:26:28.871 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-23 10:26:29.149 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:26:29.150 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-23 10:26:29.151 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:26:29.441 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-23 10:26:29.488 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-23 10:26:29.491 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-23 10:26:29.492 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-23 10:26:29.528 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-23 10:26:29.669 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 26.667 seconds (JVM running for 28.332)
2025-07-23 10:26:29.675 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:26:29.677 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:26:29.678 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:26:29.678 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:26:29.678 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:26:29.678 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:26:29.679 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:26:29.679 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:26:30.516 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:26:30.523 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:31:09.793 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-23 10:31:09.793 [Thread-67] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-23 10:31:09.793 [Thread-67] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-23 10:31:09.794 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-23 10:31:18.281 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-23 10:31:18.306 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-23 10:31:19.938 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 10:31:19.940 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:31:20.095 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 141 ms. Found 0 Redis repository interfaces.
2025-07-23 10:31:20.525 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-23 10:31:20.883 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$cfdcfeea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.907 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.915 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.918 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.924 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.931 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.933 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.934 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.941 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.950 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:20.991 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:21.003 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:31:21.383 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-23 10:31:21.704 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-23 10:31:21.711 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-23 10:31:21.712 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:31:21.712 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-23 10:31:21.958 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:31:21.958 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3651 ms
2025-07-23 10:31:22.434 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:22.923 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:31:24.197 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:31:31.679 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:31.689 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:32.008 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:32.736 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:33.307 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-23 10:31:33.598 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:31:33.630 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:31:33.921 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:33.934 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:34.446 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:35.544 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:36.013 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:36.277 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:37.935 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:38.107 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:38.869 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:38.912 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:39.934 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:40.588 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:31:41.780 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-23 10:31:41.780 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-23 10:31:42.606 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-23 10:31:42.822 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:31:42.823 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-23 10:31:42.823 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:31:43.056 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-23 10:31:43.067 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-23 10:31:43.070 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-23 10:31:43.071 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-23 10:31:43.110 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-23 10:31:43.253 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 26.792 seconds (JVM running for 28.256)
2025-07-23 10:31:43.258 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:31:43.260 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:31:43.261 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:31:43.261 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:31:43.262 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:31:43.262 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:31:43.262 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:31:43.262 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:31:44.124 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-23 10:31:44.129 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-07-23 10:31:52.135 [http-nio-6091-exec-2] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 10:31:52.135 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 10:31:52.137 [http-nio-6091-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-23 10:35:16.421 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-23 10:35:16.421 [Thread-67] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-23 10:35:16.421 [Thread-67] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-23 10:35:16.422 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-23 10:35:26.102 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-23 10:35:26.122 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-23 10:35:26.123 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-23 10:35:27.673 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 10:35:27.675 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:35:27.828 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 142 ms. Found 0 Redis repository interfaces.
2025-07-23 10:35:28.246 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-23 10:35:28.642 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$73852434] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.670 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.678 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.681 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.686 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.693 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.695 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.697 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.704 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.714 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.762 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:28.782 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:35:29.180 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-23 10:35:29.499 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-23 10:35:29.507 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-23 10:35:29.508 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:35:29.508 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-23 10:35:29.743 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:35:29.744 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3621 ms
2025-07-23 10:35:30.210 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:30.713 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:35:32.031 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:35:38.381 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:38.390 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:38.704 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:39.409 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:39.912 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-23 10:35:40.215 [redisson-netty-4-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:35:40.225 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:35:40.521 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:40.536 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:41.053 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:42.167 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:42.574 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:42.787 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:44.423 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:44.642 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:45.500 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:45.549 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:46.596 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:47.271 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:35:48.436 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-23 10:35:48.437 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-23 10:35:49.356 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-23 10:35:49.588 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:35:49.589 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-23 10:35:49.589 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:35:49.800 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-23 10:35:49.813 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-23 10:35:49.816 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-23 10:35:49.817 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-23 10:35:49.854 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-23 10:35:50.034 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 25.703 seconds (JVM running for 27.189)
2025-07-23 10:35:50.040 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:35:50.042 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:35:50.043 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:35:50.043 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:35:50.043 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:35:50.043 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:35:50.044 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:35:50.044 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:35:50.932 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:35:50.958 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:36:00.118 [http-nio-6091-exec-3] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 10:36:00.118 [http-nio-6091-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 10:36:00.120 [http-nio-6091-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-23 10:36:00.157 [http-nio-6091-exec-3] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 0, sopBaseId: '1947839596864196609', executeTargetAttachId: '1947839597325467649'
2025-07-23 10:36:00.157 [http-nio-6091-exec-3] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1947839596864196609, executeTargetAttachId: 1947839597325467649
2025-07-23 10:36:00.164 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, 执行状态: 0, SOP基础ID: 1947839596864196609, 执行目标附件ID: 1947839597325467649
2025-07-23 10:36:00.164 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 执行状态: 0, 业务类型: 7, SOP基础ID: 1947839596864196609
2025-07-23 10:36:00.466 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1947839596864196609, 名称: '拨打电话-testA-14', 业务类型: 7, 状态: 1
2025-07-23 10:36:00.509 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 0
2025-07-23 10:36:00.509 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 指定状态查询无结果，尝试查询所有状态的数据...
2025-07-23 10:36:00.533 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询所有状态的特定SOP任务数量: 1
2025-07-23 10:36:00.534 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 1
2025-07-23 10:36:00.534 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1947839596864196609, SOP名称: '拨打电话-testA-14', 执行状态: 1, 附件ID: 1947839597325467649
2025-07-23 10:36:00.534 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 1
2025-07-23 10:36:00.535 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-23 10:36:00.535 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-23 10:36:00.535 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-23 10:36:00.535 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1947839596864196609, 时间段数量=1
2025-07-23 10:36:00.562 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Wed Jul 23 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1947839597325467649, callStatus=0 (待拨打)
2025-07-23 10:36:00.563 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-23 10:36:00.563 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回1个客户的特定拨打电话SOP任务
2025-07-23 10:36:00.563 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='拨打电话-testA-14', 拨打状态=0
2025-07-23 10:36:43.709 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 0, sopBaseId: '1947839596864196609', executeTargetAttachId: '1947839597325467649'
2025-07-23 10:36:48.420 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1947839596864196609, executeTargetAttachId: 1947839597325467649
2025-07-23 10:36:57.016 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, 执行状态: 0, SOP基础ID: 1947839596864196609, 执行目标附件ID: 1947839597325467649
2025-07-23 10:37:06.634 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 执行状态: 0, 业务类型: 7, SOP基础ID: 1947839596864196609
2025-07-23 10:37:14.072 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1947839596864196609, 名称: '拨打电话-testA-14', 业务类型: 7, 状态: 1
2025-07-23 10:38:07.624 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 0
2025-07-23 10:38:28.458 [com.alibaba.nacos.naming.beat.sender] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='DEFAULT_GROUP@@scrm-web-api', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-23 10:38:29.566 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 指定状态查询无结果，尝试查询所有状态的数据...
2025-07-23 10:38:32.816 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - removed ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:38:32.819 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(0) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> []
2025-07-23 10:38:35.398 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询所有状态的特定SOP任务数量: 1
2025-07-23 10:38:49.162 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:38:49.586 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 1
2025-07-23 10:38:49.587 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:38:50.327 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1947839596864196609, SOP名称: '拨打电话-testA-14', 执行状态: 1, 附件ID: 1947839597325467649
2025-07-23 10:38:53.791 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 1
2025-07-23 10:38:54.289 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-23 10:38:57.435 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-23 10:38:57.911 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-23 10:38:57.912 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1947839596864196609, 时间段数量=1
2025-07-23 10:38:57.957 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Wed Jul 23 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1947839597325467649, callStatus=0 (待拨打)
2025-07-23 10:38:57.957 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-23 10:39:00.785 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回1个客户的特定拨打电话SOP任务
2025-07-23 10:39:01.151 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='拨打电话-testA-14', 拨打状态=0
2025-07-23 10:44:08.830 [Thread-65] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-23 10:44:08.831 [Thread-65] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-23 10:44:08.832 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-23 10:44:08.833 [Thread-4] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
2025-07-23 10:46:50.489 [main] INFO  org.scrm.ScrmWeApiApplication - The following 1 profile is active: "dev"
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 52:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 29:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'file [config\bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: URL [file:config/bootstrap.yml] - 6:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 52:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 29:13]
2025-07-23 10:46:50.513 [main] WARN  o.s.b.c.config.ConfigDataEnvironment - Property 'spring.profiles' imported from location 'class path resource [bootstrap.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [bootstrap.yml] - 6:13]
2025-07-23 10:46:52.380 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-23 10:46:52.383 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-23 10:46:52.543 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 145 ms. Found 0 Redis repository interfaces.
2025-07-23 10:46:52.950 [main] INFO  o.s.cloud.context.scope.GenericScope - BeanFactory id=afd84fc0-9a9f-34e7-b88b-a99795437a37
2025-07-23 10:46:53.394 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [org.scrm.base.config.RedisConfig$$EnhancerBySpringCGLIB$$7b39a17c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.419 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.dtflys.forest.springboot.ForestAutoConfiguration' of type [com.dtflys.forest.springboot.ForestAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestProperties' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.432 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestObjectFactory' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.450 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestInterceptorFactory' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.456 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.458 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.459 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$520/148815426] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.466 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.474 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forest-com.dtflys.forest.springboot.properties.ForestConfigurationProperties' of type [com.dtflys.forest.springboot.properties.ForestConfigurationProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.524 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConfiguration' of type [com.dtflys.forest.config.ForestConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.540 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'forestConverterBeanListener' of type [com.dtflys.forest.spring.ConverterBeanListener] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-23 10:46:53.931 [main] WARN  c.d.f.scanner.ClassPathClientScanner - [Forest] No Forest client is found in package '[org.scrm]'.
2025-07-23 10:46:54.409 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 6091 (http)
2025-07-23 10:46:54.431 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-6091"]
2025-07-23 10:46:54.432 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-23 10:46:54.432 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-23 10:46:54.686 [main] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring embedded WebApplicationContext
2025-07-23 10:46:54.686 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4173 ms
2025-07-23 10:46:55.176 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:46:55.642 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-23 10:46:57.075 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-23 10:47:07.293 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-file' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:07.303 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:07.632 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:08.451 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:09.024 [main] INFO  org.redisson.Version - Redisson 3.20.0
2025-07-23 10:47:09.408 [redisson-netty-4-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:47:09.415 [redisson-netty-4-13] INFO  o.r.c.pool.MasterConnectionPool - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 10:47:09.675 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:09.687 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:10.268 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:11.358 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:11.853 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-system' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:12.217 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:13.834 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:14.041 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:14.478 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:14.524 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:15.802 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:16.395 [main] INFO  o.s.c.o.FeignClientFactoryBean - For 'scrm-wechat-api' URL not provided. Will try picking an instance via load-balancing.
2025-07-23 10:47:17.423 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultDispatcherProvider
2025-07-23 10:47:17.424 [main] INFO  i.g.l.ai4j.utils.ServiceLoaderUtil - Loaded SPI implementation: DefaultConnectionPoolProvider
2025-07-23 10:47:18.286 [main] WARN  o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-07-23 10:47:18.531 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:47:18.532 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Environment :null
2025-07-23 10:47:18.532 [main] INFO  com.alibaba.nacos.client.naming - initializer namespace from System Property :null
2025-07-23 10:47:18.747 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-6091"]
2025-07-23 10:47:18.761 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 6091 (http) with context path '/open'
2025-07-23 10:47:18.764 [main] INFO  com.alibaba.nacos.client.naming - [BEAT] adding beat: BeatInfo{port=6091, ip='**************', weight=1.0, serviceName='DEFAULT_GROUP@@scrm-web-api', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-07-23 10:47:18.766 [main] INFO  com.alibaba.nacos.client.naming - [REGISTER-SERVICE] 16f3da13-e0bb-4941-84c2-b352c7b39505 registering service DEFAULT_GROUP@@scrm-web-api with instance: Instance{instanceId='null', ip='**************', port=6091, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-07-23 10:47:18.803 [main] INFO  c.a.c.n.r.NacosServiceRegistry - nacos registry, DEFAULT_GROUP scrm-web-api **************:6091 register finished
2025-07-23 10:47:18.947 [main] INFO  org.scrm.ScrmWeApiApplication - Started ScrmWeApiApplication in 30.51 seconds (JVM running for 32.113)
2025-07-23 10:47:18.953 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:47:18.955 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:47:18.956 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-common.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:47:18.956 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-common.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:47:18.956 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:47:18.956 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:47:18.957 [main] INFO  c.a.n.c.config.impl.ClientWorker - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [subscribe] scrm-web-api-dev.yml+DEFAULT_GROUP+16f3da13-e0bb-4941-84c2-b352c7b39505
2025-07-23 10:47:18.957 [main] INFO  c.a.n.client.config.impl.CacheData - [fixed-121.40.36.84_8848-16f3da13-e0bb-4941-84c2-b352c7b39505] [add-listener] ok, tenant=16f3da13-e0bb-4941-84c2-b352c7b39505, dataId=scrm-web-api-dev.yml, group=DEFAULT_GROUP, cnt=1
2025-07-23 10:47:19.850 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - new ips(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:47:19.857 [com.alibaba.nacos.client.naming.updater] INFO  com.alibaba.nacos.client.naming - current ips:(1) service: DEFAULT_GROUP@@scrm-web-api@@DEFAULT -> [{"instanceId":"**************#6091#DEFAULT#DEFAULT_GROUP@@scrm-web-api","ip":"**************","port":6091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scrm-web-api","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-23 10:47:24.265 [http-nio-6091-exec-1] INFO  o.a.c.c.C.[.[localhost].[/open] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-23 10:47:24.266 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-23 10:47:24.269 [http-nio-6091-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-23 10:47:29.495 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 0, sopBaseId: '1947839596864196609', executeTargetAttachId: '1947839597325467649'
2025-07-23 10:47:31.543 [http-nio-6091-exec-1] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1947839596864196609, executeTargetAttachId: 1947839597325467649
2025-07-23 10:47:33.849 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1947839596864196609, 执行目标附件ID: 1947839597325467649
2025-07-23 10:47:36.814 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1947839596864196609
2025-07-23 10:47:42.714 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1947839596864196609, 名称: '拨打电话-testA-14', 业务类型: 7, 状态: 1
2025-07-23 10:47:47.910 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 1
2025-07-23 10:48:02.338 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 1
2025-07-23 10:48:02.344 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1947839596864196609, SOP名称: '拨打电话-testA-14', 执行状态: 1, 附件ID: 1947839597325467649
2025-07-23 10:48:02.348 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 1
2025-07-23 10:48:02.350 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-23 10:48:02.351 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-23 10:48:02.353 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-23 10:48:02.355 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1947839596864196609, 时间段数量=1
2025-07-23 10:48:02.394 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Wed Jul 23 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1947839597325467649, callStatus=0 (待拨打)
2025-07-23 10:48:02.395 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-23 10:48:02.396 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回1个客户的特定拨打电话SOP任务
2025-07-23 10:48:02.396 [http-nio-6091-exec-1] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='拨打电话-testA-14', 拨打状态=0
2025-07-23 10:49:35.337 [http-nio-6091-exec-3] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 0, sopBaseId: '1947839483945144322', executeTargetAttachId: '1947839492811800577'
2025-07-23 10:49:35.337 [http-nio-6091-exec-3] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1947839483945144322, executeTargetAttachId: 1947839492811800577
2025-07-23 10:49:35.882 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1947839483945144322, 执行目标附件ID: 1947839492811800577
2025-07-23 10:49:35.882 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1947839483945144322
2025-07-23 10:49:35.926 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1947839483945144322, 名称: '拨打电话-testB-14', 业务类型: 7, 状态: 1
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1947839483945144322, SOP名称: '拨打电话-testB-14', 执行状态: 1, 附件ID: 1947839492811800579
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1947839483945144322, SOP名称: '拨打电话-testB-14', 执行状态: 1, 附件ID: 1947839492811800577
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-23 10:49:35.950 [http-nio-6091-exec-3] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1947839483945144322) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-23 10:49:35.951 [http-nio-6091-exec-3] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-23 10:49:35.951 [http-nio-6091-exec-3] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-23 10:49:35.951 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-23 10:49:35.951 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-23 10:49:35.951 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1947839483945144322, 时间段数量=1
2025-07-23 10:49:35.972 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Wed Jul 23 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1947839492811800579, callStatus=0 (待拨打)
2025-07-23 10:49:35.973 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-23 10:49:35.973 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-23 10:49:35.973 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1947839483945144322, 时间段数量=1
2025-07-23 10:49:35.993 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Wed Jul 23 00:00:00 CST 2025 的拨打状态: executeTargetAttachId=1947839492811800577, callStatus=0 (待拨打)
2025-07-23 10:49:35.993 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-23 10:49:35.993 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-23 10:49:35.993 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='拨打电话-testB-14', 拨打状态=0
2025-07-23 10:49:35.993 [http-nio-6091-exec-3] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='拨打电话-testB-14', 拨打状态=0
2025-07-23 10:51:11.169 [http-nio-6091-exec-5] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 拨打电话SOP请求 - executeWeUserId: GanSha, executeSubState: 0, sopBaseId: '1947839483945144322', executeTargetAttachId: '1947839492811800578'
2025-07-23 10:51:11.170 [http-nio-6091-exec-5] INFO  org.scrm.controller.WeSopController - [SOP_DEBUG] 查询特定SOP的拨打电话数据: 1947839483945144322, executeTargetAttachId: 1947839492811800578
2025-07-23 10:51:11.348 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询特定拨打电话SOP内容，员工ID: GanSha, SOP基础ID: 1947839483945144322, 执行目标附件ID: 1947839492811800578
2025-07-23 10:51:11.348 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始查询，参数 - 员工ID: GanSha, 业务类型: 7, SOP基础ID: 1947839483945144322
2025-07-23 10:51:11.391 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] SOP基础信息 - ID: 1947839483945144322, 名称: '拨打电话-testB-14', 业务类型: 7, 状态: 1
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 查询到特定拨打电话SOP任务数量: 2
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 开始处理查询结果，总记录数: 2
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '.', 外部ID: 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 电话: '15860932307', SOP ID: 1947839483945144322, SOP名称: '拨打电话-testB-14', 执行状态: 1, 附件ID: 1947839492811800580
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 记录详情 - 客户: '干啥', 外部ID: 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 电话: '15659819768', SOP ID: 1947839483945144322, SOP名称: '拨打电话-testB-14', 执行状态: 1, 附件ID: 1947839492811800578
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 分组后的客户数量: 2
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg' (姓名: '.', 电话: '15860932307') 包含 1 条记录
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 'wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg' (姓名: '干啥', 电话: '15659819768') 包含 1 条记录
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 注意：该SOP (ID: 1947839483945144322) 匹配了 2 个客户，如果期望只有一个客户，请检查SOP配置
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', 姓名='.', 电话='15860932307'
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] WARN  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] - 匹配的客户: 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', 姓名='干啥', 电话='15659819768'
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 使用SQL直接查询的拨打状态
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 记录数: 1
2025-07-23 10:51:11.415 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=., externalUserid=wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg, sopBaseId=1947839483945144322, 时间段数量=1
2025-07-23 10:51:11.436 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '.' 时间段 Wed Jul 23 10:15:00 CST 2025 的拨打状态: executeTargetAttachId=1947839492811800580, callStatus=0 (待拨打)
2025-07-23 10:51:11.436 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: ., 电话: 15860932307, SOP内容数量: 1
2025-07-23 10:51:11.436 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 记录数: 1
2025-07-23 10:51:11.436 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户信息: 客户名=干啥, externalUserid=wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg, sopBaseId=1947839483945144322, 时间段数量=1
2025-07-23 10:51:11.460 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 客户 '干啥' 时间段 Wed Jul 23 10:15:00 CST 2025 的拨打状态: executeTargetAttachId=1947839492811800578, callStatus=0 (待拨打)
2025-07-23 10:51:11.461 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 处理客户: 干啥, 电话: 15659819768, SOP内容数量: 1
2025-07-23 10:51:11.461 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 构建完成，返回2个客户的特定拨打电话SOP任务
2025-07-23 10:51:11.461 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='.', 电话='15860932307', 外部ID='wm8tOmOgAAwPfTMkvvbeg8Ja73DPnKpg', SOP名称='拨打电话-testB-14', 拨打状态=0
2025-07-23 10:51:11.461 [http-nio-6091-exec-5] INFO  o.s.s.impl.WeSopBaseServiceImpl - [PHONE_CALL_SOP_DEBUG] 返回客户: 姓名='干啥', 电话='15659819768', 外部ID='wm8tOmOgAA6h7MhH3NZX0nXNnANZ5oSg', SOP名称='拨打电话-testB-14', 拨打状态=0
2025-07-23 10:59:57.270 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-23 10:59:57.270 [Thread-73] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Start destroying Publisher
2025-07-23 10:59:57.270 [Thread-73] WARN  c.a.nacos.common.notify.NotifyCenter - [NotifyCenter] Destruction of the end
2025-07-23 10:59:57.271 [Thread-5] WARN  c.a.n.c.http.HttpClientBeanHolder - [HttpClientBeanHolder] Destruction of the end
