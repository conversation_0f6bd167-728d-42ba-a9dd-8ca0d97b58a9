(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-46f2181e"],{"2b91":function(t,e,s){"use strict";s.d(e,"l",(function(){return l})),s.d(e,"j",(function(){return o})),s.d(e,"d",(function(){return d})),s.d(e,"a",(function(){return f})),s.d(e,"c",(function(){return m})),s.d(e,"k",(function(){return p})),s.d(e,"i",(function(){return b})),s.d(e,"m",(function(){return h})),s.d(e,"o",(function(){return y})),s.d(e,"n",(function(){return g})),s.d(e,"p",(function(){return v})),s.d(e,"e",(function(){return _})),s.d(e,"b",(function(){return j})),s.d(e,"h",(function(){return O})),s.d(e,"g",(function(){return q})),s.d(e,"f",(function(){return C}));var a=s("b775");const{get:n,post:r,put:u,del:i}=a["b"],c="/leads";function l(){return Object(a["b"])({url:"/sea/manager/list",method:"get"})}function o(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(a["b"])({url:c+"/sea/list",params:t})}function d(){return Object(a["b"])({url:c+"/template/settings/editable"})}function f(t){return t.leadsId=t.leadId,Object(a["b"])({url:c+"/manual/add",method:"post",data:t})}function m(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(a["b"])({url:c+"/manual/list",params:t})}function p(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(a["b"])({url:c+"/my/follow",params:t})}function b(t){return Object(a["b"])({url:c+"/get/"+t,method:"get"})}function h(t){return Object(a["b"])({url:c+"/receive",method:"get",params:{leadsId:t}})}function y(t){return t.leadsId=t.leadId,Object(a["b"])({url:c+"/user/return",data:t,method:"post"})}function g(t){return t.leadsId=t.leadId,Object(a["b"])({url:c+"/update",data:t,method:"put"})}function v(t){return t.leadsId=t.leadId,Object(a["b"])({url:c+"/bind/customer",method:"post",data:t})}function _(){return Object(a["b"])({url:c+"/follow/record/getFollowMode",method:"get"})}function j(t){return Object(a["b"])({url:c+"/follow/record/addFollow",data:t,method:"post"})}function O(t){return Object(a["b"])({url:c+"/follow/record/getFollowUpList/"+t,method:"get"})}function q(t){return t=Object.assign({pageNum:1,pageSize:10},t),Object(a["b"])({url:c+"/follow/record/list",params:t,method:"get"})}function C(t){return Object(a["b"])({url:c+"/follow/record/"+t})}},3585:function(t,e,s){"use strict";s("bb29")},"66ba":function(t,e,s){},"748c":function(t,e,s){"use strict";s("66ba")},bb29:function(t,e,s){},ed99:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"index"},[e("van-search",{attrs:{shape:"round",placeholder:"请输入线索客户名称",clearable:!1,"show-action":""},on:{input:t.search},scopedSlots:t._u([{key:"action",fn:function(){return[e("div",{on:{click:function(e){return t.search()}}},[t._v("搜索")])]},proxy:!0}]),model:{value:t.query.name,callback:function(e){t.$set(t.query,"name",e)},expression:"query.name"}}),e("van-tabs",{attrs:{"swipe-threshold":2},on:{click:e=>t.query.seaId=e}},t._l(t.list,(function(s,a){return e("van-tab",{key:a,attrs:{name:s.id,title:s.name}},["my"==s.id?e("van-tabs",{on:{click:e=>t.query.status=e}},t._l(t.statusDict,(function(s,a){return e("van-tab",{key:a,attrs:{name:a,title:s}},[e("SeaCard",{ref:"listMy"+a,refInFor:!0,attrs:{query:t.query}})],1)})),1):e("SeaCard",{ref:"list"+s.id,refInFor:!0,attrs:{query:t.query}})],1)})),1)],1)},n=[],r=function(){var t=this,e=t._self._c;return e("PullRefreshScrollLoadList",{ref:"pullRefreshScrollLoadList",staticStyle:{padding:"10px"},attrs:{request:"my"==t.query.seaId?t.getMyList:t.getList,dealQueryFun:e=>Object.assign(e,t.query)},scopedSlots:t._u([{key:"default",fn:function(s){return t._l(s,(function(s,a){return e("div",{key:a,staticClass:"card",on:{click:function(e){return t.cardClick(s)}}},[0==s.leadsStatus?e("div",{staticClass:"card-new"},[t._v("new")]):t._e(),e("div",{staticClass:"card-custom flex"},[e("div",{staticClass:"custom-right"},[e("div",{staticClass:"custom-name toe",domProps:{innerHTML:t._s(t.query.name?s.name.replace(t.query.name,`<span class='heighlight'>${t.query.name}</span>`):s.name)}}),"my"==t.query.seaId?e("div",{staticClass:"custom-info"},[e("span",{staticClass:"key"},[t._v(t._s({1:"领取",2:"领取",3:"退回"}[s.leadsStatus])+"时间")]),e("span",{staticClass:"value"},[t._v(t._s(s.updateTime))])]):[s.preFollowerName?e("div",{staticClass:"custom-info"},[e("span",{staticClass:"key"},[t._v("前跟进人")]),e("span",{staticClass:"value"},[t._v(t._s(s.preFollowerName))])]):t._e(),e("div",{staticClass:"custom-info"},[e("span",{staticClass:"key"},[t._v(t._s({0:"投放",3:"回收"}[s.leadsStatus])+"时间")]),e("span",{staticClass:"value"},[t._v(t._s(0===s.leadsStatus?s.createTime:s.updateTime))])])]],2)]),s.labelsNames?e("div",{staticClass:"card-tag"},t._l(s.labelsNames.split(",").slice(0,6),(function(s,a){return e("div",{key:a,staticClass:"card-tag-item"},[t._v(" "+t._s(s)+" ")])})),0):t._e()])}))}}])})},u=[],i=(s("14d9"),s("2b91")),c=s("ed08"),l={components:{},props:{query:{type:Object,default:()=>({})},isChange:{type:Boolean,default:!1}},watch:{},data(){return{getList:i["j"],getMyList:i["k"],dateFormat:c["b"]}},methods:{cardClick(t){this.$router.push({name:"clueDetail",query:{id:t.id,isDrainage:!0}})}}},o=l,d=(s("748c"),s("2877")),f=Object(d["a"])(o,r,u,!1,null,"62e08194",null),m=f.exports,p={components:{SeaCard:m},data(){return{loading:!1,query:{name:"",seaId:"my",status:1},statusDict:{1:"跟进中",3:"已退回",2:"已转化"},list:[]}},created(){this.getSeaList()},methods:{search(){"my"==this.query.seaId?this.$refs["listMy"+this.query.status][0].$refs.pullRefreshScrollLoadList.getList(1):this.$refs["list"+this.query.seaId][0].$refs.pullRefreshScrollLoadList.getList(1)},getSeaList(){this.loading=!0,Object(i["l"])(this.query).then(({data:t})=>{this.list=[{name:"我的跟进",id:"my"}].concat(t),this.query.seaId=this.list[0].id}).finally(()=>{this.loading=!1})}}},b=p,h=(s("3585"),Object(d["a"])(b,a,n,!1,null,"70d1fde4",null));e["default"]=h.exports}}]);