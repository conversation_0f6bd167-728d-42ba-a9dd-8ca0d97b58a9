<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSopChangeMapper">

    <resultMap id="BaseResultMap" type="org.scrm.domain.WeSopChange">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="externalUserid" column="external_userid" jdbcType="VARCHAR"/>
            <result property="addUserId" column="add_user_id" jdbcType="VARCHAR"/>
            <result property="sopBaseId" column="sop_base_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,external_userid,add_user_id,
        sop_base_id,create_by,create_by_id,
        create_time,update_by,update_by_id,
        update_time,del_flag
    </sql>
</mapper>
