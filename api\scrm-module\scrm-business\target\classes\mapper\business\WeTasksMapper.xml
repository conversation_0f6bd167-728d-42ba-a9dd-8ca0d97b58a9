<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeTasksMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.task.entity.WeTasks">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="we_user_id" property="weUserId"/>
        <result column="type" property="type"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="send_time" property="sendTime"/>
        <result column="url" property="url"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_by_id" property="updateById"/>
        <result column="create_by_id" property="createById"/>
        <result column="del_flag" property="delFlag"/>
        <result column="is_visible" property="visible"/>
        <result column="leads_id" property="leadsId"/>
        <result column="record_id" property="recordId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, we_user_id, type, title, content, send_time, url,
         `status`, create_time, update_time, create_by, update_by,
        update_by_id, create_by_id, del_flag,is_visible,leads_id,record_id
    </sql>

</mapper>
