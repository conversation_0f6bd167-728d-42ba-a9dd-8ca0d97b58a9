<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeMomentsEstimateUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.scrm.domain.moments.entity.WeMomentsEstimateUser">
        <id column="id" property="id"/>
        <result column="moments_task_id" property="momentsTaskId"/>
        <result column="user_id" property="userId"/>
        <result column="we_user_id" property="weUserId"/>
        <result column="user_name" property="userName"/>
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="execute_count" property="executeCount"/>
        <result column="execute_status" property="executeStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, moments_task_id, user_id, we_user_id, user_name, dept_id, dept_name, execute_count,execute_status
    </sql>


    <select id="getNonExecuteUser" resultType="org.scrm.domain.moments.entity.WeMomentsEstimateUser">
        SELECT t1.*
        FROM we_moments_estimate_user t1
                 LEFT JOIN we_moments_user t2 ON t1.moments_task_id = t2.moments_task_id
            AND t1.we_user_id = t2.we_user_id
        WHERE t2.id IS NULL
          and t1.moments_task_id = #{weMomentsTaskId}
    </select>

    <select id="getExecuteUsers" resultType="org.scrm.domain.moments.vo.WeMomentsUserVO">
        SELECT t1.id,
            t1.moments_task_id,
            t1.user_id,
            t1.we_user_id as weUserId,
            t1.user_name as userName,
            t1.dept_id as deptId,
            GROUP_CONCAT(DISTINCT d.dept_name) as deptName,
            t1.execute_count as executeCount,
            t1.execute_status as executeStatus
        FROM we_moments_estimate_user t1
        LEFT JOIN we_moments_user t2
        ON t1.moments_task_id = t2.moments_task_id AND t1.we_user_id = t2.we_user_id
        LEFT JOIN sys_user su on su.user_id=t1.user_id
        LEFT JOIN sys_user_dept sud ON sud.user_id=su.user_id and sud.del_flag=0
        left join sys_dept d on sud.dept_id = d.dept_id
        WHERE t1.moments_task_id = #{weMomentsTaskId}
        <if test="weUserIds!=null and weUserIds !=''">
            and t1.we_user_id in
            <foreach collection="weUserIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds !=''">
            and d.dept_id in
            <foreach collection="deptIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="status!=null">
             AND t1.execute_status = #{status}
        </if>
        GROUP BY  t1.we_user_id
    </select>

    <select id="countExecuteUsers" resultType="long">
        SELECT count(DISTINCT t1.we_user_id)
        FROM we_moments_estimate_user t1
        LEFT JOIN we_moments_user t2
        ON t1.moments_task_id = t2.moments_task_id AND t1.we_user_id = t2.we_user_id
        LEFT JOIN sys_user su on su.user_id=t1.user_id
        LEFT JOIN sys_user_dept sud ON sud.user_id=su.user_id and sud.del_flag=0
        left join sys_dept d on sud.dept_id = d.dept_id
        WHERE t1.moments_task_id = #{weMomentsTaskId}
        <if test="weUserIds!=null and weUserIds !=''">
            and t1.we_user_id in
            <foreach collection="weUserIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="deptIds!=null and deptIds !=''">
            and d.dept_id in
            <foreach collection="deptIds.split(',')" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="beginTime !=null and beginTime != '' and endTime !='' and endTime != null">
            AND  date_format(t1.update_time,'%Y-%m-%d') BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="status!=null">
            AND t1.execute_status = #{status}
        </if>
    </select>

</mapper>
