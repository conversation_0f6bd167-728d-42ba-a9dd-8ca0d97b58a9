<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeSensitiveMapper">



    <resultMap type="org.scrm.domain.WeSensitive" id="WeSensitiveResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="strategyName" column="strategy_name" jdbcType="VARCHAR"/>
        <result property="patternWords" column="pattern_words" jdbcType="VARCHAR"/>
        <result property="auditUserId" column="audit_user_id" jdbcType="VARCHAR"/>
        <result property="auditUserName" column="audit_user_name" jdbcType="VARCHAR"/>
        <result property="alertFlag" column="alert_flag" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateById" column="update_by_id" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <collection property="auditUserScope" ofType="org.scrm.domain.WeSensitiveAuditScope">
            <result property="scopeType" column="scope_type"/>
            <result property="auditScopeId" column="audit_scope_id"/>
            <result property="auditScopeName" column="audit_scope_name"/>
        </collection>
    </resultMap>

    <sql id="selectWeSensitiveVo">
        select id,
               strategy_name,
               pattern_words,
               audit_user_id,
               audit_user_name,
               alert_flag,
               create_time,
               create_by,
               create_by_id,
               update_time,
               update_by,
               update_by_id,
               del_flag
        from we_sensitive
    </sql>

    <sql id="selectWeSensitiveList">
        select a.id,
               a.strategy_name,
               a.pattern_words,
               a.audit_user_id,
               a.audit_user_name,
               a.alert_flag,
               a.create_time,
               a.create_by,
               a.create_by_id,
               a.update_time,
               a.update_by,
               a.update_by_id,
               a.del_flag,
               (SELECT b.audit_scope_id from we_sensitive_audit_scope b WHERE a.id = b.sensitive_id and b.del_flag = 0) as audit_scope_id ,
               (SELECT b.audit_scope_name from we_sensitive_audit_scope b WHERE a.id = b.sensitive_id and b.del_flag = 0) as audit_scope_name,
               (SELECT b.scope_type from we_sensitive_audit_scope b WHERE a.id = b.sensitive_id and b.del_flag = 0) as scope_type
        from we_sensitive a
    </sql>

    <select id="selectWeSensitiveList" parameterType="org.scrm.domain.WeSensitive" resultMap="WeSensitiveResult">
        <include refid="selectWeSensitiveList"/>
        where
          a.del_flag = 0
            <if test="strategyName != null  and strategyName != ''">and a.strategy_name like concat('%',
                #{strategyName}, '%')
            </if>
            <if test="patternWords != null  and patternWords != ''">and a.pattern_words like concat('%',
                #{patternWords}, '%')
            </if>
            <if test="auditUserId != null and auditUserId != ''">and a.audit_user_id = #{auditUserId}</if>
            <if test="auditUserName != null and auditUserName != ''">and a.audit_user_name like concat('%',
                #{auditUserName}, '%')
            </if>
            <if test="alertFlag != null">
                and a.alert_flag=#{alertFlag}
            </if>
        order by a.update_time desc, a.create_time desc
    </select>

    <select id="selectWeSensitiveById" parameterType="Long" resultMap="WeSensitiveResult">
        <include refid="selectWeSensitiveList"/>
        where a.id = #{id} and a.del_flag = 0
    </select>

    <select id="selectWeSensitiveByIds" parameterType="String" resultMap="WeSensitiveResult">
        <include refid="selectWeSensitiveList"/>
        where a.id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and a.del_flag = 0
        order by a.update_time desc, a.create_time desc
    </select>


    <update id="batchUpdateWeSensitive" parameterType="java.util.List">
        update we_sensitive
        <trim prefix="SET" suffixOverrides=",">
            <trim prefix="strategy_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.strategyName != null and item.strategyName != ''">
                        when id = #{item.id} then #{item.strategyName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="pattern_words = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.patternWords != null and item.patternWords != ''">
                        when id = #{item.id} then #{item.patternWords}
                    </if>
                </foreach>
            </trim>
            <trim prefix="audit_user_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.auditUserId != null and auditUserId != ''">
                        when id = #{item.id} then #{item.auditUserId}
                    </if>
                </foreach>
            </trim>
            <trim prefix="audit_user_name = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.auditUserName != null and item.auditUserName != ''">
                        when id = #{item.id} then #{item.auditUserName}
                    </if>
                </foreach>
            </trim>
            <trim prefix="alert_flag = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.alertFlag != null">
                        when id = #{item.id} then #{item.alertFlag}
                    </if>
                </foreach>
            </trim>
            <trim prefix="del_flag = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.delFlag != null">
                        when id = #{item.id} then #{item.delFlag}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_by = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.createBy != null and item.createBy != ''">
                        when id = #{item.id} then #{item.createBy}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_by_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.createById != null">
                        when id = #{item.id} then #{item.createById}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.createTime != null">
                        when id = #{item.id} then #{item.createTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateBy != null and item.updateBy != ''">
                        when id = #{item.id} then #{item.updateBy}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by_id = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateById != null">
                        when id = #{item.id} then #{item.updateById}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateTime != null">
                        when id = #{item.id} then #{item.updateTime}
                    </if>
                </foreach>
            </trim>
        </trim>
        WHERE id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

</mapper>
