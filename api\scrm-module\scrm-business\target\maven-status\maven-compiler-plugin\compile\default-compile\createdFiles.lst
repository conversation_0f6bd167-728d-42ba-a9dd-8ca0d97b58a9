org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Files$File.class
org\scrm\domain\kf\query\WeKfScenesQuery.class
org\scrm\service\impl\WeMarketSubServiceImpl.class
org\scrm\domain\customer\vo\WeCustomerAddUserVo.class
org\scrm\domain\leads\leads\vo\WeLeadsUserFollowTop5VO.class
org\scrm\mapper\WeShortLinkPromotionTemplateMomentsMapper.class
org\scrm\domain\wecom\query\WeSuiteTokenQuery.class
org\scrm\domain\corp\query\WeCorpAccountQuery.class
org\scrm\service\IWeFormSurveyRadioService.class
org\scrm\domain\shortlink\dto\WeShortLinkPromotionMomentsDto$OtherContent.class
org\scrm\domain\shortlink\query\WeShortLinkAddQuery.class
org\scrm\service\IWeQrScopeService.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto$TransferDetailList.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Images$Image.class
org\scrm\domain\moments\vo\WeMomentsTaskVO.class
org\scrm\mapper\WeMomentsTaskMapper.class
org\scrm\domain\agent\query\WeAgentMsgQuery.class
org\scrm\domain\WeQiRuleManageStatistics.class
org\scrm\service\impl\WeMarketRecordServiceImpl.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery$Range$RangeBuilder.class
org\scrm\mapper\WeKeywordGroupTaskMapper.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerDetailVo$Range.class
org\scrm\service\impl\WeAiServiceImpl.class
org\scrm\domain\leads\leads\entity\WeLeadsFollower$WeLeadsFollowerBuilder.class
org\scrm\service\IWePeriodAttachmentsService.class
org\scrm\service\impl\strategic\shortlink\ClientPromotion.class
org\scrm\domain\moments\dto\MomentsParamDto$Video.class
org\scrm\service\IWeMarketPrizeService.class
org\scrm\service\IWeProductOrderService.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$WeSopExecuteGroupTagIdsCondit.class
org\scrm\handler\WeQualityWriteHandler.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCataloguePropertyMoveRequest.class
org\scrm\domain\wecom\vo\agentdev\WeTransformExternalUserIdVO.class
org\scrm\domain\wecom\query\customer\tag\WeCorpTagListQuery.class
org\scrm\mapper\WeMarketMapper.class
org\scrm\service\IWeKfNoticeLogService.class
org\scrm\service\IWeQiRuleMsgService.class
org\scrm\service\IWeGroupCodeService.class
org\scrm\service\impl\WeProductOrderServiceImpl.class
org\scrm\domain\form\vo\WeFormSurveyAnswerVO.class
org\scrm\domain\envelopes\WeRedEnvelopesRecord.class
org\scrm\domain\WeQrLoopNumber.class
org\scrm\mapper\WeEmpleCodeTagMapper.class
org\scrm\domain\product\product\vo\WeProductStatisticsVo.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyDetailVo.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$TrackStates.class
org\scrm\domain\PromotionEntity.class
org\scrm\domain\agent\query\WeAgentMsgAddQuery.class
org\scrm\domain\wecom\entity\customer\groupChat\WeGroupMemberEntity$Invitor.class
org\scrm\domain\WeKfUpgradeServer.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCataloguePropertyUpdateRequest.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery.class
org\scrm\domain\moments\dto\MomentsParamDto$VideoAttachments$VideoAttachmentsBuilder.class
org\scrm\domain\envelopes\vo\WeCutomerRedEnvelopesVo.class
org\scrm\mapper\WeKfInfoMapper.class
org\scrm\service\impl\CommonGroupMsgServiceImpl.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Text.class
org\scrm\service\IWeCustomerService.class
org\scrm\domain\wecom\entity\customer\tag\WeCorpTagEntity$WeCorpTagEntityBuilder.class
org\scrm\domain\shortlink\dto\WeShortLinkPromotionAppMsgDto.class
org\scrm\service\impl\WeGroupMessageSendResultServiceImpl.class
org\scrm\service\IWePhoneCallRecordService.class
org\scrm\domain\material\ao\WePosterFontAO.class
org\scrm\domain\wecom\query\customer\state\WeGroupChatStatisticQuery$OwnerFilter.class
org\scrm\service\IWeGroupMessageAttachmentsService.class
org\scrm\domain\qirule\query\WeQiRuleStatisticsTableListQuery.class
org\scrm\domain\groupchat\vo\WeGroupMemberVo.class
org\scrm\domain\wecom\vo\customer\UnionidToExternalUserIdVo.class
org\scrm\mapper\WeFissionMapper.class
org\scrm\service\impl\WeFormSurveySiteStasServiceImpl.class
org\scrm\domain\hotword\query\MsgAuditQuery.class
org\scrm\domain\taggroup\vo\WePresTagTaskListVo.class
org\scrm\mapper\WeAllocateCustomerMapper.class
org\scrm\domain\qirule\query\WeQiRuleListQuery.class
org\scrm\domain\moments\dto\VideoDto.class
org\scrm\domain\wecom\query\kf\QwKfListQuery.class
org\scrm\service\IWeRedEnvelopesRecordService.class
org\scrm\domain\kf\vo\WeKfAnswerAttachmentsVo.class
org\scrm\domain\wecom\query\customer\WeCustomerListQuery.class
org\scrm\service\impl\WeSvipGroupServiceImpl.class
org\scrm\service\impl\WeKnowCustomerCodeTagServiceImpl.class
org\scrm\domain\qirule\vo\WeQiRuleStatisticsTableVo.class
org\scrm\domain\wecom\vo\WeResultVo$WeResultVoBuilder.class
org\scrm\domain\WeLxQrCodeLog.class
org\scrm\domain\wecom\vo\agent\query\WeAgentQuery.class
org\scrm\mapper\WeChatContactSensitiveMsgMapper.class
org\scrm\domain\wecom\query\kf\WeKfMsgQuery.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatListVo.class
org\scrm\mapper\WeSopExecuteTargetAttachmentsMapper.class
org\scrm\domain\WeShortLinkPromotionTemplateMoments.class
org\scrm\domain\live\vo\WeLinveUserVo.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordAttachment.class
org\scrm\domain\sop\vo\WeSopExecuteEndVo$ExecuteTag.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Image$ImageBuilder.class
org\scrm\domain\groupcode\entity\WeGroupCodeTagRel.class
org\scrm\domain\message\entity\WeMessageNotification.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotPublishProgressVo.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotSignVo.class
org\scrm\mapper\WeLeadsFollowRecordContentMapper.class
org\scrm\domain\community\WeEmpleCode.class
org\scrm\domain\wecom\vo\third\auth\WePreAuthCodeVo.class
org\scrm\mapper\WeContentTalkMapper.class
org\scrm\domain\operation\vo\WeSessionArchiveDetailVo.class
org\scrm\domain\WeTaskFissionRecord$WeTaskFissionRecordBuilder.class
org\scrm\domain\form\query\WeFormSurveyCatalogueQuery.class
org\scrm\domain\wecom\vo\agentdev\WeTransformCorpVO.class
org\scrm\service\IWeKfPoolService.class
org\scrm\service\impl\WeContentViewRecordServiceImpl.class
org\scrm\domain\ai\WeAiTokenRecord.class
org\scrm\domain\sop\dto\WeSopBaseDto$WeSopBaseDtoBuilder.class
org\scrm\service\IWeGroupRobotMsgService.class
org\scrm\aop\ShortLinkViewAspect.class
org\scrm\mapper\WeQiRuleMsgNoticeMapper.class
org\scrm\domain\community\vo\WeEmplTaskVo$WeEmplTaskVoBuilder.class
org\scrm\fegin\QwKfClient.class
org\scrm\domain\moments\dto\MomentsParamDto$Link$LinkBuilder.class
org\scrm\domain\substitute\customer\order\vo\WeSubstituteCustomerOrderVO.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentDetailVo$AllowUser.class
org\scrm\domain\groupcode\vo\WeGroupCodeCountTrendVo.class
org\scrm\domain\storecode\entity\WeStoreCode$WeStoreCodeBuilder.class
org\scrm\domain\wecom\query\living\WeAddLivingQuery.class
org\scrm\domain\wecom\query\WeBaseQuery.class
org\scrm\domain\kf\vo\WeKfConsultRankCntVo.class
org\scrm\service\IWeLeadsTemplateTableEntryContentService.class
org\scrm\service\IWeKfScenesService.class
org\scrm\domain\wecom\query\customer\UpdateCustomerRemarkQuery$UpdateCustomerRemarkQueryBuilder.class
org\scrm\domain\wecom\query\living\WeGetLivingShareInfoQuery.class
org\scrm\service\IWeUserBehaviorDataService.class
org\scrm\domain\community\WeGroupSopMaterial$WeGroupSopMaterialBuilder.class
org\scrm\domain\moments\dto\ImageMessageDto.class
org\scrm\fegin\QwCustomerClient.class
org\scrm\mapper\WeLeadsFollowRecordAttachmentMapper.class
org\scrm\service\impl\WeLeadsFollowRecordServiceImpl.class
org\scrm\domain\WeQiRuleMsgNotice.class
org\scrm\service\impl\WeQiRuleMsgServiceImpl.class
org\scrm\domain\WeTag$WeTagBuilder.class
org\scrm\domain\kf\vo\WeKfConsultRankCntListVo.class
org\scrm\domain\form\query\WeAddFormSurveyAnswerQuery.class
org\scrm\service\IWeTasksService.class
org\scrm\domain\material\entity\WeTrackMaterialPrivacyAuth.class
org\scrm\service\IWeMomentsTaskService.class
org\scrm\domain\kf\vo\WeKfSceneRankCntListVo.class
org\scrm\domain\community\vo\WeCommunityNewGroupTrendCountVo.class
org\scrm\domain\community\vo\WeGroupCodeVo$WeGroupCodeVoBuilder.class
org\scrm\fegin\QwAuthClient.class
org\scrm\service\impl\WeSensitiveActHitServiceImpl.class
org\scrm\service\IWeQiRuleMsgNoticeService.class
org\scrm\mapper\WeLxQrScopeMapper.class
org\scrm\domain\msgaudit\vo\WeChatContactMsgVo.class
org\scrm\domain\wecom\vo\user\WeUserInviteVo.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatDetailVo$WeGroupAdmin.class
org\scrm\domain\wecom\vo\customer\WeBatchCustomerDetailVo.class
org\scrm\domain\community\WeGroupSopMaterial.class
org\scrm\domain\wecom\vo\department\WeAddDeptVo.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Text.class
org\scrm\domain\leads\sea\entity\WeLeadsSeaBaseSettings$WeLeadsSeaBaseSettingsBuilder.class
org\scrm\domain\WeKfMsg.class
org\scrm\domain\groupcode\vo\WeGroupCodeH5Vo.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesParmDto.class
org\scrm\service\impl\WeGroupMessageTaskServiceImpl.class
org\scrm\service\impl\WeMessageNotificationServiceImpl.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateAppMsgUpdateQuery.class
org\scrm\service\impl\WeShortLinkServiceImpl.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Images.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionVo.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatGetJoinWayVo.class
org\scrm\domain\WeQiRuleMsg.class
org\scrm\service\IWeCustomerLinkCountService.class
org\scrm\config\common\EnumConfig.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateAppMsgAddQuery.class
org\scrm\domain\wecom\query\customer\strategy\WeCustomerStrategyQuery.class
org\scrm\service\IWeQiRuleUserStatisticsService.class
org\scrm\domain\wecom\query\customer\msg\WeCancelGroupMsgSendQuery.class
org\scrm\domain\WeAgentInfo.class
org\scrm\domain\moments\dto\MomentsParamDto.class
org\scrm\domain\WeMarketPrize.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentListVo.class
org\scrm\mapper\WeLiveWatchUserMapper.class
org\scrm\domain\WeMsgTlp$ImageText.class
org\scrm\domain\qirule\vo\WeQiRuleScopeVo.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotAibotQuery.class
org\scrm\service\IWeLeadsAutoRecoveryService.class
org\scrm\domain\moments\query\WeMomentsSyncGroupSendRequest.class
org\scrm\domain\wecom\callback\third\WeThirdSuiteTicketVo.class
org\scrm\domain\moments\dto\MomentsParamDto$ImageAttachments.class
org\scrm\mapper\WeGroupMessageTaskMapper.class
org\scrm\domain\SysLeaveUser$SysLeaveUserBuilder.class
org\scrm\mapper\WeFissionNoticeMapper.class
org\scrm\domain\task\query\WeTasksRequest$WeTasksRequestBuilder.class
org\scrm\domain\WeLeaveUser.class
org\scrm\domain\WeGroupMember$WeGroupMemberBuilder.class
org\scrm\domain\WePeriodAttachments$WePeriodAttachmentsBuilder.class
org\scrm\mapper\WeKfScenesMapper.class
org\scrm\domain\wecom\vo\customer\seas\CustomerSeasRecordVo.class
org\scrm\mapper\WeFissionInviterPosterMapper.class
org\scrm\service\IWeKfUpgradeService.class
org\scrm\service\impl\WeMomentsTaskRelationServiceImpl.class
org\scrm\domain\fission\vo\WeTaskFissionDetailVo.class
org\scrm\algor\RoundWeQrScope.class
org\scrm\mapper\WeGroupCodeTagRelMapper.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Attachments.class
org\scrm\domain\wecom\query\customer\transfer\WeTransferGroupChatQuery.class
org\scrm\service\impl\WeHotWordServiceImpl.class
org\scrm\domain\moments\dto\MomentsParamDto$Link.class
org\scrm\domain\wecom\query\customer\msg\WeWelcomeMsgQuery.class
org\scrm\domain\wecom\query\product\QwProductQuery.class
org\scrm\service\IWeGroupService.class
org\scrm\domain\moments\query\WeMomentsTaskEstimateCustomerNumRequest$WeMomentsTaskEstimateCustomerNumRequestBuilder.class
org\scrm\domain\moments\entity\WeMomentsTask$WeMomentsTaskBuilder.class
org\scrm\domain\leads\leads\query\WeLeadsExportRequest.class
org\scrm\domain\qr\WeQrScope$WeQrScopeBuilder.class
org\scrm\service\impl\WeLeadsServiceImpl.class
org\scrm\service\IWeAnalysisHotWordService.class
org\scrm\domain\qr\query\WeQrCodeListQuery.class
org\scrm\mapper\WeAiTokenRecordMapper.class
org\scrm\domain\wecom\vo\living\WeLivingIdListVo.class
org\scrm\domain\WeShortLinkPromotionSendResult.class
org\scrm\service\IWeLeadsFollowerService.class
org\scrm\domain\system\config\vo\SysConfigVo$WxApp.class
org\scrm\domain\material\entity\WeContentTalk.class
org\scrm\service\IWeMomentsEstimateCustomerService.class
org\scrm\domain\leads\leads\vo\WeLeadsImportRecordVO.class
org\scrm\service\impl\WeQiRuleScopeServiceImpl.class
org\scrm\service\IWeLeadsFollowRecordService.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo$WeCustomerSop.class
org\scrm\service\IWeRedEnvelopesService.class
org\scrm\service\IWeMaterialService.class
org\scrm\service\impl\strategic\shortlink\MomentsPromotion.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Images.class
org\scrm\domain\wecom\vo\living\WeGetLivingShareInfoVo.class
org\scrm\mapper\WeCategoryMapper.class
org\scrm\mapper\WeLeaveInfoMapper.class
org\scrm\domain\qirule\query\WeQiRuleNoticeListQuery.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordContent$WeLeadsFollowRecordContentBuilder.class
org\scrm\service\AbstractGroupMsgSendTaskService.class
org\scrm\domain\qr\vo\WeLxQrCodeSheetVo$WeLxQrCodeSheetVoBuilder.class
org\scrm\mapper\WeShortLinkUserPromotionTaskMapper.class
org\scrm\service\impl\IWeQrAttachmentsServiceImpl.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesParmDto$WeRedEnvelopesParmDtoBuilder.class
org\scrm\mapper\WeSopPushTimeMapper.class
org\scrm\service\IWeGroupMemberService.class
org\scrm\domain\substitute\customer\order\entity\WeSubstituteCustomerOrderCatalogue.class
org\scrm\domain\WeGroupMessageSendResult.class
org\scrm\domain\strategic\crowd\query\WeStrategicCrowdQuery.class
org\scrm\domain\wecom\query\customer\tag\WeAddCorpTagQuery.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Miniprograms$Miniprogram.class
org\scrm\domain\material\ao\WeMaterialImgAo$WeMaterialImgAoBuilder.class
org\scrm\domain\fission\WeFissionInviterRecordSub.class
org\scrm\domain\wecom\entity\customer\strategy\WeCustomerStrategyRangeEntity.class
org\scrm\domain\wecom\query\merchant\WeGetBillListQuery.class
org\scrm\mapper\WeCustomerMapper.class
org\scrm\domain\leave\WeLeaveInfo$WeLeaveInfoBuilder.class
org\scrm\domain\WeGroupMessageTask.class
org\scrm\domain\side\vo\WeChatSideVo.class
org\scrm\service\impl\WeLeadsTemplateSettingsServiceImpl.class
org\scrm\domain\hotword\vo\IYqueAnalysisHotWordVo.class
org\scrm\domain\moments\dto\MomentsParamDto$VideoAttachments.class
org\scrm\domain\taggroup\WePresTagGroupTask.class
org\scrm\domain\WeShortLinkPromotionAttachment.class
org\scrm\domain\qr\WeQrScope.class
org\scrm\domain\WeSopChange$WeSopChangeBuilder.class
org\scrm\domain\moments\dto\MomentsParamDto$Image$ImageBuilderImpl.class
org\scrm\domain\qr\query\WeQrUserInfoDetailQuery.class
org\scrm\domain\WeStrackStage.class
org\scrm\domain\sop\vo\WeSopListsVo.class
org\scrm\domain\WeMsgTlpAttachments.class
org\scrm\service\IWeAiService.class
org\scrm\mapper\WeShortLinkPromotionSendResultMapper.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatUpdateJoinWayQuery.class
org\scrm\domain\community\WeKeywordGroupTask.class
org\scrm\service\impl\IWeQrScopeServiceImpl.class
org\scrm\service\impl\WeMsgTlpServiceImpl.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$WeSopExecuteGroupMemberLimitCondit.class
org\scrm\domain\moments\dto\CustomerMessagePushDto.class
org\scrm\service\IWeLiveService.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesResultDto.class
org\scrm\domain\wecom\query\living\WeGetLivingCodeQuery.class
org\scrm\domain\customer\WeMakeCustomerTag$WeMakeCustomerTagBuilder.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateMomentsAddQuery.class
org\scrm\domain\kf\query\WeKfQualityStatQuery.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Videos$Video.class
org\scrm\service\impl\WeFormSurveyCountServiceImpl.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$Location.class
org\scrm\service\impl\WeMarketPrizeServiceImpl.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Miniprograms$Miniprogram.class
org\scrm\domain\leads\record\query\WeLeadsFollowRecordRequest.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatAddJoinWayQuery.class
org\scrm\domain\groupchat\query\WeMakeGroupTagQuery$WeMakeGroupTagQueryBuilder.class
org\scrm\domain\WeCustomerInfoExpand$CustomerInfoExpand.class
org\scrm\domain\operation\vo\WeUserCustomerRankVo.class
org\scrm\domain\wecom\vo\customer\tag\WeCorpTagVo.class
org\scrm\service\impl\WeShortLinkPromotionDayStatServiceImpl.class
org\scrm\domain\qr\vo\WeLxQrCodeReceiveVo.class
org\scrm\domain\wecom\callback\third\WeThirdBackCustomerTagVo.class
org\scrm\domain\WeCommonLinkStat.class
org\scrm\mapper\WeMomentsCustomerMapper.class
org\scrm\domain\community\WeGroupSopPic.class
org\scrm\service\impl\WeFlowerCustomerTagRelServiceImpl.class
org\scrm\converter\WeMomentsUserStatusConverter.class
org\scrm\domain\msgaudit\vo\WeChatContactSensitiveMsgVo.class
org\scrm\domain\wecom\query\user\WeUserConvertQuery.class
org\scrm\domain\svipgroup\WeSvipGroup.class
org\scrm\service\IWeLeadsFollowRecordAttachmentService.class
org\scrm\domain\moments\query\WeMomentsSyncGroupSendMqRequest.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$MpNews.class
org\scrm\domain\moments\query\WeMomentsTaskEstimateCustomerNumRequest.class
org\scrm\mapper\WeCustomerLinkCountMapper.class
org\scrm\service\IWeMarketRecordService.class
org\scrm\mapper\WeGroupSopChatMapper.class
org\scrm\service\impl\WeAnalysisHotWordServiceImpl.class
org\scrm\domain\qr\vo\WeLxQrCodeListVo.class
org\scrm\service\impl\WeLeaveUserServiceImpl.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateClientUpdateQuery.class
org\scrm\domain\tag\vo\WeTagVo$WeTagVoBuilder.class
org\scrm\service\impl\WeFormSurveyRadioServiceImpl.class
org\scrm\domain\wecom\callback\WeBackDeptVo.class
org\scrm\service\impl\WeFormSurveyStatisticsServiceImpl.class
org\scrm\service\impl\WeLeadsFollowerServiceImpl.class
org\scrm\domain\wecom\query\product\QwProductListQuery.class
org\scrm\domain\sop\vo\WeSopDetailCustomerVo.class
org\scrm\domain\WeShortLinkUserPromotionTask.class
org\scrm\mapper\WeCustomerTrackRecordMapper.class
org\scrm\service\IWeAgentMsgService.class
org\scrm\domain\storecode\vo\datareport\WeStoreGroupReportVo.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyVo.class
org\scrm\domain\groupmsg\vo\WeGroupMessageListVo.class
org\scrm\domain\WeProductStatistics.class
org\scrm\service\impl\WeUserRedEnvelopsLimitServiceImpl.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity$MomentText.class
org\scrm\service\impl\WeAgentMsgServiceImpl.class
org\scrm\domain\wecom\query\living\WeLivingQuery.class
org\scrm\domain\WeKfUserStat.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$TextCard$TextCardBuilder.class
org\scrm\service\IWeEmpleCodeUseScopService.class
org\scrm\domain\moments\vo\MomentsSendResultVO$ExternalUserid.class
org\scrm\domain\kf\WeKfUser.class
org\scrm\domain\leads\sea\entity\WeLeadsSeaBaseSettings.class
org\scrm\service\impl\WeLeadsManualAddRecordServiceImpl.class
org\scrm\service\impl\WeShortLinkPromotionTemplateAppMsgServiceImpl.class
org\scrm\domain\moments\entity\WeMomentsCustomer.class
org\scrm\domain\wecom\query\kf\WeKfStateQuery.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery.class
org\scrm\domain\wecom\vo\customer\WeCustomerDetailVo$ExternalUserTag.class
org\scrm\service\impl\WePromotionTaskRecordServiceImpl.class
org\scrm\domain\sop\vo\content\WeGroupSopBaseContentVo.class
org\scrm\mapper\WeMarketSubMapper.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo$ExecuteUserCondit.class
org\scrm\domain\leads\leads\query\WeLeadsUserStatisticRequest.class
org\scrm\domain\WeMarketCountVo$MarketTab.class
org\scrm\domain\leads\leads\vo\WeLeadsFollowerVO.class
org\scrm\domain\storecode\vo\WeStoreCodeGroupTableVo.class
org\scrm\domain\WeMarketVo.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyListVo$CustomerStrategy.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatAddJoinWayVo.class
org\scrm\service\IWeMomentsTaskRelationService.class
org\scrm\domain\storecode\vo\datareport\WeStoreShopGuideReportVo.class
org\scrm\domain\WeTaskFissionRecord.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentDetailVo$AllowTags.class
org\scrm\domain\wecom\vo\third\auth\WeAuthAdminVo$Admin.class
org\scrm\domain\sop\WeSopBase.class
org\scrm\domain\wecom\vo\department\WeDeptIdVo.class
org\scrm\domain\wecom\vo\living\WeAddLivingVo.class
org\scrm\service\IWeAttachmentPeriodService.class
org\scrm\domain\wecom\query\department\WeDeptQuery.class
org\scrm\domain\qr\query\WeLxQrAddQuery.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo.class
org\scrm\domain\WeSynchRecord$WeSynchRecordBuilder.class
org\scrm\domain\leads\sea\entity\WeLeadsSeaVisibleRange.class
org\scrm\domain\wecom\query\third\auth\WeExtensionRegisterQuery.class
org\scrm\fallback\QwLivingFallbackFactory.class
org\scrm\domain\wecom\entity\department\WeDeptEntity.class
org\scrm\service\IWeKnowCustomerCodeTagService.class
org\scrm\mapper\WeShortLinkStatMapper.class
org\scrm\domain\storecode\vo\tab\WeStoreGroupTabVo.class
org\scrm\service\IWeQrCodeService.class
org\scrm\domain\wecom\callback\WeBackKfVo.class
org\scrm\domain\system\user\query\SysUserQuery.class
org\scrm\service\IWeKfAnswerGroupService.class
org\scrm\domain\material\vo\WeMaterialNewVo.class
org\scrm\service\impl\WeMomentsAttachmentsServiceImpl.class
org\scrm\mapper\WeSensitiveActHitMapper.class
org\scrm\service\impl\WeGroupMemberServiceImpl.class
org\scrm\service\IWeStrackStageService.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotSignQuery.class
org\scrm\service\impl\WeOperationCenterServiceImpl.class
org\scrm\domain\community\query\WeCommunityNewGroupQuery.class
org\scrm\domain\leads\sea\query\VisibleRange$DeptRange.class
org\scrm\domain\wecom\vo\customer\product\QwProductVo$QwProduct.class
org\scrm\domain\material\vo\MaterialMediaTypeVO$MaterialMediaTypeVOBuilder.class
org\scrm\service\impl\WeUserBehaviorDataServiceImpl.class
org\scrm\domain\wecom\vo\third\auth\WeAuthInfoVo$AuthInfo.class
org\scrm\domain\sop\vo\content\WeCustomerSopBaseContentVo.class
org\scrm\domain\wecom\vo\kf\WeKfSyncEventMsgVo.class
org\scrm\fegin\QwSysDeptClient.class
org\scrm\domain\leads\template\vo\WeLeadsTemplateSettingsVO.class
org\scrm\domain\product\analyze\vo\WeProductOrderDataReportVo.class
org\scrm\domain\WeCustomer.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$Link.class
org\scrm\service\IWeGroupTagRelService.class
org\scrm\domain\fission\WeFissionInviterRecord$WeFissionInviterRecordBuilder.class
org\scrm\domain\sop\vo\WeSopAttachmentVo.class
org\scrm\service\IWeMsgTlpAttachmentsService.class
org\scrm\domain\envelopes\dto\H5RedEnvelopesDetailDto$AccpestCustomer.class
org\scrm\domain\moments\entity\WeMomentsTask.class
org\scrm\mapper\WeSynchRecordMapper.class
org\scrm\domain\moments\entity\WeMomentsAttachments$WeMomentsAttachmentsBuilder.class
org\scrm\mapper\WeShortLinkPromotionTemplateAppMsgMapper.class
org\scrm\domain\qr\WeQrAttachments.class
org\scrm\service\IWeLeadsSeaRuleRecordService.class
org\scrm\mapper\WeLeadsImportRecordMapper.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCatalogueAddRequest.class
org\scrm\domain\customer\vo\WeCustomerLinkCountTrendVo.class
org\scrm\domain\live\WeLive$WeLiveBuilder.class
org\scrm\domain\MarketTable.class
org\scrm\mapper\WeKfServicerMapper.class
org\scrm\domain\form\query\WeAddFormSurveyCatalogueQuery.class
org\scrm\domain\wecom\query\customer\strategy\WeAddCustomerStrategyQuery.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatDetailQuery.class
org\scrm\domain\qr\vo\WeQrCodeDetailVo.class
org\scrm\domain\community\query\WeGroupSopQuery.class
org\scrm\domain\envelopes\vo\WeGroupRedEnvelopesVo.class
org\scrm\domain\groupchat\vo\WeGroupSimpleVo.class
org\scrm\domain\wecom\vo\kf\WeAddKfVo.class
org\scrm\domain\wecom\vo\third\auth\WeGetCustomizedAuthUrlVo.class
org\scrm\domain\moments\dto\MomentsParamDto$Image$ImageBuilder.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo$ExecuteDeptCondit.class
org\scrm\domain\wecom\callback\WeBackLiveVo.class
org\scrm\domain\wecom\vo\kf\WeCustomerInfoVo.class
org\scrm\mapper\WeMomentsUserMapper.class
org\scrm\domain\operation\vo\WeGroupAnalysisVo.class
org\scrm\service\impl\WeProductDayStatisticsServiceImpl.class
org\scrm\service\IWeGroupCodeTagRelService.class
org\scrm\domain\wecom\vo\third\auth\WeAuthInfoVo$DealerCorpInfo.class
org\scrm\domain\operation\vo\WeSessionCustomerTotalCntVo.class
org\scrm\domain\qr\vo\WeLxQrCodeSheetVo.class
org\scrm\domain\substitute\customer\order\vo\WeSubstituteCustomerOrderDetailVO.class
org\scrm\fallback\QwMediaFallbackFactory.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$TaskCard.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentResultVo$MomentResult.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaListVo.class
org\scrm\service\impl\WeQrCodeServiceImpl.class
org\scrm\domain\wecom\query\WxAppletBaseQuery.class
org\scrm\domain\material\ao\WeMaterialImgAo.class
org\scrm\fegin\QwSysUserClient.class
org\scrm\domain\wecom\callback\WeBackBaseVo.class
org\scrm\domain\form\query\WeFormSiteStasQuery.class
org\scrm\domain\moments\vo\WeMomentsEstimateCustomerVO.class
org\scrm\domain\wecom\query\customer\strategy\WeUpdateCustomerStrategyQuery.class
org\scrm\domain\sop\vo\WeSopExecuteEndVo$JoinCustomerGroup.class
org\scrm\domain\moments\entity\WeMomentsEstimateCustomer.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Voice$VoiceBuilder.class
org\scrm\config\rabbitmq\RabbitMQConfig.class
org\scrm\mapper\WeKfUpgradeMapper.class
org\scrm\domain\kf\vo\WeKfAnswerVo.class
org\scrm\domain\WeStrategicCrowd.class
org\scrm\service\impl\WeSubstituteCustomerOrderCatalogueServiceImpl.class
org\scrm\domain\wecom\vo\third\auth\WeAuthInfoVo$AuthUserInfo.class
org\scrm\domain\ai\AiMsgDTO.class
org\scrm\domain\wecom\query\user\WeLoginUserDetailQuery.class
org\scrm\mapper\WeUnionExternalUseridRelationMapper.class
org\scrm\domain\wecom\callback\third\WeThirdBackCustomerGroupVo.class
org\scrm\service\IWePresTagGroupTaskService.class
org\scrm\service\impl\WeKeywordGroupViewCountServiceImpl.class
org\scrm\domain\sop\vo\content\WeCustomerSopToBeSentVo$WeCustomerSopToBeSentVoBuilder.class
org\scrm\domain\WeKfMsgCursor.class
org\scrm\domain\wecom\vo\kf\WeKfStatisticListVo$KfStatistic.class
org\scrm\service\impl\WeTagServiceImpl.class
org\scrm\domain\kf\vo\WeKfInfoVo.class
org\scrm\service\IWeProductStatisticsService.class
org\scrm\mapper\WeKfEventMsgMapper.class
org\scrm\mapper\WeQrLoopNumberMapper.class
org\scrm\service\IWeCustomerTrackRecordService.class
org\scrm\domain\wecom\vo\third\auth\WeTicketVo.class
org\scrm\fegin\QwProductAlbumClient.class
org\scrm\service\impl\WeLeadsFollowRecordAttachmentServiceImpl.class
org\scrm\mapper\WeMarketPrizeMapper.class
org\scrm\domain\storecode\entity\WeStoreCodeConfig.class
org\scrm\domain\substitute\customer\order\entity\WeSubstituteCustomerOrderCatalogueProperty.class
org\scrm\domain\strategic\crowd\vo\WeStrategicCrowdAnalyzelVo.class
org\scrm\domain\wecom\query\media\WeMediaQuery.class
org\scrm\domain\wecom\vo\agentdev\WeTransformUser.class
org\scrm\fegin\QxAuthClient.class
org\scrm\service\IWeLiveAttachmentsService.class
org\scrm\domain\material\entity\WeMaterial.class
org\scrm\service\IWeMomentsCustomerService.class
org\scrm\domain\fission\vo\WeGroupFissionDetailVo.class
org\scrm\domain\leads\leads\entity\WeLeads.class
org\scrm\domain\material\entity\WeCategory.class
org\scrm\domain\moments\dto\MomentsParamDto$MomentsParamDtoBuilder.class
org\scrm\domain\storecode\entity\WeStoreCodeCount$WeStoreCodeCountBuilder.class
org\scrm\domain\wecom\vo\kf\WeKfIntentListVo$WeKfIntentQuestion.class
org\scrm\domain\qr\query\WeLxQrCodeQuery.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity$MomentVideo.class
org\scrm\domain\wecom\query\customer\tag\WeMarkTagQuery.class
org\scrm\domain\WeCustomerLinkAttachments.class
org\scrm\domain\wecom\query\WeThirdLoginQuery.class
org\scrm\service\impl\WeLeadsTemplateTableEntryContentServiceImpl.class
org\scrm\domain\wecom\vo\customer\product\QwProductListVo.class
org\scrm\service\SopTaskService.class
org\scrm\domain\wecom\callback\WeCallBackEvent.class
org\scrm\domain\material\dto\WeContentSendViewDto.class
org\scrm\fallback\QwAgentFallbackFactory.class
org\scrm\service\impl\IWeMomentsEstimateUserServiceImpl.class
org\scrm\domain\moments\vo\WeMomentsCustomerVO.class
org\scrm\domain\product\analyze\vo\WeProductOrderTop5Vo.class
org\scrm\domain\wecom\query\groupmsg\WeGroupMsgQuery.class
org\scrm\domain\moments\dto\MomentsCreateResultDto$WeMomentSendVo.class
org\scrm\domain\wecom\vo\kf\WeKfStatisticListVo.class
org\scrm\domain\WeSensitiveActHit.class
org\scrm\mapper\WeLeadsSeaBaseSettingsMapper.class
org\scrm\service\impl\WeGroupUserStatisticServiceImpl.class
org\scrm\mapper\WeMomentsEstimateUserMapper.class
org\scrm\domain\WeCustomerTrackRecord.class
org\scrm\domain\kf\query\WeKfAddIntentQuery.class
org\scrm\domain\WeLeaveUserInfoAllocate.class
org\scrm\domain\operation\vo\WeSessionCustomerTotalCntVo$WeSessionCustomerTotalCntVoBuilder.class
org\scrm\domain\WeGroupTagRel$WeGroupTagRelBuilder.class
org\scrm\domain\wecom\vo\department\WeDeptInfoVo.class
org\scrm\domain\qr\vo\WeLxQrCodeReceiveListVo.class
org\scrm\fegin\QwTicketClient.class
org\scrm\domain\operation\vo\WeGroupRemindAnalysisVo.class
org\scrm\mapper\WeMomentsTaskRelationMapper.class
org\scrm\service\IWeEmpleCodeService.class
org\scrm\domain\shortlink\query\WeShortLinkStatisticQuery.class
org\scrm\domain\leads\leads\vo\WeLeadsFollowerVO$WeLeadsFollowerVOBuilder.class
org\scrm\mapper\WeFormSurveyCountMapper.class
org\scrm\mapper\WeQiRuleWeeklyUserDataMapper.class
org\scrm\domain\wecom\query\msg\WeRecallMsgQuery.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerListsQuery$WeLinkCustomerListsQueryBuilder.class
org\scrm\domain\kf\vo\WeKfRecordVo.class
org\scrm\fallback\WxKfRobotFallbackFactory.class
org\scrm\domain\wecom\query\qr\WeAddWayQuery$WeAddWayQueryBuilder.class
org\scrm\service\impl\WeShortLinkPromotionSendResultServiceImpl.class
org\scrm\domain\msgtlp\query\WeMsgTlpQuery.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionGetVo.class
org\scrm\mapper\WeKfWelcomeMapper.class
org\scrm\domain\strategic\crowd\vo\WeStrategicCrowdDetailVo.class
org\scrm\service\impl\WeUnionidExternalUseridRelationServiceImpl.class
org\scrm\mapper\WeFormSurveyStatisticsMapper.class
org\scrm\mapper\WeSvipGroupRecordMapper.class
org\scrm\domain\qirule\query\WeQiRuleAddQuery.class
org\scrm\service\IWeErrorMsgService.class
org\scrm\mapper\WeMarketRecordMapper.class
org\scrm\domain\material\vo\MaterialMediaTypeVO.class
org\scrm\service\IWePromotionTaskRecordService.class
org\scrm\domain\QwBaseEntity.class
org\scrm\domain\WeTaskFissionCompleteRecord.class
org\scrm\handler\DataValidityWriteHandler.class
org\scrm\domain\sop\vo\WeSopDetailTabVo.class
org\scrm\domain\WeTagGroup$WeTagGroupBuilder.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Files$File.class
org\scrm\domain\material\query\WeContentSendRecordQuery.class
org\scrm\domain\operation\query\WePageStateQuery.class
org\scrm\domain\kf\query\WeKfAnswerAddQuery.class
org\scrm\domain\wecom\query\customer\msg\WeGroupMsgListQuery.class
org\scrm\domain\taggroup\vo\WePresTagGroupTaskTabCountVo.class
org\scrm\mapper\WeOperationCenterMapper.class
org\scrm\service\impl\WeKfInfoServiceImpl.class
org\scrm\service\IWeDefaultWelcomeMsgService.class
org\scrm\domain\moments\entity\WeMomentsEstimateUser.class
org\scrm\domain\WeGroup$WeGroupBuilder.class
org\scrm\domain\fission\WeFissionInviterRecordSub$WeFissionInviterRecordSubBuilder.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerDetailVo.class
org\scrm\domain\robot\query\WeRobotQuery.class
org\scrm\mapper\WeShortLinkPromotionMapper.class
org\scrm\service\IWeShortLinkStatService.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$TaskCard$TaskCardBuilder.class
org\scrm\domain\moments\entity\WeMomentsUser$WeMomentsUserBuilder.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentDetailVo.class
org\scrm\domain\moments\entity\WeMomentsInteracte.class
org\scrm\domain\WePromotionTaskRecord$WePromotionTaskRecordBuilder.class
org\scrm\domain\WeShortLinkStat.class
org\scrm\domain\WeProductOrderRefund.class
org\scrm\service\impl\WeShortLinkPromotionTemplateGroupServiceImpl.class
org\scrm\domain\leads\template\query\WeTemplateSettingsReRankRequest.class
org\scrm\mapper\WeQiRuleUserStatisticsMapper.class
org\scrm\domain\WeSynchRecord.class
org\scrm\service\impl\WeStrackStageServiceImpl.class
org\scrm\domain\WeFormSurveyStatistics.class
org\scrm\domain\qr\vo\WeQrCodeScanLineCountVo.class
org\scrm\mapper\WeMomentsEstimateCustomerMapper.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordCooperateUser$WeLeadsFollowRecordCooperateUserBuilder.class
org\scrm\domain\WeMarketVo$WeMarketVoBuilder.class
org\scrm\service\impl\WeCorpAccountServiceImpl.class
org\scrm\service\IWeMomentsUserService.class
org\scrm\domain\qr\vo\WeQrScopeUserVo.class
org\scrm\domain\wecom\query\third\auth\WeGetAuthInfoQuery.class
org\scrm\domain\WeGroupRobotMsg.class
org\scrm\domain\moments\query\WeMomentsStatisticUserRecordRequest.class
org\scrm\domain\strategic\crowd\WeStrategicCrowdSwipe.class
org\scrm\service\impl\WeCustomerTrackRecordServiceImpl.class
org\scrm\service\impl\WeSensitiveActServiceImpl.class
org\scrm\domain\community\WeGroupSopChat.class
org\scrm\mapper\WeGroupMemberMapper.class
org\scrm\service\IWeKfMsgService.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo$WeSendGroupSopContentVoBuilder.class
org\scrm\domain\WeQiRule.class
org\scrm\service\impl\IWeContentTalkServiceImpl.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$TextCard.class
org\scrm\service\impl\WeLeaveInfoSubServiceImpl.class
org\scrm\service\impl\WeLxQrScopeServiceImpl.class
org\scrm\domain\kf\vo\QwKfListVo.class
org\scrm\domain\kf\query\WeKfCustomerStatisticQuery.class
org\scrm\mapper\WeProductStatisticsMapper.class
org\scrm\domain\kf\vo\WeKfConsultRealCntVo.class
org\scrm\domain\wecom\vo\third\auth\WeAuthAdminVo.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Text.class
org\scrm\service\impl\WePhoneCallRecordServiceImpl.class
org\scrm\service\IWeMsgTlpService.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Markdown$MarkdownBuilder.class
org\scrm\domain\moments\dto\MomentsParamDto$LinkAttachments.class
org\scrm\domain\WeTaskFissionStaff.class
org\scrm\service\impl\WePresTagGroupMsgServiceImpl.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery$PriorityOption.class
org\scrm\domain\robot\query\WeRobotMsgAddQuery.class
org\scrm\converter\CDataAdapter.class
org\scrm\domain\wecom\query\user\WeLeaveUserQuery$WeLeaveUserQueryBuilder.class
org\scrm\service\IWeLeaveInfoService.class
org\scrm\domain\wecom\vo\third\auth\WeGetQrCodeVo.class
org\scrm\service\impl\WeCustomerLinkAttachmentsServiceImpl.class
org\scrm\domain\WeQiRuleUserStatistics.class
org\scrm\mapper\WeEmpleCodeUseScopMapper.class
org\scrm\service\impl\WeSvipGroupRecordServiceImpl.class
org\scrm\mapper\WeQiRuleMsgMapper.class
org\scrm\domain\leads\sea\query\WeLeadsSeaBaseSettingsRequest.class
org\scrm\service\impl\WeMessagePushServiceImpl.class
org\scrm\service\impl\WeSopChangeServiceImpl.class
org\scrm\domain\shortlink\dto\WeShortLinkPromotionMomentsDto.class
org\scrm\domain\hotword\WeHotWord.class
org\scrm\domain\fission\WeFission$ExchangeContent.class
org\scrm\domain\side\WeChatSide.class
org\scrm\handler\WeLeasExportDateHandler.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$File.class
org\scrm\domain\leads\record\query\WeLeadsFollowRecordAttachmentRequest.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordCooperateUser.class
org\scrm\domain\leave\WeLeaveInfo.class
org\scrm\aop\SynchRecordAop.class
org\scrm\service\impl\WeCommunityNewGroupServiceImpl.class
org\scrm\domain\wecom\vo\customer\state\WeGroupChatStatisticVo$GroupchatStatisticData.class
org\scrm\service\IWeFissionService.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Video$VideoBuilder.class
org\scrm\domain\sop\vo\content\WeCustomerSopContentVo.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Files.class
org\scrm\domain\kf\vo\WeKfUpgradeConfigVo.class
org\scrm\mapper\WeProductMapper.class
org\scrm\domain\kf\query\WeAddKfInfoQuery.class
org\scrm\service\impl\WeStoreCodeServiceImpl.class
org\scrm\domain\phone\WePhoneCallRecord.class
org\scrm\service\impl\WeKfAnswerServiceImpl.class
org\scrm\domain\wecom\query\qr\WeAddWayQuery.class
org\scrm\domain\system\dept\query\SysDeptQuery.class
org\scrm\service\QwAppSendMsgService.class
org\scrm\service\IWeCustomerSeasService.class
org\scrm\domain\WeGroupStatistic.class
org\scrm\service\IWeStrategicCrowdService.class
org\scrm\domain\WeShortLink.class
org\scrm\domain\fission\WeFissionInviterRecord.class
org\scrm\domain\WeKfAnswerAttachments.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateGroupAddQuery.class
org\scrm\domain\community\WeCommunityNewGroup.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity$MomentLink.class
org\scrm\service\impl\WeChatContactMsgServiceImpl.class
org\scrm\domain\wecom\vo\WeCorpTokenVo.class
org\scrm\service\IWeUserRedEnvelopsLimitService.class
org\scrm\domain\leads\record\vo\WeLeadsFollowRecordDetailVO.class
org\scrm\domain\material\vo\MaterialAddViewVo.class
org\scrm\domain\kf\query\WeKfChatMsgListQuery.class
org\scrm\domain\WeAllocateGroups.class
org\scrm\domain\system\config\vo\SysConfigVo$CorpVo$CorpVoBuilder.class
org\scrm\domain\WeFormSurveyCount.class
org\scrm\service\IWeFissionInviterPosterService.class
org\scrm\mapper\WeRedEnvelopesLimitMapper.class
org\scrm\mapper\WeGroupSopMapper.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto$TransferSceneReportInfos$TransferSceneReportInfosBuilder.class
org\scrm\domain\media\WeMessageTemplate.class
org\scrm\domain\WeSensitive.class
org\scrm\mapper\WeLeadsFollowerMapper.class
org\scrm\domain\material\entity\WeContentSendRecord.class
org\scrm\mapper\WeQrTagRelMapper.class
org\scrm\mapper\WePresTagGroupTaskTagMapper.class
org\scrm\domain\WeAiMsg.class
org\scrm\domain\taggroup\WePresTagGroupTaskStat.class
org\scrm\domain\customer\WeBacthMakeCustomerTag$WeBacthMakeCustomerTagBuilder.class
org\scrm\domain\groupmsg\query\WeAddGroupMessageQuery$SenderInfo.class
org\scrm\service\IWeLiveTipService.class
org\scrm\domain\strategic\crowd\query\WeAddStrategicCrowdQuery.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Text$TextBuilder.class
org\scrm\service\impl\WeQiRuleMsgNoticeServiceImpl.class
org\scrm\domain\leave\dto\WeLeaveUserInfoAllocateDto.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionTemplateMomentsVo.class
org\scrm\service\IWeKfAnswerLikeQuestionService.class
org\scrm\domain\leads\template\vo\WeLeadsTemplateTableEntryContentVO.class
org\scrm\mapper\WeKfUserStatMapper.class
org\scrm\domain\storecode\vo\WeStoreCodeGroupTableVo$WeStoreCodeGroupTableVoBuilder.class
org\scrm\service\impl\WeShortLinkUserPromotionTaskServiceImpl.class
org\scrm\domain\kf\vo\WeKfRecordListVo.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo$WeGroupSopContent.class
org\scrm\domain\wecom\query\third\auth\WeGetAppQrCodeQuery.class
org\scrm\domain\sop\vo\content\WeGroupSopContentVo.class
org\scrm\domain\storecode\vo\tab\WeStoreShopGuideTabVo.class
org\scrm\fallback\QwAuthFallbackFactory.class
org\scrm\domain\WeSopChange.class
org\scrm\domain\material\query\LinkMediaQuery.class
org\scrm\service\IWeLxQrCodeLogService.class
org\scrm\domain\WeKfEventMsg.class
org\scrm\mapper\WeStrategicCrowdMapper.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto$WeRedEnvelopesV3ParmDtoBuilder.class
org\scrm\domain\envelopes\dto\H5RedEnvelopesDetailDto$H5RedEnvelopesDetailDtoBuilder.class
org\scrm\domain\live\WeLiveWatchUser.class
org\scrm\service\IWeShortLinkPromotionService.class
org\scrm\service\impl\WeAllocateCustomerServiceImpl.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaDataDetailVO.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyRangeVo.class
org\scrm\service\impl\WeFissionInviterRecordSubServiceImpl.class
org\scrm\domain\shortlink\vo\WeShortLinkLineVo$YData.class
org\scrm\fegin\QwAgentClient.class
org\scrm\domain\svipgroup\query\WeSvipGroupQuery.class
org\scrm\domain\wecom\vo\kf\WeKfStateVo.class
org\scrm\domain\WeStrackStage$WeStrackStageBuilder.class
org\scrm\domain\leads\template\query\WeLeadsTemplateTableEntryContentRequest.class
org\scrm\domain\system\dept\vo\SysDeptVo.class
org\scrm\domain\WxUser.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderUpdateRequest.class
org\scrm\domain\WeCorpAccount$WeCorpAccountBuilder.class
org\scrm\domain\wecom\query\living\WeGetUserAllLivingIdQuery.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery$WeLinkCustomerQueryBuilder.class
org\scrm\domain\agent\query\WeAgentMsgListQuery.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery$PriorityOption$PriorityOptionBuilder.class
org\scrm\domain\wecom\vo\merchant\WeGetBillListVo$Refund.class
org\scrm\domain\community\WeEmpleCodeUseScop.class
org\scrm\domain\wecom\query\kf\WeKfIntentQuery.class
org\scrm\service\impl\WeKfMsgCursorServiceImpl.class
org\scrm\domain\qr\vo\WeQrScopeVo.class
org\scrm\domain\material\dto\TemporaryMaterialDto.class
org\scrm\domain\qr\vo\WeLxQrCodeLineVo.class
org\scrm\domain\shortlink\dto\WeShortLinkPromotionMomentsDto$OtherContent$OtherContentBuilder.class
org\scrm\service\IWeQrLoopNumberService.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Links.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotAibotVo.class
org\scrm\mapper\WeCommunityNewGroupMapper.class
org\scrm\mapper\WeAttachmentPeriodMapper.class
org\scrm\domain\leads\leads\query\WeLeadsBindCustomerRequest.class
org\scrm\mapper\WeSubstituteCustomerOrderCatalogueMapper.class
org\scrm\mapper\WeGroupMessageTemplateMapper.class
org\scrm\domain\kf\vo\WeKfCustomerportrait.class
org\scrm\domain\side\query\WeChatItemQuery.class
org\scrm\domain\agent\query\WeAgentEditQuery.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$WeSopExecuteGroupTimeCondit.class
org\scrm\domain\storecode\query\WxStoreCodeQuery$WxStoreCodeQueryBuilder.class
org\scrm\service\IWeKfStatisticService.class
org\scrm\domain\wecom\vo\living\WeLivingStatInfoVo$LivingStatInfo.class
org\scrm\domain\WeKfWelcome.class
org\scrm\service\IWeSensitiveService.class
org\scrm\service\impl\WeOnJobInfoServiceImpl.class
org\scrm\domain\WeAttachmentPeriod.class
org\scrm\service\IWeShortLinkPromotionTemplateGroupService.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerListsVo.class
org\scrm\domain\wecom\vo\customer\moment\WeAddMomentVo.class
org\scrm\fegin\QwFileClient.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Videos.class
org\scrm\domain\wecom\vo\user\tag\WeUserTagDetailVo.class
org\scrm\service\impl\WeShortLinkPromotionTemplateClientServiceImpl.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaEmployeeRankVO.class
org\scrm\fegin\QwLivingClient.class
org\scrm\domain\WeTag.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateAppMsgAddQuery$ExecuteUserCondit.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerAcquisitionQuotaVo.class
org\scrm\domain\WeSensitiveAct.class
org\scrm\domain\customer\vo\WeCustomersVo.class
org\scrm\domain\WeQrLoopNumber$WeQrLoopNumberBuilder.class
org\scrm\mapper\WeChatContactMsgMapper.class
org\scrm\domain\storecode\vo\WeStoreCodeTableVo$WeStoreCodeTableVoBuilder.class
org\scrm\mapper\WeProductDayStatisticsMapper.class
org\scrm\domain\qr\vo\WxLxQrCodeVo.class
org\scrm\domain\moments\dto\MomentsInteracteParamDto.class
org\scrm\service\impl\WeMomentsTaskServiceImpl.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$MiniprogramNotice$MiniprogramNoticeBuilder.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaTrendVO.class
org\scrm\domain\wecom\query\agentdev\WeTransformExternalUserIdQuery.class
org\scrm\domain\WeGroupCodeRange.class
org\scrm\service\IWxUserService.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo.class
org\scrm\service\IWeSubstituteCustomerOrderService.class
org\scrm\domain\envelopes\vo\WeReceiveRedEnvelopesVo.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordAttachment$WeLeadsFollowRecordAttachmentBuilder.class
org\scrm\mapper\WeAiMsgMapper.class
org\scrm\service\impl\FissionGroupMsgServiceImpl.class
org\scrm\mapper\WeChatItemMapper.class
org\scrm\constant\SubstituteCustomerOrderCataloguePropertyConstants.class
org\scrm\service\IWeProductDayStatisticsService.class
org\scrm\domain\moments\entity\WeMomentsEstimateCustomer$WeMomentsEstimateCustomerBuilder.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerVo$Link.class
org\scrm\mapper\WeCustomerLinkMapper.class
org\scrm\mapper\WeAnalysisHotWordMapper.class
org\scrm\domain\WeSysFieldTemplate.class
org\scrm\domain\WeCorpCustomerVo.class
org\scrm\domain\wecom\vo\customer\state\WeGroupChatStatisticVo$StatisticData.class
org\scrm\domain\moments\query\WeMomentsTaskListRequest.class
org\scrm\domain\wecom\query\kf\WeKfGetMsgQuery.class
org\scrm\service\impl\WeQiRuleUserStatisticsServiceImpl.class
org\scrm\domain\community\vo\WeEmplTaskVo.class
org\scrm\domain\leads\template\entity\WeLeadsTemplateTableEntryContent$WeLeadsTemplateTableEntryContentBuilder.class
org\scrm\domain\msgaudit\query\WeChatContactMsgQuery.class
org\scrm\domain\qr\vo\WeQrScopePartyVo.class
org\scrm\domain\tag\vo\LabelVO$LabelGroup.class
org\scrm\service\IWeMomentsInteracteService.class
org\scrm\domain\envelopes\WeRedEnvelopesLimit.class
org\scrm\mapper\WeSensitiveAuditScopeMapper.class
org\scrm\domain\qr\query\WeQrAddQuery.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$TrackUser.class
org\scrm\fegin\QwMsgAuditClient.class
org\scrm\domain\WeMarket.class
org\scrm\domain\material\query\WeMaterialMobileAddViewRequest.class
org\scrm\service\IWeAiTokenRecordService.class
org\scrm\domain\wecom\query\user\tag\WeUserTagQuery.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionUpdateQuery.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto.class
org\scrm\domain\WeProduct.class
org\scrm\domain\live\WeLiveAttachments$WeLiveAttachmentsBuilder.class
org\scrm\domain\qirule\vo\WeQiRuleWeeklyDetailVo.class
org\scrm\service\IWeSensitiveActService.class
org\scrm\domain\groupcode\entity\WeGroupCode.class
org\scrm\domain\fission\WeFissionInviterPoster.class
org\scrm\service\impl\WeChatCollectionServiceImpl.class
org\scrm\domain\moments\query\WeMomentsStatisticCustomerRecordRequest.class
org\scrm\domain\storecode\vo\WeStoreCodesVo.class
org\scrm\domain\wecom\vo\merchant\WeGetBillListVo.class
org\scrm\service\impl\WeSopPushTimeServiceImpl.class
org\scrm\domain\live\WeLive$SendWeuser.class
org\scrm\mapper\WeGroupCodeMapper.class
org\scrm\domain\envelopes\WeRedEnvelopesRecord$WeRedEnvelopesRecordBuilder.class
org\scrm\service\impl\WeMarketServiceImpl.class
org\scrm\service\IWeKfUpgradeServerService.class
org\scrm\domain\svipgroup\dto\WeSvipGroupDto.class
org\scrm\mapper\WeCustomerSeasMapper.class
org\scrm\service\IWeChatContactMsgService.class
org\scrm\mapper\WeKfAnswerAttachmentsMapper.class
org\scrm\config\rabbitmq\RabbitMQSettingConfig.class
org\scrm\domain\fission\WeFission.class
org\scrm\domain\storecode\query\WeStoreCodeQuery.class
org\scrm\service\IWeCustomerLinkAttachmentsService.class
org\scrm\fallback\QwCustomerFallbackFactory.class
org\scrm\domain\taggroup\vo\WePresTagGroupTaskTrendCountVo.class
org\scrm\domain\leads\leads\query\WeLeadsAddRequest.class
org\scrm\domain\WeGroupMember.class
org\scrm\domain\kf\vo\WeKfMsgAnalyzeVo.class
org\scrm\domain\qr\query\WxLxQrQuery.class
org\scrm\converter\WeMomentsInteractTypeConverter.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotBatchImportSkillQuery.class
org\scrm\service\impl\WeMomentsTaskStatisticServiceImpl.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity.class
org\scrm\service\IWeFormSurveyCountService.class
org\scrm\domain\wecom\vo\customer\WeCustomerListVo.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatListQuery.class
org\scrm\domain\WeCustomerTrajectory.class
org\scrm\service\IWeSopChangeService.class
org\scrm\domain\wecom\query\customer\transfer\WeTransferCustomerQuery.class
org\scrm\domain\wecom\vo\customer\WeFollowUserListVo.class
org\scrm\mapper\WeKfMsgCursorMapper.class
org\scrm\mapper\WeShortLinkPromotionAttachmentMapper.class
org\scrm\mapper\WeCommonLinkStatMapper.class
org\scrm\service\impl\WeLeadsAutoRecoveryServiceImpl.class
org\scrm\domain\wecom\query\kf\WeKfQuery.class
org\scrm\domain\material\dto\ResetCategoryDto.class
org\scrm\service\impl\WeCustomerSeasServiceImpl.class
org\scrm\domain\system\config\vo\SysConfigVo$FileConig.class
org\scrm\domain\live\WeLiveTaskDetailTab.class
org\scrm\mapper\WeKfUpgradeServerMapper.class
org\scrm\domain\substitute\customer\order\vo\WeSubstituteCustomerOrderCataloguePropertyValueVO.class
org\scrm\domain\system\user\vo\SysUserVo.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatAddJoinWayQuery$WeGroupChatAddJoinWayQueryBuilder.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo$ExecuteDeptCondit$ExecuteDeptConditBuilder.class
org\scrm\domain\WeCustomerLink$WeCustomerLinkBuilder.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Image.class
org\scrm\domain\wecom\vo\customer\transfer\WeTransferCustomerVo.class
org\scrm\domain\WeKfCustomerStat.class
org\scrm\mapper\WxUserMapper.class
org\scrm\domain\material\vo\WeCategoryVo.class
org\scrm\annotation\ScrmWechatApiFeignClient.class
org\scrm\domain\wecom\vo\living\WeGetLivingCodeVo.class
org\scrm\service\IWeLxQrCodeService.class
org\scrm\domain\material\ao\PurePoster$PurePosterBuilder.class
org\scrm\domain\WeCustomerSeas$WeCustomerSeasBuilder.class
org\scrm\domain\msgtlp\entity\WeTlpMaterial.class
org\scrm\mapper\WeDefaultWelcomeMsgMapper.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatListVo$GroupChat.class
org\scrm\domain\kf\query\WeKfMsgTaskQuery.class
org\scrm\domain\shortlink\vo\WeShortLinkListVo.class
org\scrm\domain\leads\leads\entity\WeLeadsFollower.class
org\scrm\domain\wecom\vo\WeCorpQrVo.class
org\scrm\fegin\QwMomentsClient.class
org\scrm\service\IWeTrackMaterialPrivacyAuthService.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Files.class
org\scrm\service\impl\WeSensitiveServiceImpl.class
org\scrm\service\impl\WeSubstituteCustomerOrderServiceImpl.class
org\scrm\domain\WeAllocateCustomer.class
org\scrm\domain\WePromotionTaskRecord.class
org\scrm\service\IWeKnowCustomerCodeService.class
org\scrm\domain\msgtlp\query\WeMsgTlpAddQuery.class
org\scrm\service\impl\WeQrLoopNumberServiceImpl.class
org\scrm\service\IWeStrategicCrowdStateTagService.class
org\scrm\domain\moments\dto\MomentsCreateResultDto.class
org\scrm\domain\wecom\vo\third\auth\WeAuthCorpInfoVo.class
org\scrm\service\IWeLeaveInfoSubService.class
org\scrm\domain\qirule\query\WeQiRuleWeeklyDetailListQuery.class
org\scrm\domain\sop\vo\content\WeGroupSopToBeSentVo.class
org\scrm\domain\moments\dto\MomentsParamDto$VisibleRange.class
org\scrm\annotation\ScrmSystemFeignClient.class
org\scrm\domain\wecom\query\product\QwAddProductQuery$Attachment.class
org\scrm\domain\wecom\entity\customer\tag\WeCorpTagGroupEntity.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Videos.class
org\scrm\mapper\WeLeadsTemplateSettingsMapper.class
org\scrm\domain\wecom\vo\weixin\WxJumpWxaVo.class
org\scrm\domain\WeKfPool.class
org\scrm\domain\qirule\vo\WeQiRuleWeeklyDetailListVo.class
org\scrm\domain\leave\WeLeaveInfoSub.class
org\scrm\domain\WeKfAnswerGroup.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$CrowdAttribute.class
org\scrm\service\IWeSensitiveActHitService.class
org\scrm\domain\kf\query\WeKfAnswerGroupQuery.class
org\scrm\domain\material\vo\TextMaterialExportVo.class
org\scrm\domain\kf\vo\WeKfAnswerGroupVo.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentCommentListVo.class
org\scrm\service\IWeCategoryService.class
org\scrm\domain\strategic\crowd\query\WeStrategicCrowdListQuery.class
org\scrm\domain\WeKfServicer.class
org\scrm\domain\kf\query\WeKfRecordQuery.class
org\scrm\domain\live\WeLiveWatchUser$WeLiveWatchUserBuilder.class
org\scrm\service\IWeKfMsgCursorService.class
org\scrm\fegin\QwUserClient.class
org\scrm\domain\ai\ChatStdResponseDto$ChatStdResponseDtoBuilder.class
org\scrm\domain\operation\vo\WeGroupTotalCntVo$WeGroupTotalCntVoBuilder.class
org\scrm\domain\kf\WeKfWelcomeEvent.class
org\scrm\domain\wecom\query\customer\UnionidToExternalUserIdQuery.class
org\scrm\typeHandler\WeCustomerInfoExpandListTypeHandler$1.class
org\scrm\domain\material\vo\WeMaterialFileVo$WeMaterialFileVoBuilder.class
org\scrm\domain\wecom\callback\third\WeThirdBackDeptVo.class
org\scrm\service\IWeShortLinkPromotionTemplateClientService.class
org\scrm\domain\wecom\vo\kf\WeKfIntentListVo.class
org\scrm\domain\SysLeaveUser.class
org\scrm\service\impl\WePresTagGroupTaskServiceImpl.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerDetailVo$Link.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto.class
org\scrm\service\IWeFormSurveyCategoryService.class
org\scrm\domain\material\vo\talk\WeContentTalkVo.class
org\scrm\domain\WeStrategicCrowdCustomerRel.class
org\scrm\domain\form\query\WeFormSurveyCatalogueQuery$WeFormSurveyCatalogueQueryBuilder.class
org\scrm\domain\qr\query\WeQrCodeEventQuery.class
org\scrm\service\impl\WeChatItemServiceImpl.class
org\scrm\domain\customer\vo\WeCustomerChannelCountVo.class
org\scrm\service\IWeLeadsTemplateSettingsService.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotPublish.class
org\scrm\mapper\WeFormSurveyCatalogueMapper.class
org\scrm\mapper\WeLiveAttachmentsMapper.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Video.class
org\scrm\domain\WeFormSurveyCount$WeFormSurveyCountBuilder.class
org\scrm\service\impl\WeCommonLinkStatServiceImpl.class
org\scrm\domain\WeGroupMessageTemplate$WeCustomersOrGroupQuery.class
org\scrm\domain\moments\dto\MomentsParamDto$BaseAttachments.class
org\scrm\mapper\WeFormSurveyRadioMapper.class
org\scrm\domain\strategic\crowd\vo\WeStrategicCrowdAnalyzelDataVo.class
org\scrm\domain\wecom\query\customer\tag\WeUpdateCorpTagQuery$WeUpdateCorpTagQueryBuilder.class
org\scrm\domain\wecom\vo\customer\state\WeUserBehaviorDataVo.class
org\scrm\service\IWeCustomerLinkService.class
org\scrm\domain\live\WeLive.class
org\scrm\domain\wecom\query\qr\WeContactWayQuery$WeContactWayQueryBuilder.class
org\scrm\domain\kf\vo\WeKfQualityHistogramVo.class
org\scrm\mapper\WeFormSurveyAnswerMapper.class
org\scrm\domain\storecode\vo\drum\WeStoreShopGuideDrumVo.class
org\scrm\mapper\WePeriodAttachmentsMapper.class
org\scrm\domain\customer\vo\WeCustomerAddGroupVo.class
org\scrm\typeHandler\WeCustomerInfoValHandler.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$CompanyOrPersonTag.class
org\scrm\mapper\WeStrackStageMapper.class
org\scrm\service\impl\WeKfServicerServiceImpl.class
org\scrm\domain\WeFlowerCustomerTagRel$WeFlowerCustomerTagRelBuilder.class
org\scrm\domain\product\analyze\vo\WeProductAnalyzeStatisticsVo.class
org\scrm\domain\storecode\entity\WeStoreCode.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatDetailVo$GroupChatDetail.class
org\scrm\service\IWeShortLinkPromotionTemplateAppMsgService.class
org\scrm\domain\WeLxQrScope.class
org\scrm\service\IWeChatSideService.class
org\scrm\domain\customer\query\WeCustomersQuery.class
org\scrm\mapper\WeQiRuleManageStatisticsMapper.class
org\scrm\domain\wecom\query\customer\UpdateCustomerRemarkQuery.class
org\scrm\domain\groupchat\query\WeGroupChatQuery$WeGroupChatQueryBuilder.class
org\scrm\typeHandler\WeStrategicCrowdSwipeListTypeHandler$1.class
org\scrm\domain\product\product\vo\WeProductListVo.class
org\scrm\domain\leads\leads\entity\WeLeadsImportRecord.class
org\scrm\domain\WeCustomerLinkCount.class
org\scrm\service\IWeKfAnswerAttachmentsService.class
org\scrm\domain\shortlink\dto\WeShortLinkLineDto.class
org\scrm\domain\wecom\query\customer\msg\WeCancelGroupMsgSendQuery$WeCancelGroupMsgSendQueryBuilder.class
org\scrm\service\IWeMarketService.class
org\scrm\domain\leads\leads\vo\Properties.class
org\scrm\domain\leads\record\vo\WeLeadsFollowRecordContentVO.class
org\scrm\domain\hotword\dto\IYqueMsgAuditDto.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaStatisticVO.class
org\scrm\service\impl\WeLeadsImportRecordServiceImpl.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Attachments.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatVo.class
org\scrm\handler\WeLeadsExportHeadsWriteHandler.class
org\scrm\service\impl\WeKfScenesServiceImpl.class
org\scrm\service\IWeLiveWatchUserService.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Markdown.class
org\scrm\domain\taggroup\WePresTagGroupTaskTag.class
org\scrm\domain\kf\query\WeKfServicerListQuery.class
org\scrm\mapper\WeSopExecuteTargetMapper.class
org\scrm\fallback\QxAppletFallbackFactory.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Links.class
org\scrm\domain\leave\WeLeaveInfoSub$WeLeaveInfoSubBuilder.class
org\scrm\domain\wecom\vo\msgaudit\WeMsgAuditVo$AgreeInfo.class
com\tencent\wework\FinanceService.class
org\scrm\domain\WeBuildUserOrGroupConditVo.class
org\scrm\domain\msgaudit\query\WeSensitiveHitQuery.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Links$Link.class
org\scrm\domain\wecom\callback\WeBackUserTagVo.class
org\scrm\domain\wecom\query\third\auth\WeSetSessionInfoQuery.class
org\scrm\domain\operation\vo\WePageCountVo.class
org\scrm\domain\substitute\customer\order\vo\WeSubstituteCustomerOrderCatalogueVO.class
org\scrm\fallback\QwSysAreaFallbackFactory.class
org\scrm\service\impl\TaskJourneyServiceImpl.class
org\scrm\domain\fission\vo\WeFissionDetailSubVo.class
org\scrm\domain\wecom\query\WeThirdLoginQuery$WeThirdLoginQueryBuilder.class
org\scrm\domain\system\config\vo\SysConfigVo$FileConig$FileConigBuilder.class
org\scrm\domain\shortlink\query\WeShortLinkQuery.class
org\scrm\domain\wecom\callback\third\WeThirdRegisterCorpVo$ContactSyncVo.class
org\scrm\domain\wecom\vo\kf\WeUpgradeServiceConfigVo$MemberRange.class
org\scrm\domain\wecom\query\weixin\WxJumpWxaQuery.class
org\scrm\domain\sop\vo\WeSopListsVo$WeSopListsVoBuilder.class
org\scrm\mapper\WeTagGroupMapper.class
org\scrm\domain\wecom\vo\agentdev\WeTransformUserIdVO.class
org\scrm\domain\operation\vo\WeSessionGroupTotalCntVo$WeSessionGroupTotalCntVoBuilder.class
org\scrm\domain\qirule\query\WeQiRuleWeeklyListQuery.class
org\scrm\mapper\WeTagMapper.class
org\scrm\service\impl\WeShortLinkPromotionAttachmentServiceImpl.class
org\scrm\mapper\WeAgentMsgMapper.class
org\scrm\service\IWeLeaveUserService.class
org\scrm\domain\WeKfScenes.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity$MomentImage.class
org\scrm\service\IWeSvipGroupRecordService.class
org\scrm\domain\wecom\vo\customer\link\WeLinkWecustomerCountVo$CustomerList.class
org\scrm\service\impl\WeTrackMaterialPrivacyAuthServiceImpl.class
org\scrm\service\IWeTalkMaterialService.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto$TransferSceneReportInfos.class
org\scrm\mapper\WeMomentsInteracteMapper.class
org\scrm\mapper\WeKeywordGroupViewCountMapper.class
org\scrm\domain\WeFormSurveyRadio.class
org\scrm\domain\kf\query\WeKfListQuery.class
org\scrm\domain\wecom\entity\customer\groupChat\WeGroupMemberEntity.class
org\scrm\domain\material\ao\WePosterSubassembly.class
org\scrm\domain\moments\dto\MomentsParamDto$Text$TextBuilder.class
org\scrm\domain\leads\leads\entity\WeLeadsManualAddRecord.class
org\scrm\domain\wecom\vo\kf\WeKfListVo.class
org\scrm\service\impl\WeKfCustomerStatServiceImpl.class
org\scrm\domain\kf\vo\WeKfConsultDurationVo.class
org\scrm\domain\know\vo\WeKnowCustomerCountTrendOrTableVo.class
org\scrm\domain\qirule\query\WeQiRuleStatisticsTableMsgQuery.class
org\scrm\domain\WeAllocateCustomer$WeAllocateCustomerBuilder.class
org\scrm\domain\moments\dto\MomentsParamDto$1.class
org\scrm\mapper\WeGroupSopMaterialMapper.class
org\scrm\mapper\WeKfAnswerMapper.class
org\scrm\service\impl\strategic\shortlink\ShortLinkPromotionGroupMsgServiceImpl.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentSendsEntity.class
org\scrm\domain\product\product\query\WeAddProductQuery.class
org\scrm\service\impl\WeStoreCodeConfigServiceImpl.class
org\scrm\domain\community\vo\WeGroupSopVo.class
org\scrm\domain\wecom\vo\third\auth\WeAuthInfoVo.class
org\scrm\handler\WeQiRuleWeeklyUserDetailWriteHandler.class
org\scrm\domain\moments\vo\WeMomentsInteractVO.class
org\scrm\domain\wecom\query\customer\WeBatchCustomerQuery.class
org\scrm\mapper\WeHotWordMapper.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$MiniprogramNotice.class
org\scrm\domain\envelopes\vo\RedEnvelopesBaseInfoVo.class
org\scrm\service\IWeCustomerTrajectoryService.class
org\scrm\service\impl\SopTransferGroupMsgServiceImpl.class
org\scrm\domain\material\query\WeMaterialMobileAddViewRequest$WeMaterialMobileAddViewRequestBuilder.class
org\scrm\service\IWeOnJobInfoService.class
org\scrm\mapper\WeKnowCustomerCodeTagMapper.class
org\scrm\domain\taggroup\WePresTagGroupTaskStat$WePresTagGroupTaskStatBuilder.class
org\scrm\domain\envelopes\vo\WeRedEnvelopesCountVo.class
org\scrm\mapper\WeStoreCodeConfigMapper.class
org\scrm\domain\storecode\query\WxStoreCodeQuery.class
org\scrm\domain\wecom\callback\third\WeThirdCreateAuthVo.class
org\scrm\service\IWeShortLinkPromotionDayStatService.class
org\scrm\domain\envelopes\WeRedEnvelopesLimit$WeRedEnvelopesLimitBuilder.class
org\scrm\domain\kf\query\WeKfAddMsgQuery.class
org\scrm\domain\wecom\vo\WeCorpTokenVo$Admin.class
org\scrm\service\impl\WeKfMsgServiceImpl.class
org\scrm\domain\moments\entity\WeMomentsTaskRelation.class
org\scrm\domain\tag\vo\WeTagVo.class
org\scrm\domain\agent\vo\LwAgentListVo.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$Video.class
org\scrm\mapper\WeCustomerLinkAttachmentsMapper.class
org\scrm\domain\operation\vo\WeGroupMemberRealCntVo.class
org\scrm\domain\sop\vo\WeSopExecuteEndVo.class
org\scrm\domain\kf\WeKfWelcomeInfo.class
org\scrm\service\impl\WeGroupMessageListServiceImpl.class
org\scrm\domain\wecom\vo\qr\WeContactWayVo.class
org\scrm\mapper\WeShortLinkMapper.class
org\scrm\mapper\WeGroupMessageListMapper.class
org\scrm\domain\WeMarketCountVo$MarketViewTop.class
org\scrm\domain\kf\query\WeKfAddIntentQuery$IntentSimilarQuestions.class
org\scrm\domain\wecom\vo\customer\transfer\WeUnassignedVo$UnassignedVo.class
org\scrm\domain\wecom\vo\department\WeDeptVo.class
org\scrm\domain\wecom\vo\user\WeLeaveUserVo.class
org\scrm\domain\wecom\vo\user\WeLoginUserDetailVo.class
org\scrm\mapper\WeLeadsFollowRecordMapper.class
org\scrm\domain\wecom\callback\WeBackAsynTaskVo$BatchJobVo.class
org\scrm\mapper\WeGroupSopPicMapper.class
org\scrm\mapper\WeQrAttachmentsMapper.class
org\scrm\service\impl\WeCustomerLinkCountServiceImpl.class
org\scrm\domain\sop\vo\content\WeSopToBeSentContentInfoVo.class
org\scrm\domain\robot\query\WeRobotAddQuery.class
org\scrm\domain\qirule\vo\WeQiRuleStatisticsViewVo.class
org\scrm\domain\wecom\query\third\auth\WeGetCustomizedAuthUrlQuery.class
org\scrm\service\IWeProductOrderRefundService.class
org\scrm\mapper\WeSensitiveActMapper.class
org\scrm\domain\sop\vo\content\WeGroupSopToBeSentVo$WeGroupSopToBeSentVoBuilder.class
org\scrm\domain\wecom\query\media\WeGetMediaQuery.class
org\scrm\service\impl\WeMomentsInteracteServiceImpl.class
org\scrm\domain\phone\WePhoneCallRecord$WePhoneCallRecordBuilder.class
org\scrm\domain\moments\dto\TextMessageDto$1.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerQuery$Range.class
org\scrm\domain\wecom\vo\user\tag\WeUserTagListVo.class
org\scrm\service\IWeSynchRecordService.class
org\scrm\domain\operation\query\WeOperationCustomerQuery$WeOperationCustomerQueryBuilder.class
org\scrm\domain\moments\dto\MomentsCancelDTO$MomentsCancelDTOBuilder.class
org\scrm\domain\moments\vo\MomentsSendResultVO.class
org\scrm\config\common\EnumConfig$CommonEnumMap.class
org\scrm\domain\envelopes\WeRedEnvelopes$WeRedEnvelopesBuilder.class
org\scrm\service\impl\strategic\shortlink\ShortLinkPromotionStrategyFactory.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatDetailVo.class
org\scrm\domain\ai\ChatStdResponseDto.class
org\scrm\domain\operation\query\WeOperationGroupQuery.class
org\scrm\service\IWeQrAttachmentsService.class
org\scrm\domain\substitute\customer\order\vo\WeSubstituteCustomerOrderCataloguePropertyVO.class
org\scrm\domain\community\vo\WeCommunityNewGroupTableVo.class
org\scrm\domain\WeSysFieldTemplate$OtherContent.class
org\scrm\mapper\WeQrCodeMapper.class
org\scrm\domain\MarketTable$MarketTableBuilder.class
org\scrm\domain\sop\WeSopExecuteTarget$WeSopExecuteTargetBuilder.class
org\scrm\domain\WeKfAnswer.class
org\scrm\domain\moments\dto\MomentsParamDto$ExternalContactList.class
org\scrm\service\impl\WeGroupRobotInfoServiceImpl.class
org\scrm\domain\groupcode\query\WeMakeGroupCodeTagQuery$WeMakeGroupCodeTagQueryBuilder.class
org\scrm\service\IWeQiRuleWeeklyUserDataService.class
org\scrm\domain\operation\vo\WeGroupTotalCntVo.class
org\scrm\domain\groupchat\query\WeOnTheJobGroupQuery$WeOnTheJobGroup.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderRequest.class
org\scrm\domain\operation\query\WeOperationCustomerQuery.class
org\scrm\domain\kf\vo\WeKfSceneRankCntVo.class
org\scrm\domain\product\refund\vo\WeProductOrderRefundVo.class
org\scrm\service\impl\WeSopAttachmentsServiceImpl.class
org\scrm\service\impl\WeTagGroupServiceImpl.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateClientAddQuery.class
org\scrm\domain\WeTaskFissionReward.class
org\scrm\domain\product\product\vo\WeProductVo.class
org\scrm\domain\shortlink\vo\WeShortLinkLineVo.class
org\scrm\domain\form\query\WeFormSurveyAnswerQuery.class
org\scrm\service\IWeFormSurveySiteStasService.class
org\scrm\domain\community\vo\WeGroupCodeVo$1.class
org\scrm\domain\WeShortLinkPromotionTemplateGroup.class
org\scrm\fallback\QwCorpFallbackFactory.class
org\scrm\domain\WeAllocateGroups$WeAllocateGroupsBuilder.class
org\scrm\mapper\WeAllocateGroupMapper.class
org\scrm\domain\kf\vo\WeKfAnswerLikeQuestionVo.class
org\scrm\domain\leads\record\query\WeLeadsAddFollowRequest.class
org\scrm\service\IWeShortLinkPromotionAttachmentService.class
org\scrm\domain\live\vo\WeLinveUserVo$WeLinveUserVoBuilder.class
org\scrm\domain\wecom\vo\user\WeUserConvertVo.class
org\scrm\service\IWeLeadsManualAddRecordService.class
org\scrm\domain\wecom\query\living\WeLivingQuery$WeLivingQueryBuilder.class
org\scrm\mapper\WeQrScopeMapper.class
org\scrm\service\IWeStrategicCrowdCustomerRelService.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateAppMsgAddQuery$ExecuteDeptCondit.class
org\scrm\domain\wecom\vo\user\WeLoginUserVo.class
org\scrm\domain\groupcode\vo\WeGroupChatInfoVo.class
org\scrm\service\impl\WeFissionInviterPosterServiceImpl.class
org\scrm\service\impl\strategic\shortlink\ShortLinkPromotionConstants.class
org\scrm\domain\wecom\callback\third\WeThirdChangeAppAdminVo.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentListVo$Agent.class
org\scrm\service\impl\LiveGroupMsgServiceImpl.class
org\scrm\service\impl\WePeriodAttachmentsServiceImpl.class
org\scrm\domain\qr\vo\WeLxQrScopeUserVo.class
org\scrm\domain\envelopes\vo\RedEnvelopesBaseInfoVo$RedEnvelopesBaseInfoVoBuilder.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotBatchImportSkill$WxKfRobotSkill.class
org\scrm\domain\WeShortLinkPromotionDayStat.class
org\scrm\domain\WeMarketCountVo$MrketJoinTop.class
org\scrm\service\IWeKnowCustomerAttachmentsService.class
org\scrm\mapper\WeKfAnswerGroupMapper.class
org\scrm\mapper\WeStoreCodeMapper.class
org\scrm\domain\kf\vo\WeKfCustomerportrait$WeKfCustomerportraitBuilder.class
org\scrm\domain\qr\WeQrTagRel.class
org\scrm\domain\WeAgentMsg.class
org\scrm\domain\leads\leads\entity\WeLeadsAutoRecovery.class
org\scrm\listener\SopReevaluationEventListener.class
org\scrm\domain\wecom\callback\third\WeThirdRegisterCorpVo.class
org\scrm\handler\TextMaterialImportDataListener.class
org\scrm\domain\kf\vo\WeKfEventVo.class
org\scrm\domain\moments\vo\WeMomentsTaskMobileVO.class
org\scrm\service\IWeGroupUserStatisticService.class
org\scrm\service\IWeSopBaseService.class
org\scrm\service\impl\WeLeaveInfoServiceImpl.class
org\scrm\domain\moments\dto\MomentsParamDto$Image.class
org\scrm\domain\sop\dto\WeSopPushTimeDto.class
org\scrm\mapper\WeGroupRobotMsgMapper.class
org\scrm\domain\wecom\vo\msgaudit\WeMsgAuditVo$Member.class
org\scrm\mapper\WeKfCustomerMapper.class
org\scrm\mapper\WeSubstituteCustomerOrderMapper.class
org\scrm\domain\WeShortLinkPromotion.class
org\scrm\service\impl\WeLeadsSeaServiceImpl.class
org\scrm\domain\moments\dto\MomentsSendResultDTO.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$News$NewsBuilder.class
org\scrm\mapper\WeLeadsSeaVisibleRangeMapper.class
org\scrm\mapper\WeSopBaseMapper.class
org\scrm\domain\WeMarketCountVo$Lottery.class
org\scrm\domain\welcomemsg\vo\WeDefaultWelcomeMsgVo.class
org\scrm\domain\WeSensitiveAuditScope.class
org\scrm\domain\moments\entity\WeMomentsEstimateUser$WeMomentsEstimateUserBuilder.class
org\scrm\domain\wecom\vo\customer\UnionidToExternalUserIdVo$UnionIdToExternalUserIdList.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$News.class
org\scrm\config\HunYuanClient.class
org\scrm\service\impl\WeSopBaseServiceImpl.class
org\scrm\fallback\QwSysUserFallbackFactory.class
org\scrm\domain\wecom\query\living\WeModifyLivingQuery$WeModifyLivingQueryBuilder.class
org\scrm\service\IWePresTagGroupTaskTagService.class
org\scrm\domain\kf\query\WeKfAddIntentQuery$QuestionText.class
org\scrm\domain\moments\dto\MomentsParamDto$SenderList$SenderListBuilder.class
org\scrm\service\IWeMomentsEstimateUserService.class
org\scrm\service\IWeLeadsService.class
org\scrm\service\IWeMomentsAttachmentsService.class
org\scrm\service\impl\WeCustomerLinkServiceImpl.class
org\scrm\domain\moments\query\WeMomentsStatisticInteractRecordRequest.class
org\scrm\domain\phone\dto\PhoneCallRecordDto.class
org\scrm\mapper\WeMsgTlpAttachmentsMapper.class
org\scrm\domain\moments\entity\WeMomentsUser.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Miniprograms.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionQuery.class
org\scrm\domain\leads\sea\query\VisibleRange$PostRange.class
org\scrm\domain\WeGroupMessageTemplate$WeGroupQuery$WeGroupQueryBuilder.class
org\scrm\domain\know\WeKnowCustomerCode.class
org\scrm\domain\moments\entity\WeMomentsAttachments.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$Moment.class
org\scrm\mapper\WeCorpAccountMapper.class
org\scrm\domain\groupmsg\query\WeAddGroupMessageQuery$SenderInfo$SenderInfoBuilder.class
org\scrm\service\IWePresTagGroupTaskScopeService.class
org\scrm\mapper\WeQiRuleScopeMapper.class
org\scrm\domain\material\vo\WeMaterialFileVo.class
org\scrm\service\IWeEmpleCodeTagService.class
org\scrm\domain\wecom\query\department\WeAddDeptQuery.class
org\scrm\service\impl\IWeQrTagRelServiceImpl.class
org\scrm\service\IWeKfInfoService.class
org\scrm\domain\wecom\vo\customer\product\QwProductVo.class
org\scrm\domain\wecom\vo\third\auth\WeExtensionRegisterVo.class
org\scrm\domain\groupchat\query\WeGroupChatQuery.class
org\scrm\domain\wecom\vo\media\WeMediaVo.class
org\scrm\domain\moments\entity\WeMomentsTaskRelation$WeMomentsTaskRelationBuilder.class
org\scrm\service\IWeLeadsFollowRecordCooperateUserService.class
org\scrm\service\IWeSopAttachmentsService.class
org\scrm\domain\wecom\callback\WeBackAsynTaskVo.class
org\scrm\domain\operation\vo\WeGroupRealCntVo.class
org\scrm\service\impl\WeShortLinkStatServiceImpl.class
org\scrm\service\IWeFormSurveyStatisticsService.class
org\scrm\handler\SpinnerWriteHandler.class
org\scrm\service\impl\WeKfStatisticServiceImpl.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaBaseSettingsVO.class
org\scrm\domain\WeCustomerTrajectory$WeCustomerTrajectoryBuilder.class
org\scrm\domain\sop\WeSopExecuteTargetAttachments$WeSopExecuteTargetAttachmentsBuilder.class
org\scrm\mapper\WeChatCollectionMapper.class
org\scrm\service\impl\WeShortLinkPromotionServiceImpl.class
org\scrm\domain\envelopes\query\WeRedEnvelopeListQuery.class
org\scrm\domain\product\product\query\WeProductQuery.class
org\scrm\domain\community\vo\WeCommunityWeComeMsgVo.class
org\scrm\domain\wecom\vo\customer\product\QwAddProductVo.class
org\scrm\mapper\WeUserBehaviorDataMapper.class
org\scrm\service\IWeLeadsSeaBaseSettingsService.class
org\scrm\domain\know\WeKnowCustomerCodeCount$WeKnowCustomerCodeCountBuilder.class
org\scrm\domain\wecom\vo\user\WeUserListVo.class
org\scrm\domain\leads\sea\query\WeLeadsSeaUpdateRequest.class
org\scrm\converter\LiveStateConverter.class
org\scrm\domain\community\vo\WeCommunityNewGroupTabCountVo.class
org\scrm\mapper\WeFissionInviterRecordSubMapper.class
org\scrm\service\IWeShortLinkPromotionSendResultService.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$File$FileBuilder.class
org\scrm\fallback\QwTicketFallbackFactory.class
org\scrm\service\impl\WeAiTokenRecordServiceImpl.class
org\scrm\domain\moments\query\WeMomentsStatisticUserRecordRequest$WeMomentsStatisticUserRecordRequestBuilder.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$Groups.class
org\scrm\domain\kf\query\WeEditKfScenesQuery.class
org\scrm\domain\wecom\vo\goupmsg\WeGroupMsgTplVo.class
org\scrm\service\impl\WeGroupStatisticServiceImpl.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Voice.class
org\scrm\domain\kf\query\WeKfEventMsgListQuery.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateGroupUpdateQuery.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCatalogueUpdateRequest.class
org\scrm\domain\wecom\entity\customer\moment\WeVisibleRangeEntity.class
org\scrm\domain\sop\dto\WeSopBaseDto.class
org\scrm\domain\WeMarketCountVo$MarketTrend.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionTemplateGroupVo.class
org\scrm\domain\WeGroupMessageTemplate$WeCustomersOrGroupQuery$WeCustomersOrGroupQueryBuilder.class
org\scrm\mapper\WeShortLinkPromotionDayStatMapper.class
org\scrm\domain\moments\dto\LinkMessageDto.class
org\scrm\domain\customer\vo\WeCustomerSimpleInfoVo.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatJoinWayQuery.class
org\scrm\fallback\QwSysDeptFallbackFactory.class
org\scrm\fallback\QwFileFallbackFactory.class
org\scrm\domain\leads\leads\query\WeLeadsBaseRequest.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotBatchImportSkillVo.class
org\scrm\domain\leave\dto\WeLeaveInfoDto.class
org\scrm\service\IWeQiRuleScopeService.class
org\scrm\domain\moments\dto\MomentsParamDto$Text.class
org\scrm\domain\WeErrorMsg$WeErrorMsgBuilder.class
org\scrm\domain\wecom\callback\WeBackUserVo.class
org\scrm\service\impl\strategic\shortlink\PromotionType.class
org\scrm\domain\wecom\vo\customer\tag\WeCorpTagListVo.class
org\scrm\mapper\WeFlowerCustomerTagRelMapper.class
org\scrm\service\impl\WeChatContactSensitiveMsgServiceImpl.class
org\scrm\domain\live\WeLiveTip$WeLiveTipBuilder.class
org\scrm\domain\wecom\query\customer\moment\WeAddMomentQuery.class
org\scrm\domain\kf\query\WeAddKfScenesQuery.class
org\scrm\domain\wecom\vo\WeKfAddIntentVo.class
org\scrm\domain\moments\dto\MomentsListDetailParamDto.class
org\scrm\domain\fission\WeFissionNotice$WeFissionNoticeBuilder.class
com\tencent\wework\Finance.class
org\scrm\domain\msg\QwAppMsgBody.class
org\scrm\domain\WeGroupUserStatistic.class
org\scrm\domain\groupchat\vo\WeCustomerDeduplicationVo.class
org\scrm\domain\media\WeMessageTemplate$WeMessageTemplateBuilder.class
org\scrm\domain\WeFormSurveyStatistics$WeFormSurveyStatisticsBuilder.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecordContent.class
org\scrm\domain\leads\record\query\WeLeadsFollowRecordCooperateUserRequest.class
org\scrm\domain\wecom\vo\kf\WeUpgradeServiceConfigVo.class
org\scrm\service\impl\WeStrategicCrowdCustomerRelServiceImpl.class
org\scrm\domain\wecom\vo\third\auth\WeAuthAgentInfoVo$Privilege.class
org\scrm\domain\task\vo\WeTasksVO.class
org\scrm\domain\wecom\entity\customer\tag\WeCorpTagEntity.class
org\scrm\domain\groupcode\query\WeMakeGroupCodeTagQuery.class
org\scrm\mapper\WeLeadsSeaRuleRecordMapper.class
org\scrm\mapper\WeLxQrCodeLogMapper.class
org\scrm\domain\agent\vo\WeAgentMsgVo.class
org\scrm\domain\WeKeyWordGroupSub.class
org\scrm\domain\qirule\vo\WeQiRuleNoticeListVo.class
org\scrm\domain\task\entity\WeTasks.class
org\scrm\domain\leads\leads\vo\WeLeadsUserStatisticVO.class
org\scrm\domain\community\vo\WeCommunityNewGroupVo.class
org\scrm\domain\wecom\query\qr\WeContactWayQuery.class
org\scrm\domain\wecom\vo\customer\WeCustomerDetailVo$ExternalContact.class
org\scrm\service\IWeKfCustomerService.class
org\scrm\service\IWeShortLinkUserPromotionTaskService.class
org\scrm\mapper\WeLeadsTemplateTableEntryContentMapper.class
org\scrm\service\IWeGroupMessageTaskService.class
org\scrm\fallback\QwUserFallbackFactory.class
org\scrm\domain\material\query\WeContentViewRecordQuery.class
org\scrm\domain\WeFormSurveyCatalogue.class
org\scrm\domain\wecom\vo\living\WeLivingStatInfoVo$LivingExternalUsers.class
org\scrm\domain\community\query\WeCommunityKeyWordGroupTableQuery.class
org\scrm\domain\leads\leads\query\WeLeadsUpdateRequest.class
org\scrm\domain\phone\dto\PhoneCallRecordDto$PhoneCallRecordDtoBuilder.class
org\scrm\domain\operation\vo\WeCustomerRealCntVo.class
org\scrm\domain\user\vo\WeUserScreenConditVo$ExecuteDeptCondit.class
org\scrm\mapper\WeCustomerInfoExpandMapper.class
org\scrm\domain\WeGroupMessageList.class
org\scrm\service\impl\WeCustomerServiceImpl.class
org\scrm\service\IWeFormSurveyAnswerService.class
org\scrm\domain\leads\record\entity\WeLeadsFollowRecord.class
org\scrm\domain\customer\vo\WeCustomerLinkCountTabVo.class
org\scrm\domain\welcomemsg\WeDefaultWelcomeMsg.class
org\scrm\domain\community\vo\WeGroupCodeVo.class
org\scrm\domain\operation\vo\WeSessionUserChatRankVo.class
org\scrm\domain\wecom\query\customer\tag\WeCorpTagListQuery$WeCorpTagListQueryBuilder.class
org\scrm\domain\wecom\vo\user\WeUserDetailVo.class
org\scrm\service\IWeQiRuleManageStatisticsService.class
org\scrm\domain\material\entity\WeContentViewRecord.class
org\scrm\domain\wecom\vo\kf\WeKfMsgVo.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$TrackUser$TrackUserBuilder.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Links$Link.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionStatisticQuery.class
org\scrm\domain\moments\query\WeMomentsJobIdToMomentsIdRequest.class
org\scrm\service\impl\WeEmpleCodeUseScopServiceImpl.class
org\scrm\domain\WeCustomerTrackRecord$WeCustomerTrackRecordBuilder.class
org\scrm\mapper\WeGroupMapper.class
org\scrm\mapper\WeSopAttachmentsMapper.class
org\scrm\domain\leave\WeOnJobInfo.class
org\scrm\mapper\WeLeaveInfoSubMapper.class
org\scrm\service\impl\WxUserServiceImpl.class
org\scrm\domain\wecom\entity\customer\groupChat\WeOwnerFilterEntity$WeOwnerFilterEntityBuilder.class
org\scrm\domain\index\vo\WeIndexVo.class
org\scrm\domain\system\user\query\SysUserQuery$SysUserQueryBuilder.class
org\scrm\service\impl\WeCustomerInfoExpandServiceImpl.class
org\scrm\domain\wecom\query\customer\transfer\WeTransferCustomerQuery$WeTransferCustomerQueryBuilder.class
org\scrm\domain\wx\coupon\WxSendCouponQuery.class
org\scrm\annotation\ScrmFileFeignClient.class
org\scrm\service\impl\WeFissionServiceImpl.class
org\scrm\service\impl\WeLeadsFollowRecordContentServiceImpl.class
org\scrm\service\IWeContentViewRecordService.class
org\scrm\service\impl\WePresTagGroupTaskStatServiceImpl.class
org\scrm\domain\leads\leads\vo\WeLeadsFollowerNumVO.class
org\scrm\domain\wecom\query\customer\moment\WeMomentQuery.class
org\scrm\domain\leads\record\vo\WeLeadsFollowRecordAttachmentVO.class
org\scrm\service\impl\WeFormSurveyCatalogueServiceImpl.class
org\scrm\domain\tag\vo\LabelVO.class
org\scrm\domain\WeConfigParamInfo.class
org\scrm\service\IWeSopExecuteTargetService.class
org\scrm\domain\msgtlp\dto\WeMsgTlpDto.class
org\scrm\domain\WeFormSurveySiteStas.class
org\scrm\mapper\WeKnowCustomerAttachmentsMapper.class
org\scrm\service\impl\IWeFissionInviterRecordServiceImpl.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo$WeCustomerSop$WeCustomerSopBuilder.class
org\scrm\domain\envelopes\vo\WeReceiveRedEnvelopesVo$WeReceiveRedEnvelopesVoBuilder.class
org\scrm\domain\wecom\query\living\WeActivityDetailQuery.class
org\scrm\domain\WeGroupMessageTemplate$WeGroupQuery.class
org\scrm\domain\fission\WeFissionNotice.class
org\scrm\mapper\WeTlpMaterialMapper.class
org\scrm\domain\WeMsgTlp.class
org\scrm\domain\wecom\callback\third\WeThirdBackUserTagVo.class
org\scrm\service\impl\WeGroupMessageAttachmentsServiceImpl.class
org\scrm\mapper\WeQiRuleMapper.class
org\scrm\domain\task\entity\WeTasks$WeTasksBuilder.class
org\scrm\domain\wecom\vo\kf\WeKfIntentListVo$WeKfIntent.class
org\scrm\mapper\WeAgentInfoMapper.class
org\scrm\domain\kf\query\WeKfAddIntentQuery$IntentQuestion.class
org\scrm\domain\kf\vo\WeKfQualityChatVo.class
org\scrm\domain\wecom\query\user\tag\WeAddTagUsersQuery.class
org\scrm\domain\wecom\vo\customer\msg\WeGroupMsgTaskVo.class
org\scrm\domain\know\WeKnowCustomerCodeCount.class
org\scrm\domain\WeGroupMessageAttachments.class
org\scrm\domain\moments\dto\MomentsResultDto$CustomerList.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Article.class
org\scrm\domain\wecom\query\WeCorpQrQuery.class
org\scrm\domain\WeKfNoticeLog.class
org\scrm\domain\know\WeKnowCustomerAttachments.class
org\scrm\mapper\WeTalkMaterialMapper.class
org\scrm\service\impl\WeCategoryServiceImpl$1.class
org\scrm\domain\wecom\query\WeCorpTokenQuery.class
org\scrm\service\impl\WeFissionNoticeServiceImpl.class
org\scrm\domain\moments\dto\MomentsSendResultDTO$MomentsSendResultDTOBuilder.class
org\scrm\domain\wecom\query\living\WeModifyLivingQuery.class
org\scrm\service\IWeGroupMessageListService.class
org\scrm\domain\wecom\query\kf\WeKfKnowledgeGroupQuery.class
org\scrm\service\IWeCorpAccountService.class
org\scrm\domain\wecom\query\kfrobot\WxKfRobotBatchImportSkill.class
org\scrm\domain\fission\vo\WeFissionDataReportVo.class
org\scrm\domain\material\ao\WePoster.class
org\scrm\domain\wecom\query\third\auth\WeAuthAdminQuery.class
org\scrm\service\impl\SopGroupMsgServiceImpl.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$MediaId.class
org\scrm\domain\taggroup\query\WePresTagGroupTaskQuery.class
org\scrm\domain\qr\vo\WeLxQrAddVo.class
org\scrm\domain\live\WeLive$SendTarget.class
org\scrm\domain\qr\query\WeQrUserInfoQuery.class
org\scrm\service\IWeSysFieldTemplateService.class
org\scrm\domain\material\vo\WeMaterialVo.class
org\scrm\service\IWeFormSurveyCatalogueService.class
org\scrm\converter\CustomerAddWayConverter.class
org\scrm\domain\WePeriodAttachments.class
org\scrm\mapper\WeLeaveUserMapper.class
org\scrm\fegin\QwCorpClient.class
org\scrm\domain\material\query\ContentDetailQuery.class
org\scrm\domain\wecom\vo\qr\WeContactWayVo$WeContactWay.class
org\scrm\domain\product\order\vo\WeProductOrderWareVo.class
org\scrm\service\impl\WeRedEnvelopesRecordServiceImpl.class
org\scrm\service\impl\WeGroupTagRelServiceImpl.class
org\scrm\domain\kf\WeKfMenu.class
org\scrm\domain\sop\vo\WeSopDetailGroupVo.class
org\scrm\mapper\WeGroupCodeRangeMapper.class
org\scrm\service\IWeKfWelcomeService.class
org\scrm\domain\WeCustomer$WeCustomerBuilder.class
org\scrm\domain\wecom\vo\user\WeUserActiveVo.class
org\scrm\domain\WeTaskFission.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyListVo.class
org\scrm\domain\community\WeEmpleCodeTag.class
org\scrm\domain\substitute\customer\order\entity\WeSubstituteCustomerOrder.class
org\scrm\domain\form\query\WeFormSurveyStatisticQuery.class
org\scrm\service\IWeOperationCenterService.class
org\scrm\utils\FileUtils$1.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentCustomerListVo$WeMomentCustomer.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionAddQuery.class
org\scrm\domain\customer\WeBacthMakeCustomerTag.class
org\scrm\domain\WeCustomerLink.class
org\scrm\domain\taggroup\WePresTagGroupTaskScope.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery.class
org\scrm\domain\wecom\vo\customer\msg\WeGroupMsgSendVo.class
org\scrm\domain\wecom\vo\kf\WeCustomerInfoVo$WeKfCustomer.class
org\scrm\service\impl\WeKnowCustomerAttachmentsServiceImpl.class
org\scrm\domain\kf\query\WeKfAnswerQuery.class
org\scrm\mapper\WeShortLinkPromotionTemplateGroupMapper.class
org\scrm\domain\material\query\WeTalkQuery.class
org\scrm\domain\moments\dto\MomentsInteracteResultDto.class
org\scrm\domain\wecom\query\customer\moment\WeMomentResultQuery.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerCountQuery$WeLinkCustomerCountQueryBuilder.class
org\scrm\domain\leads\template\query\WeLeadsTemplateSettingsRequest.class
org\scrm\domain\fission\vo\WeGroupMessageExecuteUserOrGroupTipVo.class
org\scrm\domain\material\vo\WeMaterialAnalyseVo.class
org\scrm\domain\WeMarketPrize$WeMarketPrizeBuilder.class
org\scrm\domain\shortlink\vo\WeShortLinkVo.class
org\scrm\domain\wecom\callback\WeBackCustomerTagVo.class
org\scrm\domain\WeCorpAccount.class
org\scrm\domain\wecom\query\customer\transfer\WeUnassignedQuery.class
org\scrm\service\impl\WeProductStatisticsServiceImpl.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotPublishProgressVo$WxKfRobotAibotProgress.class
org\scrm\service\impl\WeGroupRobotMsgServiceImpl.class
org\scrm\domain\WeKfInfo.class
org\scrm\domain\wecom\callback\third\WeThirdRegisterCorpVo$AuthUserInfoVo.class
org\scrm\mapper\WeGroupMessageAttachmentsMapper.class
org\scrm\service\IWeLeadsFollowRecordContentService.class
org\scrm\service\impl\WeKfEventMsgServiceImpl.class
org\scrm\service\IWeGroupStatisticService.class
org\scrm\domain\operation\query\WeOperationChatQuery.class
org\scrm\domain\product\refund\query\WeProductOrderRefundQuery.class
org\scrm\utils\FileUtils.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesResultDto$WeRedEnvelopesResultDtoBuilder.class
org\scrm\domain\wecom\query\customer\msg\WeGetGroupMsgListQuery.class
org\scrm\domain\moments\dto\MomentsCancelDTO.class
org\scrm\domain\WeMarketRecord.class
org\scrm\domain\wecom\vo\kf\WeUpgradeServiceConfigVo$GroupChatRange.class
org\scrm\domain\moments\dto\MomentsParamDto$Video$VideoBuilder.class
org\scrm\domain\strategic\crowd\query\WeCorpStateTagSourceQuery.class
org\scrm\domain\wecom\query\weixin\WxJumpWxaQuery$JumpWxa.class
org\scrm\domain\wecom\vo\kfrobot\WxKfRobotAibotVo$WxKfRobotAibotOption.class
org\scrm\domain\shortlink\vo\WeShortLinkCountVo.class
org\scrm\domain\community\vo\WeCommunityTaskEmplVo.class
org\scrm\domain\form\vo\WeFormSurveyStatisticsVO.class
org\scrm\service\IWeMessagePushService.class
org\scrm\domain\material\vo\talk\WeTalkVO.class
org\scrm\domain\operation\vo\WeSessionUserAvgReplyTimeRankVo.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$Groups$GroupsBuilder.class
org\scrm\fallback\QwMomentsFallbackFactory.class
org\scrm\mapper\WeLeadsManualAddRecordMapper.class
org\scrm\mapper\WeProductOrderMapper.class
org\scrm\domain\moments\dto\MomentsInteracteParamDto$MomentsInteracteParamDtoBuilder.class
org\scrm\domain\WeCustomerInfoExpand.class
org\scrm\domain\WeShortLinkPromotionTemplateClient.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentDetailVo$AllowPartys.class
org\scrm\service\IWeGroupSopService.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerCountQuery.class
org\scrm\domain\envelopes\WeUserRedEnvelopsLimit.class
org\scrm\domain\WeFlowerCustomerTagRel.class
org\scrm\domain\sop\vo\content\WeCustomerSopToBeSentVo.class
org\scrm\domain\envelopes\WeUserRedEnvelopsLimit$WeUserRedEnvelopsLimitBuilder.class
org\scrm\domain\moments\query\WeMomentsTaskMobileRequest.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionStatisticQuery$WeShortLinkPromotionStatisticQueryBuilder.class
org\scrm\domain\wecom\query\customer\transfer\WeTransferGroupChatQuery$WeTransferGroupChatQueryBuilder.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatUpdateJoinWayQuery$WeGroupChatUpdateJoinWayQueryBuilder.class
org\scrm\domain\user\vo\WeUserScreenConditVo$ExecuteUserCondit.class
org\scrm\service\IWeLxQrScopeService.class
org\scrm\domain\moments\dto\MomentsResultDto$TaskList.class
org\scrm\service\impl\WeProductServiceImpl.class
org\scrm\service\IWeKfCustomerStatService.class
org\scrm\domain\wecom\vo\msg\WeAppMsgVo.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$TrackStates$TrackStatesBuilder.class
org\scrm\service\impl\WeKfUpgradeServiceImpl.class
org\scrm\service\impl\WeGroupCodeRangeServiceImpl.class
org\scrm\domain\wecom\vo\msgaudit\WeMsgAuditVo.class
org\scrm\service\IWeGroupRobotInfoService.class
org\scrm\converter\WeMomentsCustomerStatusConverter.class
org\scrm\mapper\WeShortLinkPromotionTemplateClientMapper.class
org\scrm\mapper\WeKfCustomerStatMapper.class
org\scrm\domain\live\query\WeLiveQuery.class
org\scrm\service\impl\WeLiveAttachmentsServiceImpl.class
org\scrm\domain\groupmsg\query\WeAddGroupMessageQuery.class
org\scrm\domain\wecom\entity\customer\strategy\WeCustomerStrategyPrivilegeEntity.class
org\scrm\service\impl\WeMomentsEstimateCustomerServiceImpl.class
org\scrm\domain\moments\query\WeMomentTabVo.class
org\scrm\mapper\WeTrackMaterialPrivacyAuthMapper.class
org\scrm\domain\customer\vo\WeCustomerLinkCountTableVo.class
org\scrm\domain\task\query\WeTasksRequest.class
org\scrm\service\IWeFissionNoticeService.class
org\scrm\domain\WeQiRuleScope.class
org\scrm\handler\TextMaterialCellWriteHandler.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionTemplateAppMsgVo.class
org\scrm\domain\wecom\vo\merchant\WeGetBillListVo$Bill.class
org\scrm\domain\wecom\vo\weixin\WxTokenVo.class
org\scrm\service\AbstractGroupMsgService.class
org\scrm\domain\robot\query\WeRobotMsgListQuery.class
org\scrm\domain\wecom\query\user\WeUserQuery.class
org\scrm\domain\ai\AiResultDTO.class
org\scrm\mapper\WeSubstituteCustomerOrderCataloguePropertyMapper.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo$WeGroupSop.class
org\scrm\mapper\WeOnJobInfoMapper.class
org\scrm\domain\WeQiRuleWeeklyUserData.class
org\scrm\domain\kf\query\WeKfAnswerGroupAddQuery.class
org\scrm\domain\WeGroupTagRel.class
org\scrm\domain\wx\coupon\WxCouponListQuery.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTemplateMomentsUpdateQuery.class
org\scrm\service\IWeChatCollectionService.class
org\scrm\mapper\WeKfNoticeLogMapper.class
org\scrm\domain\customer\vo\WeCustomerSocialConnVo.class
org\scrm\domain\qirule\vo\WeQiRuleWeeklyListVo.class
org\scrm\mapper\SysLeaveUserMapper.class
org\scrm\domain\material\query\WePosterQuery.class
org\scrm\domain\wecom\query\kf\WeUpgradeServiceQuery$Member.class
org\scrm\domain\wecom\query\user\WeUserListQuery.class
org\scrm\service\impl\WeAllocateGroupServiceImpl.class
org\scrm\domain\wecom\vo\customer\transfer\WeTransferCustomerVo$TransferCustomerVo.class
org\scrm\mapper\WeUserRedEnvelopsLimitMapper.class
org\scrm\domain\customer\vo\WeCustomersVo$WeCustomersVoBuilder.class
org\scrm\domain\user\vo\WeUserScreenConditVo.class
org\scrm\service\impl\WeTalkMaterialServiceImpl.class
org\scrm\domain\qr\vo\WeLxQrCodeDetailVo.class
org\scrm\domain\wecom\vo\kf\WeKfUserListVo.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatJoinWayQuery$WeGroupChatJoinWayQueryBuilder.class
org\scrm\service\IWeStrategicCrowdBehaviorService.class
org\scrm\fallback\QwMerchantFallBackFactory.class
org\scrm\service\IWeMarketSubService.class
org\scrm\domain\material\vo\TextMaterialExportVo$TextMaterialExportVoBuilder.class
org\scrm\domain\groupmsg\vo\WeGroupMessageTaskVo.class
org\scrm\service\IWeLeadsImportRecordService.class
org\scrm\domain\wecom\callback\WeBackCustomerGroupVo$MemChange.class
org\scrm\service\impl\WeSysFieldTemplateServiceImpl.class
org\scrm\service\IWeUnionidExternalUseridRelationService.class
org\scrm\converter\InviterStateConverter.class
org\scrm\domain\shortlink\vo\WeShortLinkStatisticsVo.class
org\scrm\domain\leads\leads\vo\WeLeadsImportResultVO.class
org\scrm\domain\fission\vo\WeFissionTrendVo.class
org\scrm\domain\kf\vo\WeKfQualityBrokenLineVo.class
org\scrm\domain\WeShortLinkPromotionTemplateAppMsg.class
org\scrm\domain\robot\vo\LwRobotListVo.class
org\scrm\domain\groupchat\vo\WeGroupChannelCountVo.class
org\scrm\domain\message\vo\WeMessageNotificationVo.class
org\scrm\domain\qr\query\WeLxQrUserInfoQuery.class
org\scrm\domain\svipgroup\WeSvipGroupRecord$WeSvipGroupRecordBuilder.class
org\scrm\domain\moments\dto\MomentsParamDto$LinkAttachments$LinkAttachmentsBuilder.class
org\scrm\domain\material\query\WeTrackMaterialPrivacyAuthQuery.class
org\scrm\domain\shortlink\vo\WeShortLinkPromotionTemplateClientVo.class
org\scrm\domain\leads\leads\entity\WeLeadsImportRecord$WeLeadsImportRecordBuilder.class
org\scrm\domain\operation\vo\WeSessionArchiveAnalysisVo.class
org\scrm\mapper\WePresTagGroupTaskMapper.class
org\scrm\domain\wecom\query\msgaudit\WeMsgAuditQuery.class
org\scrm\domain\groupchat\vo\LinkGroupChatVo.class
org\scrm\domain\leads\leads\vo\WeLeadsFollowerNumVO$WeLeadsFollowerNumVOBuilder.class
org\scrm\domain\kf\query\WeAddKfWelcomeQuery.class
org\scrm\mapper\WeKfPoolMapper.class
org\scrm\mapper\WeContentViewRecordMapper.class
org\scrm\service\IWeCommunityKeywordToGroupService.class
org\scrm\domain\WeGroupMessageTemplate.class
org\scrm\domain\material\entity\WeTalkMaterial.class
org\scrm\domain\material\vo\ContentAxisVo.class
org\scrm\domain\leads\leads\vo\WeLeadsVO.class
org\scrm\domain\wecom\vo\customer\transfer\WeTransferGroupChatVo.class
org\scrm\domain\wecom\vo\living\WeLivingInfoVo.class
org\scrm\domain\kf\vo\WeKfSceneRealCntVo.class
org\scrm\domain\kf\query\WeKfAddUpgradeConfigQuery.class
org\scrm\service\IWeKeyWordGroupSubService.class
org\scrm\domain\wecom\query\user\WeAddUserQuery.class
org\scrm\domain\WeMarketCountVo.class
org\scrm\mapper\WeProductOrderRefundMapper.class
org\scrm\service\impl\WePresTagGroupTaskTagServiceImpl.class
org\scrm\domain\moments\dto\MomentsParamDto$ImageAttachments$ImageAttachmentsBuilder.class
org\scrm\service\impl\WeMomentsUserServiceImpl.class
org\scrm\fegin\WxKfRobotClient.class
org\scrm\mapper\WeStoreCodeCountMapper.class
org\scrm\domain\kf\WeKfMsgOrEvent.class
org\scrm\service\IWeTagService.class
org\scrm\service\impl\WeEmpleCodeServiceImpl.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentTaskVo$WeMomentTask.class
org\scrm\mapper\WeLeadsAutoRecoveryMapper.class
org\scrm\service\IWeKfServicerService.class
org\scrm\domain\operation\vo\WeCustomerRemindAnalysisVo.class
org\scrm\domain\WeSideBarVo.class
org\scrm\service\impl\WeKfCustomerServiceImpl.class
org\scrm\domain\wecom\vo\kf\WeKfKnowledgeGroupListVo.class
org\scrm\domain\qr\WeQrCode.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo$WeCustomerSopContent.class
org\scrm\domain\WeFormSurveyAnswer.class
org\scrm\mapper\WeSopChangeMapper.class
org\scrm\service\impl\WeSynchRecordServiceImpl.class
org\scrm\domain\WeAllocateGroup.class
org\scrm\domain\wecom\vo\customer\state\WeGroupChatStatisticVo.class
org\scrm\domain\wecom\query\third\auth\WeExtensionRegisterQuery$WeExtensionRegisterQueryBuilder.class
org\scrm\service\impl\WeKfWelcomeServiceImpl.class
org\scrm\domain\wecom\vo\customer\seas\CustomerSeasCountVo.class
org\scrm\service\IWeLeadsStatisticService.class
org\scrm\typeHandler\WeStrategicCrowdSwipeListTypeHandler.class
org\scrm\domain\product\product\query\WeProductLineChartQuery.class
org\scrm\domain\operation\vo\WeSessionCustomerAnalysisVo.class
org\scrm\mapper\WeLeadsFollowRecordCooperateUserMapper.class
org\scrm\mapper\WeSensitiveMapper.class
org\scrm\service\impl\WeKfAnswerGroupServiceImpl.class
org\scrm\service\impl\WeCommunityKeywordToGroupServiceImpl.class
org\scrm\service\impl\strategic\state\QwUserQrCodeImpl.class
org\scrm\domain\leads\leads\query\WeLeadsUserReturnRequest.class
org\scrm\domain\leads\leads\vo\WeLeadsUserFollowTop5VO$WeLeadsUserFollowTop5VOBuilder.class
org\scrm\service\impl\WeAgentInfoServiceImpl.class
org\scrm\domain\material\vo\WeContentCountVo.class
org\scrm\domain\kf\query\WeAddKfServicerQuery.class
org\scrm\domain\leads\sea\entity\WeLeadsSeaRuleRecord.class
org\scrm\domain\qirule\query\WeQiUserInfoQuery.class
org\scrm\domain\wecom\query\third\auth\WeGetPermanentCodeQuery.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$ExecuteCustomerQUECondit.class
org\scrm\domain\WeAttachmentPeriod$WeAttachmentPeriodBuilder.class
org\scrm\domain\msgtlp\vo\WeMsgTlpVo.class
org\scrm\typeHandler\WeCustomerInfoExpandListTypeHandler.class
org\scrm\domain\customer\WeMakeCustomerTag.class
org\scrm\domain\fission\vo\WeFissionProgressVo.class
org\scrm\mapper\WeStrategicCrowdCustomerRelMapper.class
org\scrm\domain\WeSysFieldTemplate$WeSysFieldTemplateBuilder.class
org\scrm\service\IWeKfEventMsgService.class
org\scrm\mapper\WeRedEnvelopesRecordMapper.class
org\scrm\fegin\QwSysAreaClient.class
org\scrm\domain\envelopes\WeRedEnvelopes.class
org\scrm\domain\fission\WeFission$WeFissionBuilder.class
org\scrm\domain\sop\WeSopAttachments.class
org\scrm\utils\FileUtils$FileEntity$FileEntityBuilder.class
org\scrm\mapper\WeLxQrCodeMapper.class
org\scrm\service\IWeProductService.class
org\scrm\domain\qirule\vo\WeQiRuleDetailVo.class
org\scrm\mapper\WeGroupMessageSendResultMapper.class
org\scrm\service\IWeShortLinkService.class
org\scrm\domain\svipgroup\vo\WeCheckSvipGroupVo.class
org\scrm\domain\operation\vo\WeSessionGroupAnalysisVo.class
org\scrm\domain\community\vo\WeCommunityKeyWordGroupTableVo.class
org\scrm\domain\product\order\query\WePlaceAnOrderQuery.class
org\scrm\domain\moments\dto\MomentsParamDto$ExternalContactList$ExternalContactListBuilder.class
org\scrm\domain\wecom\vo\kf\WeKfAddKnowledgeGroupVo.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentListVo.class
org\scrm\domain\storecode\vo\trend\WeStoreGroupTrendVo.class
org\scrm\domain\moments\dto\TextMessageDto$TextMessageDtoBuilderImpl.class
org\scrm\service\impl\strategic\state\QwCorpStateImpl.class
org\scrm\domain\WeSensitive$WeSensitiveBuilder.class
org\scrm\domain\know\vo\WeKnowCustomerCountTabVo.class
org\scrm\domain\wecom\vo\customer\strategy\WeCustomerStrategyDetailVo$StrategyDetail.class
org\scrm\domain\groupcode\entity\WeGroupCode$WeGroupCodeBuilder.class
org\scrm\domain\product\order\query\WeProductOrderQuery.class
org\scrm\domain\wecom\vo\user\WeLeaveUserVo$Info.class
org\scrm\service\impl\WeContentSendRecordServiceImpl.class
org\scrm\domain\ExchangeContent.class
org\scrm\domain\wecom\query\msgaudit\WeMsgAuditQuery$MsgAuditInfo.class
org\scrm\domain\customer\query\WeCustomersQuery$WeCustomersQueryBuilder.class
org\scrm\domain\wecom\vo\customer\transfer\WeTransferGroupChatVo$TransferGroupChat.class
org\scrm\service\impl\WeCategoryServiceImpl.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$ExecuteCustomerQUECondits.class
org\scrm\domain\envelopes\dto\H5RedEnvelopesDetailDto.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Button$ButtonBuilder.class
org\scrm\domain\kf\vo\WeKfScenesVo.class
org\scrm\domain\wecom\query\WeMsgTemplateQuery$Videos$Video.class
org\scrm\service\IWeMessageNotificationService.class
org\scrm\domain\kf\WeKfMenuList.class
org\scrm\service\IWeSensitiveAuditScopeService.class
org\scrm\domain\kf\vo\WeKfEvaluationVo.class
org\scrm\service\impl\WeLeadsStatisticServiceImpl.class
org\scrm\service\impl\WeGroupMessageTemplateServiceImpl.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderAddRequest.class
org\scrm\service\IWeSopExecuteTargetAttachmentsService.class
org\scrm\domain\wecom\vo\third\auth\WeAuthInfoVo$RegisterCodeInfo.class
org\scrm\domain\WeCommonLinkStat$WeCommonLinkStatBuilder.class
org\scrm\mapper\WePresTagGroupTaskScopeMapper.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$MpNews$MpNewsBuilder.class
org\scrm\domain\WeAllocateGroup$WeAllocateGroupBuilder.class
org\scrm\domain\leads\sea\entity\WeLeadsSea.class
org\scrm\domain\live\WeLiveTip.class
org\scrm\domain\moments\dto\MomentsInteracteResultDto$Interacte.class
org\scrm\service\IWeLeadsSeaService.class
org\scrm\service\IWeSopPushTimeService.class
org\scrm\service\impl\WePresTagGroupTaskScopeServiceImpl.class
org\scrm\domain\fission\vo\WeFissionTabVo.class
org\scrm\service\IWeFissionInviterRecordService.class
org\scrm\service\impl\WeEmpleCodeTagServiceImpl.class
org\scrm\domain\sop\WeSopExecuteTargetAttachments.class
org\scrm\service\impl\WeKfPoolServiceImpl.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Miniprograms.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCataloguePropertyAddRequest.class
org\scrm\service\impl\WeQiRuleWeeklyUserDataServiceImpl.class
org\scrm\domain\WeChatContactSensitiveMsg.class
org\scrm\domain\leads\template\entity\WeLeadsTemplateSettings.class
org\scrm\domain\wecom\query\customer\tag\WeMarkTagQuery$WeMarkTagQueryBuilder.class
org\scrm\domain\sop\WeSopBase$WeSopBaseBuilder.class
org\scrm\mapper\WeMaterialMapper.class
org\scrm\domain\wecom\query\customer\WeCustomerQuery.class
org\scrm\domain\customer\WeCustomerPortraitDto.class
org\scrm\domain\groupmsg\vo\WeGroupMessageExecuteUsertipVo.class
org\scrm\service\IWeFissionInviterRecordSubService.class
org\scrm\domain\community\WeGroupSop.class
org\scrm\domain\wecom\query\customer\msg\WeAddCustomerMsgQuery.class
org\scrm\domain\customer\query\WeOnTheJobCustomerQuery.class
org\scrm\service\IWeGroupCodeRangeService.class
org\scrm\service\IWeContentTalkService.class
org\scrm\domain\wecom\query\kf\WeKfAddQuery.class
org\scrm\mapper\WeKfMsgMapper.class
org\scrm\service\impl\WeGroupSopServiceImpl.class
org\scrm\domain\WeMarketCountVo$CyMarketTab.class
org\scrm\service\IWeKeywordGroupViewCountService.class
org\scrm\service\IWeAiMsgService.class
org\scrm\converter\CustomerTrackStateConverter.class
org\scrm\domain\taggroup\vo\WePresTagGroupTaskTableVo.class
org\scrm\domain\hotword\vo\IYqueAnalysisHotWordTabVo.class
org\scrm\domain\moments\query\WeMomentTabVo$WeMomentTabVoBuilder.class
org\scrm\domain\WeGroupRobotInfo.class
org\scrm\service\IWeKfAnswerService.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ParmDto$TransferDetailList$TransferDetailListBuilder.class
org\scrm\domain\leads\sea\query\VisibleRange.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentResultVo.class
org\scrm\domain\wecom\vo\customer\state\WeUserBehaviorDataVo$BehaviorData.class
org\scrm\domain\moments\dto\TextMessageDto$TextMessageDtoBuilder.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo.class
org\scrm\domain\WeLxQrCode.class
org\scrm\domain\WeUserBehaviorData.class
org\scrm\domain\material\query\LinkMediaQuery$LinkMediaQueryBuilder.class
org\scrm\service\impl\WeFormSurveyCategoryServiceImpl.class
org\scrm\domain\moments\dto\MiniprogramMessageDto.class
org\scrm\domain\WeChatCollection.class
org\scrm\service\IWeLeadsSeaVisibleRangeService.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentEntity$MomentLocation.class
org\scrm\service\IWeChatContactSensitiveMsgService.class
org\scrm\service\impl\WeGroupCodeServiceImpl.class
org\scrm\service\impl\WeKnowCustomerCodeCountServiceImpl.class
org\scrm\mapper\WeCustomerTrajectoryMapper.class
org\scrm\domain\WeGroupCodeRange$WeGroupCodeRangeBuilder.class
org\scrm\service\IWeKfUserStatService.class
org\scrm\domain\wecom\query\product\QwAddProductQuery.class
org\scrm\domain\wecom\vo\user\tag\WeUserTagVo.class
org\scrm\domain\WeKeywordGroupViewCount.class
org\scrm\domain\groupcode\vo\WeGroupCodeH5Vo$WeGroupCodeH5VoBuilder.class
org\scrm\domain\operation\vo\WeCustomerAnalysisVo.class
org\scrm\domain\wecom\query\groupmsg\WeGroupMsgQuery$WeGroupMsgQueryBuilder.class
org\scrm\domain\agent\vo\WeAgentMsgListVo.class
org\scrm\domain\wecom\query\user\WeUserInviteQuery.class
org\scrm\domain\wecom\vo\weixin\WxAuthUserInfoVo.class
org\scrm\mapper\WeGroupRobotInfoMapper.class
org\scrm\service\impl\WeQiRuleManageStatisticsServiceImpl.class
org\scrm\service\impl\WeSopExecuteTargetServiceImpl.class
org\scrm\domain\WeErrorMsg.class
org\scrm\domain\WeKfUpgrade.class
org\scrm\domain\WeSideBarVo$WeSideBarVoBuilder.class
org\scrm\service\IWeCustomerInfoExpandService.class
org\scrm\domain\wecom\vo\customer\msg\WeGroupMsgListVo.class
org\scrm\domain\moments\dto\MomentsListDetailResultDto$Text.class
org\scrm\fegin\QwMerchantClient.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupOpenGidQuery.class
org\scrm\domain\sop\WeSopPushTime.class
org\scrm\converter\CustomerTypeConverter.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo$WeCustomerSopContent$WeCustomerSopContentBuilder.class
org\scrm\mapper\WeEmpleCodeMapper.class
org\scrm\domain\WeCustomerSeas.class
org\scrm\domain\leads\leads\vo\WeLeadsConversionRateVO.class
org\scrm\service\impl\strategic\state\QwNewCustomerGroupImpl.class
org\scrm\mapper\WePresTagGroupTaskStatMapper.class
org\scrm\fallback\QxAuthFallbackFactory.class
org\scrm\mapper\WeGroupStatisticMapper.class
org\scrm\service\IWeKnowCustomerCodeCountService.class
org\scrm\domain\know\WeKnowCustomerCodeTag.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo$WeSopExecuteUserConditVoBuilder.class
org\scrm\domain\storecode\vo\WeStoreCodeTableVo.class
org\scrm\mapper\WeTasksMapper.class
org\scrm\domain\agent\query\WeAgentAddQuery.class
org\scrm\mapper\WeKnowCustomerCodeCountMapper.class
org\scrm\service\IWeGroupMessageSendResultService.class
org\scrm\service\impl\WeKfUserStatServiceImpl.class
org\scrm\domain\material\vo\WePosterVo.class
org\scrm\utils\FileUtils$FileEntity.class
org\scrm\domain\WeLeaveUserInfoAllocate$WeLeaveUserInfoAllocateBuilder.class
org\scrm\fallback\QwKfFallbackFactory.class
org\scrm\domain\wecom\query\kf\WeUpgradeServiceQuery$GroupChat.class
org\scrm\domain\moments\entity\WeMomentsInteracte$WeMomentsInteracteBuilder.class
org\scrm\domain\sop\vo\content\WeSendCustomerSopContentVo$WeSendCustomerSopContentVoBuilder.class
org\scrm\domain\moments\dto\TextMessageDto.class
org\scrm\mapper\WeMsgTlpMapper.class
org\scrm\service\impl\strategic\shortlink\GroupPromotion.class
org\scrm\service\impl\WeTasksServiceImpl.class
org\scrm\domain\shortlink\vo\WeShortLinkAddVo.class
org\scrm\domain\WeCustomerLinkAttachments$WeCustomerLinkAttachmentsBuilder.class
org\scrm\service\IWeAgentInfoService.class
org\scrm\domain\wecom\query\customer\tag\WeUpdateCorpTagQuery.class
org\scrm\domain\kf\vo\WeKfSceneAnalysisVo.class
org\scrm\utils\WxPayUtils$1.class
org\scrm\domain\leads\sea\query\VisibleRange$UserRange.class
org\scrm\mapper\WePromotionTaskRecordMapper.class
org\scrm\domain\wecom\callback\WeBackCustomerVo.class
org\scrm\service\IWeStoreCodeService.class
org\scrm\fallback\QwMsgAuditFallbackFactory.class
org\scrm\domain\storecode\vo\drum\WeStoreGroupDrumVo.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Button.class
org\scrm\service\impl\WeLiveWatchUserServiceImpl.class
org\scrm\domain\moments\query\WeMomentsTaskAddRequest.class
org\scrm\service\impl\WeFormSurveyAnswerServiceImpl.class
org\scrm\service\impl\WeAttachmentPeriodServiceImpl.class
org\scrm\domain\moments\dto\MomentsResultDto.class
org\scrm\domain\svipgroup\vo\WeSvipGroupVo.class
org\scrm\domain\wecom\vo\user\WeThirdLoginUserVo.class
org\scrm\service\IWeGroupMessageTemplateService.class
org\scrm\domain\WeGroup.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo$WeGroupSopContent$WeGroupSopContentBuilder.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentCommentListVo$Comment.class
org\scrm\domain\wecom\vo\customer\transfer\WeUnassignedVo.class
org\scrm\domain\WeMsgTlp$Applet.class
org\scrm\service\IWeQiRuleService.class
org\scrm\mapper\WeMomentsAttachmentsMapper.class
org\scrm\domain\qirule\vo\WeQiRuleListVo.class
org\scrm\domain\wecom\callback\third\WeThirdBackBaseVo.class
org\scrm\service\impl\WeKfUpgradeServerServiceImpl.class
org\scrm\domain\side\WeChatItem.class
org\scrm\domain\wecom\query\customer\link\WeLinkCustomerListsQuery.class
org\scrm\domain\moments\dto\MomentsCreateResultDto$WeMomentCustomerVo.class
org\scrm\domain\WeTagGroup.class
org\scrm\domain\live\WeLiveAttachments.class
org\scrm\domain\wecom\vo\living\WeLivingStatInfoVo.class
org\scrm\service\impl\WeCustomerTrajectoryServiceImpl.class
org\scrm\domain\wecom\query\customer\state\WeGroupChatStatisticQuery.class
org\scrm\domain\side\dto\WeChatItemDto.class
org\scrm\domain\live\WeLiveTaskUserDetail.class
org\scrm\domain\hotword\WeAnalysisHotWord.class
org\scrm\domain\wecom\vo\living\WeLivingStatInfoVo$LivingUser.class
org\scrm\domain\wecom\vo\merchant\WeGetBillListVo$Contact.class
org\scrm\domain\WeProductOrder.class
org\scrm\domain\wecom\vo\kf\WeKfSyncMsgVo.class
org\scrm\domain\leads\leads\vo\WeLeadsDataTrendVO.class
org\scrm\service\impl\WeKfAnswerLikeQuestionServiceImpl.class
org\scrm\service\impl\WeProductOrderRefundServiceImpl.class
org\scrm\domain\wecom\entity\customer\WeCustomerFollowInfoEntity.class
org\scrm\domain\customer\vo\WeCustomerDetailInfoVo$CompanyOrPersonTag$CompanyOrPersonTagBuilder.class
org\scrm\service\impl\WeChatSideServiceImpl.class
org\scrm\domain\wecom\query\msg\WeAppMsgQuery$Article$ArticleBuilder.class
org\scrm\domain\WeMarketSub.class
org\scrm\domain\wecom\vo\agentdev\WeUnionidTransformExternalUserIdVO.class
org\scrm\fegin\QwMediaClient.class
org\scrm\domain\wecom\vo\customer\link\WeLinkCustomerVo.class
org\scrm\service\IWeAllocateGroupService.class
org\scrm\mapper\WeFormSurveySiteStasMapper.class
org\scrm\mapper\WeRedEnvelopesMapper.class
org\scrm\mapper\WeLiveMapper.class
org\scrm\service\impl\WeKfNoticeLogServiceImpl.class
org\scrm\domain\operation\vo\WeSessionGroupTotalCntVo.class
org\scrm\domain\material\vo\WeCategoryNewVo.class
org\scrm\domain\wecom\query\WeProvideTokenQuery.class
org\scrm\fegin\QwAppMsgClient.class
org\scrm\domain\kf\vo\WeKfUpgradeServiceConfigVO.class
org\scrm\domain\moments\vo\WeMomentsEstimateUserVO.class
org\scrm\domain\wecom\query\user\WeLeaveUserQuery.class
org\scrm\domain\system\config\vo\SysConfigVo.class
org\scrm\domain\qirule\vo\WeQiRuleUserVo.class
org\scrm\domain\svipgroup\WeSvipGroupRecord.class
org\scrm\service\impl\WeMomentsCustomerServiceImpl.class
org\scrm\domain\kf\vo\WeKfScenesListVo.class
org\scrm\config\mybatis\GenericTypeHandler.class
org\scrm\service\impl\WeGroupServiceImpl.class
org\scrm\domain\wecom\vo\customer\msg\WeGroupMsgVo.class
org\scrm\domain\wecom\entity\customer\groupChat\WeOwnerFilterEntity.class
org\scrm\service\impl\WeRedEnvelopesServiceImpl.class
org\scrm\typeHandler\WeCustomerInfoValHandler$1.class
org\scrm\domain\sop\WeSopExecuteTarget.class
org\scrm\service\impl\WeLeadsSeaBaseSettingsServiceImpl.class
org\scrm\domain\hotword\vo\IYqueAnalysisHotWordVo$IYqueAnalysisHotWordVoBuilder.class
org\scrm\domain\envelopes\query\H5RedEnvelopesParmQuery.class
org\scrm\domain\wecom\vo\customer\msg\WeAddCustomerMsgVo.class
org\scrm\domain\wecom\query\product\QwAddProductQuery$Image.class
org\scrm\service\IWeSubstituteCustomerOrderCataloguePropertyService.class
org\scrm\mapper\WeGroupTagRelMapper.class
org\scrm\service\impl\WeKfAnswerAttachmentsServiceImpl.class
org\scrm\fallback\QwDeptFallbackFactory.class
org\scrm\domain\wecom\query\living\WeAddLivingQuery$WeAddLivingQueryBuilder.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ResultDto$WeRedEnvelopesV3ResultDtoBuilder.class
org\scrm\domain\wecom\query\customer\groupchat\WeGroupChatListQuery$WeGroupChatListQueryBuilder.class
org\scrm\domain\wecom\vo\third\auth\WeAuthAgentInfoVo.class
org\scrm\domain\WeUnionidExternalUseridRelation.class
org\scrm\mapper\WeGroupUserStatisticMapper.class
org\scrm\mapper\WeKnowCustomerCodeMapper.class
org\scrm\domain\sop\vo\content\WeSendGroupSopContentVo$WeGroupSop$WeGroupSopBuilder.class
org\scrm\domain\groupcode\entity\WeGroupCodeTagRel$WeGroupCodeTagRelBuilder.class
org\scrm\domain\sop\vo\WeSopExecuteEndVo$ToChangeIntoOtherSop.class
org\scrm\service\IWeChatItemService.class
org\scrm\domain\wecom\vo\WeResultVo.class
org\scrm\domain\material\entity\WeMaterial$WeMaterialBuilder.class
org\scrm\domain\wecom\query\agentdev\WeUnionidTransformExternalUserIdQuery.class
org\scrm\service\IWeShortLinkPromotionTemplateMomentsService.class
org\scrm\domain\community\vo\WeKeywordGroupViewCountVo.class
org\scrm\domain\know\vo\WeKnowCustomerJumpContentVo.class
org\scrm\domain\customer\vo\WeCustomerPortraitVo.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentCustomerListVo.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCataloguePropertyRequest.class
org\scrm\service\IWeCommonLinkStatService.class
org\scrm\domain\WeChatContactMsg.class
org\scrm\domain\groupmsg\query\WeAddMsgTemplateQuery$Images$Image.class
org\scrm\domain\substitute\customer\order\query\WeSubstituteCustomerOrderCatalogueMoveRequest.class
org\scrm\service\impl\WeShortLinkPromotionTemplateMomentsServiceImpl.class
org\scrm\domain\community\WeCommunityNewGroup$WeCommunityNewGroupBuilder.class
org\scrm\domain\sop\vo\WeSopExecuteUserConditVo$ExecuteUserCondit$ExecuteUserConditBuilder.class
org\scrm\domain\system\config\vo\SysConfigVo$WxApp$WxAppBuilder.class
org\scrm\domain\wecom\query\customer\WeCustomerListQuery$WeCustomerListQueryBuilder.class
org\scrm\domain\wecom\query\user\WeUserActiveQuery.class
org\scrm\service\IWePresTagGroupTaskStatService.class
org\scrm\domain\leads\template\entity\WeLeadsTemplateSettings$WeLeadsTemplateSettingsBuilder.class
org\scrm\domain\wecom\query\kf\WeUpgradeServiceQuery.class
org\scrm\service\impl\WeLeadsSeaVisibleRangeServiceImpl.class
org\scrm\domain\leads\sea\vo\WeLeadsSeaVo.class
org\scrm\domain\qr\vo\WeLxQrCodeReceiveLineVo.class
org\scrm\domain\wecom\callback\third\WeThirdBackUserVo.class
org\scrm\domain\system\config\vo\SysConfigVo$CorpVo.class
org\scrm\domain\wecom\vo\qr\WeAddWayVo.class
org\scrm\mapper\WeChatSideMapper.class
org\scrm\domain\ai\WeAiTokenRecord$WeAiTokenRecordBuilder.class
org\scrm\service\impl\WeMaterialServiceImpl.class
org\scrm\domain\svipgroup\vo\WeSvipGroupTabVo.class
org\scrm\service\impl\WeQiRuleServiceImpl.class
org\scrm\mapper\WeLeadsMapper.class
org\scrm\service\impl\WeSubstituteCustomerOrderCataloguePropertyServiceImpl.class
org\scrm\domain\leads\record\query\WeLeadsFollowRecordReplyRequest.class
org\scrm\domain\material\query\LinkMediaCollectQuery.class
org\scrm\domain\sop\vo\WeSopExecuteConditVo$ExecuteCustomerCondit.class
org\scrm\service\IWeTagGroupService.class
org\scrm\domain\community\vo\WeGroupCodeVo$WeGroupCodeVoBuilderImpl.class
org\scrm\fegin\QxAppletClient.class
org\scrm\config\AsyncConfig.class
org\scrm\domain\WeKfAnswerLikeQuestion.class
org\scrm\domain\groupchat\query\WeMakeGroupTagQuery.class
org\scrm\handler\WeLeadsImportDataListener.class
org\scrm\domain\storecode\vo\trend\WeStoreShopGuideTrendVo.class
org\scrm\domain\wx\WxBaseQuery.class
org\scrm\utils\WxPayUtils.class
org\scrm\service\IWeContentSendRecordService.class
org\scrm\domain\wecom\entity\customer\moment\WeMomentCustomersEntity.class
org\scrm\service\impl\WeLiveTipServiceImpl.class
org\scrm\domain\wecom\query\customer\state\WeUserBehaviorDataQuery.class
org\scrm\domain\wecom\query\kf\WeKfGetStatisticQuery.class
org\scrm\domain\WeKfCustomer.class
org\scrm\domain\wecom\vo\kf\WeKfGetMsgVo.class
org\scrm\domain\leads\leads\vo\Properties$PropertiesBuilder.class
org\scrm\domain\operation\vo\WeCustomerTotalCntVo$WeCustomerTotalCntVoBuilder.class
org\scrm\domain\taggroup\vo\WePresTagGroupTaskVo.class
org\scrm\service\IWeCommunityNewGroupService.class
org\scrm\domain\envelopes\dto\WeRedEnvelopesV3ResultDto.class
org\scrm\domain\moments\dto\CancelMomentTaskDto.class
org\scrm\domain\wecom\vo\customer\moment\WeMomentTaskVo.class
org\scrm\service\IWeSubstituteCustomerOrderCatalogueService.class
org\scrm\domain\storecode\entity\WeStoreCodeCount.class
org\scrm\domain\WeProductDayStatistics.class
org\scrm\service\impl\WeLeadsSeaRuleRecordServiceImpl.class
org\scrm\event\SopReevaluationEvent.class
org\scrm\service\impl\WeAiMsgServiceImpl.class
org\scrm\service\IWeFlowerCustomerTagRelService.class
org\scrm\mapper\WeKfAnswerLikeQuestionMapper.class
org\scrm\domain\fission\vo\WeExecuteUserOrGroupConditVo.class
org\scrm\domain\moments\entity\WeMomentsCustomer$WeMomentsCustomerBuilder.class
org\scrm\domain\wecom\vo\customer\WeCustomerDetailVo.class
org\scrm\domain\wecom\vo\merchant\WeGetBillListVo$Commodity.class
org\scrm\domain\wecom\vo\qr\WeContactWayListVo.class
org\scrm\domain\moments\dto\MomentsCreateResultDto$Result.class
org\scrm\domain\customer\vo\WeCustomerDelCountVo.class
org\scrm\config\redisson\RedissonConfig.class
org\scrm\domain\product\product\vo\WeUserOrderTop5Vo.class
org\scrm\domain\wecom\vo\living\WeLivingInfoVo$LivingInfo.class
org\scrm\service\IWeStoreCodeConfigService.class
org\scrm\domain\moments\dto\MomentsParamDto$SenderList.class
org\scrm\domain\wecom\query\agentdev\WeTransformUserIdQuery.class
org\scrm\mapper\WeSvipGroupMapper.class
org\scrm\domain\corp\vo\WeCorpAccountVo.class
org\scrm\domain\material\ao\PurePoster.class
org\scrm\domain\leads\record\vo\WeLeadsFollowRecordVO.class
org\scrm\domain\product\order\vo\WeProductOrderPayInfoVo.class
org\scrm\service\impl\WeErrorMsgServiceImpl.class
org\scrm\service\IWeHotWordService.class
org\scrm\domain\wecom\vo\kf\WeKfStatisticListVo$KfStatisticVo.class
org\scrm\domain\kf\vo\WeKfQualityAnalysisVo.class
org\scrm\mapper\WeSysFieldTemplateMapper.class
org\scrm\domain\leads\leads\query\WeLeadsAllocationRequest.class
org\scrm\domain\WeKeywordGroupViewCount$WeKeywordGroupViewCountBuilder.class
org\scrm\mapper\WeContentSendRecordMapper.class
org\scrm\mapper\WeLiveTipMapper.class
org\scrm\service\IWeQrTagRelService.class
org\scrm\mapper\WeFissionInviterRecordMapper.class
org\scrm\domain\kf\vo\WeKfServicerListVo.class
org\scrm\domain\shortlink\query\WeShortLinkPromotionTaskEndQuery.class
org\scrm\mapper\WeKfStatisticMapper.class
org\scrm\service\IWeMomentsTaskStatisticService.class
org\scrm\domain\wecom\vo\agent\vo\WeAgentDetailVo$AllowUserInfos.class
org\scrm\service\impl\WeLxQrCodeServiceImpl.class
org\scrm\domain\message\entity\WeMessageNotification$WeMessageNotificationBuilder.class
org\scrm\domain\WeCustomerLinkCount$WeCustomerLinkCountBuilder.class
org\scrm\domain\leads\template\entity\WeLeadsTemplateTableEntryContent.class
org\scrm\domain\sop\dto\WeSopPushTaskDto.class
org\scrm\domain\message\query\WeClientLeadsNewsMqRequest.class
org\scrm\mapper\WeErrorMsgMapper.class
org\scrm\domain\wecom\vo\kf\WeKfDetailVo.class
org\scrm\service\impl\strategic\shortlink\DefaultPromotion.class
org\scrm\service\impl\WeLiveServiceImpl.class
org\scrm\domain\wecom\query\kf\WeKfAddKnowledgeGroupQuery.class
org\scrm\domain\welcomemsg\WeDefaultWelcomeMsg$WeDefaultWelcomeMsgBuilder.class
org\scrm\service\IWeSvipGroupService.class
org\scrm\domain\leads\record\vo\WeLeadsFollowRecordContentVO$Content.class
org\scrm\domain\groupchat\vo\LinkGroupChatListVo.class
org\scrm\domain\groupchat\query\WeOnTheJobGroupQuery.class
org\scrm\domain\wecom\vo\kf\WeKfUserVo.class
org\scrm\domain\wecom\query\user\tag\WeAddUserTagQuery.class
org\scrm\service\impl\strategic\shortlink\AppMsgPromotion.class
org\scrm\domain\customer\query\WeOnTheJobCustomerQuery$WeOnTheJobCustomer.class
org\scrm\service\impl\WeLeadsFollowRecordCooperateServiceImpl.class
org\scrm\domain\product\order\vo\WeProductOrderVo.class
org\scrm\domain\wecom\query\customer\WeCustomerQuery$WeCustomerQueryBuilder.class
org\scrm\domain\moments\dto\MomentsParamDto$VisibleRange$VisibleRangeBuilder.class
org\scrm\fallback\QwAppMsgFallbackFactory.class
org\scrm\service\impl\WeKeyWordGroupSubServiceImpl.class
org\scrm\domain\material\vo\ContentDataDetailVo.class
org\scrm\domain\moments\query\WeMomentsJobIdToMomentsIdRequest$WeMomentsJobIdToMomentsIdRequestBuilder.class
org\scrm\domain\hotword\query\MsgAuditQuery$MsgAuditQueryBuilder.class
org\scrm\domain\wecom\query\kf\WeKfCustomerQuery.class
org\scrm\fallback\QwProductAlbumFallbackFactory.class
org\scrm\domain\form\query\WeFormSurveyRadioQuery.class
org\scrm\domain\moments\vo\WeMomentsUserVO.class
org\scrm\domain\wecom\entity\customer\WeCustomerFollowUserEntity.class
org\scrm\service\impl\WeGroupCodeTagRelServiceImpl.class
org\scrm\fegin\QwDeptClient.class
org\scrm\domain\wecom\callback\WeBackCustomerGroupVo.class
org\scrm\domain\storecode\vo\tab\WeStoreTabVo.class
org\scrm\mapper\WePhoneCallRecordMapper.class
org\scrm\domain\leads\leads\entity\WeLeadsAutoRecovery$WeLeadsAutoRecoveryBuilder.class
org\scrm\domain\wecom\query\customer\tag\WeAddCorpTagQuery$WeAddCorpTagQueryBuilder.class
org\scrm\service\impl\WeSensitiveAuditScopeServiceImpl.class
org\scrm\domain\wecom\callback\third\WeThirdBackCustomerVo.class
org\scrm\domain\WeShortLinkPromotionDayStat$WeShortLinkPromotionDayStatBuilder.class
org\scrm\service\impl\WeSopExecuteTargetAttachmentsServiceImpl.class
org\scrm\domain\qr\query\WeLxQrCodeListQuery.class
org\scrm\service\HunYuanService.class
org\scrm\domain\wecom\vo\customer\link\WeLinkWecustomerCountVo.class
org\scrm\domain\operation\vo\WeCustomerTotalCntVo.class
org\scrm\service\impl\WeKnowCustomerCodeServiceImpl.class
org\scrm\domain\kf\vo\WeKfConsultAnalysisVo.class
org\scrm\service\impl\WeDefaultWelcomeMsgServiceImpl.class
org\scrm\service\impl\WeLxQrCodeLogServiceImpl.class
org\scrm\domain\wecom\vo\customer\groupchat\WeGroupChatGetJoinWayVo$JoinWay.class
org\scrm\domain\qr\vo\WeQrCodeScanCountVo.class
org\scrm\mapper\WeKeyWordGroupSubMapper.class
org\scrm\mapper\WeMessageNotificationMapper.class
org\scrm\domain\leave\WeOnJobInfo$WeOnJobInfoBuilder.class
org\scrm\domain\wecom\vo\kf\WeKfKnowledgeGroupListVo$KnowledgeGroupVo.class
org\scrm\domain\groupmsg\vo\WeGroupMessageDetailVo.class
org\scrm\service\impl\WeMsgTlpAttachmentsServiceImpl.class
org\scrm\domain\leads\sea\query\WeLeadsSeaSaveRequest.class
org\scrm\mapper\WeLeadsSeaMapper.class
org\scrm\service\IWeAllocateCustomerService.class
org\scrm\service\impl\WeStrategicCrowdServiceImpl.class
