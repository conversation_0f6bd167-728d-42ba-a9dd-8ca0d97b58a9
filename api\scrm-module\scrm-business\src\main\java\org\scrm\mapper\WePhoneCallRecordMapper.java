package org.scrm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.scrm.domain.phone.WePhoneCallRecord;

import java.util.List;
import java.util.Map;

/**
 * 拨打电话记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
public interface WePhoneCallRecordMapper extends BaseMapper<WePhoneCallRecord> {

    /**
     * 查询指定员工的拨打电话记录
     * 
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID（可选）
     * @return 拨打记录列表
     */
    List<WePhoneCallRecord> findCallRecordsByWeUserId(@Param("weUserId") String weUserId, 
                                                      @Param("sopBaseId") String sopBaseId);

    /**
     * 查询指定客户的拨打电话记录
     * 
     * @param externalUserid 客户外部ID
     * @param weUserId 员工企微ID
     * @return 拨打记录列表
     */
    List<WePhoneCallRecord> findCallRecordsByCustomer(@Param("externalUserid") String externalUserid, 
                                                      @Param("weUserId") String weUserId);

    /**
     * 查询指定SOP执行目标的拨打记录
     * 
     * @param executeTargetId SOP执行目标ID
     * @return 拨打记录列表
     */
    List<WePhoneCallRecord> findCallRecordsByExecuteTarget(@Param("executeTargetId") String executeTargetId);

    /**
     * 检查客户是否已被拨打过电话
     * 
     * @param externalUserid 客户外部ID
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID
     * @return 是否已拨打过
     */
    boolean hasCalledCustomer(@Param("externalUserid") String externalUserid, 
                              @Param("weUserId") String weUserId, 
                              @Param("sopBaseId") String sopBaseId);

    /**
     * 统计员工在指定SOP下的拨打电话数量
     *
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID
     * @return 拨打数量
     */
    int countCallRecordsBySop(@Param("weUserId") String weUserId,
                              @Param("sopBaseId") String sopBaseId);

    /**
     * 获取特定SOP的拨打统计信息
     *
     * @param weUserId 员工企微ID
     * @param sopBaseId SOP基础ID
     * @param executeTargetAttachId 执行目标附件ID（可选）
     * @return 统计信息Map，包含waitingCount和calledCount
     */
    Map<String, Object> getSopCallStatistics(@Param("weUserId") String weUserId,
                                             @Param("sopBaseId") String sopBaseId,
                                             @Param("executeTargetAttachId") String executeTargetAttachId);
}
