<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeStoreCodeCountMapper">

    <select id="countWeStoreShopGuideTab" resultType="org.scrm.domain.storecode.vo.tab.WeStoreShopGuideTabVo">
        SELECT (
                   SELECT
                       COUNT(DISTINCT unionid)
                   from
                       we_store_code_count where source=1
               )  as totalShopGuideScannNumber,
               (
                   SELECT
                       COUNT(DISTINCT unionid)
                   from
                       we_store_code_count where date_format( create_time, '%y%m%d' ) = date_format( curdate(), '%y%m%d' ) and  source=1
               ) as tdShopGuideScannNumber,
               (
                   SELECT
                       COUNT(DISTINCT unionid)
                   from
                       we_store_code_count  where  date_format(create_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d') and  source=1
               ) as ydShopGuideScannNumber,
               (
                   SELECT count(DISTINCT external_userid) from we_customer where state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%')
               ) as customerTotalNumber,
               (
                   SELECT count(DISTINCT external_userid) from we_customer where state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%') AND  date_format(add_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
               ) as ydCustomerNumber,
               (
                   SELECT count(DISTINCT external_userid) from we_customer where state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%') AND  date_format(create_time,'%y%m%d') = date_format(date_sub(curdate(),interval 1 day),'%y%m%d')
               ) as tdCustomerNumber
    </select>


    <select id="countWeStoreTab" resultType="org.scrm.domain.storecode.vo.tab.WeStoreTabVo">
        SELECT
        (
        SELECT
        COUNT(DISTINCT unionid)
        from
        we_store_code_count where source=1
         <if test="storeCodeId !=null">and store_code_id=#{storeCodeId}</if>
        )  as totalShopGuideScannNumber,
        (
        SELECT
        COUNT(DISTINCT if(date_format(create_time,'%y%m%d') = date_format(curdate(),'%y%m%d'),unionid,0))
        from
        we_store_code_count where source=1
        <if test="storeCodeId !=null">and store_code_id=#{storeCodeId}</if>
        ) as tdShopGuideScannNumber,

        (
        SELECT count(DISTINCT external_userid) from we_customer where state in (
        SELECT shop_guide_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        )
        ) as customerTotalNumber,
        (
        SELECT count(DISTINCT external_userid) from we_customer where state in (
        SELECT shop_guide_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        ) AND  date_format(add_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
        ) as ydCustomerNumber,

        (
        SELECT
        COUNT(DISTINCT unionid)
        from
        we_store_code_count where source=2
        <if test="storeCodeId !=null">and store_code_id=#{storeCodeId}</if>
        )  as totalStoreGroupScannNumber,
        (
        SELECT
        COUNT(DISTINCT if(date_format(create_time,'%y%m%d') = date_format(curdate(),'%y%m%d'),unionid,0))
        from
        we_store_code_count where source=2
        <if test="storeCodeId !=null">and store_code_id=#{storeCodeId}</if>
        ) as tdStoreGroupScannNumber,

        (
        SELECT COUNT(DISTINCT wgm.user_id)
        FROM we_group_member wgm
        where wgm.state in (
        SELECT group_code_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        )
        AND wgm.quit_scene IS NULL

        ) as totalJoinGroupMemberNumber,

        (
        SELECT COUNT(DISTINCT wgm.user_id)
        FROM we_group_member wgm
        where wgm.state in (
        SELECT group_code_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        ) and date_format(join_time,'%y%m%d') = date_format(curdate(),'%y%m%d') AND wgm.quit_scene IS NULL
        ) as tdJoinGroupMemberNumber,

        (
        SELECT COUNT(DISTINCT wgm.user_id)
        FROM we_group_member wgm
        where wgm.state in (
        SELECT group_code_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        ) 	AND wgm.quit_scene IS NOT NULL

        ) as totalExitGroupMemberNumber,

        (
        SELECT COUNT(DISTINCT wgm.user_id)
        FROM we_group_member wgm
        where wgm.state in (
        SELECT group_code_state from we_store_code
        <where>
            <if test="storeCodeId !=null">id=#{storeCodeId}</if>
        </where>
        )  AND wgm.quit_scene IS NOT NULL AND  date_format(quit_time,'%y%m%d') = date_format(curdate(),'%y%m%d')
        ) as tdExitGroupMemberNumber
    </select>


    <select id="countWeStoreGroupTab" resultType="org.scrm.domain.storecode.vo.tab.WeStoreGroupTabVo">
        SELECT
            (
                SELECT
                    COUNT( DISTINCT unionid )
                FROM
                    we_store_code_count
                WHERE
                    source = 2
            ) AS totalStoreGroupScannNumber,
            (
                SELECT
                    COUNT( DISTINCT unionid )
                FROM
                    we_store_code_count
                WHERE
                    source = 2
                  AND date_format( create_time, '%y%m%d' ) = date_format( curdate(), '%y%m%d' )
            ) AS tdStoreGroupScannNumber,
            (
                SELECT
                    COUNT( DISTINCT unionid )
                FROM
                    we_store_code_count
                WHERE
                    source = 2
                  AND date_format( create_time, '%y%m%d' ) = date_format( date_sub( curdate(), INTERVAL 1 DAY ), '%y%m%d' )
            ) AS ydStoreGroupScannNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%') and wgm.quit_time IS NULL
            ) AS totalJoinGroupMemberNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%')
                  AND wgm.quit_scene IS NOT NULL
            ) AS totalExitGroupMemberNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%') and wgm.quit_time IS NULL
                  AND date_format( wgm.join_time, '%y%m%d' ) = date_format( curdate(), '%y%m%d' )
            ) AS tdJoinGroupMemberNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%')
                  AND wgm.quit_time IS NOT NULL
                  AND date_format( wgm.quit_time, '%y%m%d' ) = date_format( curdate(), '%y%m%d' )
            ) AS tdExitGroupMemberNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%')
                  AND wgm.quit_time IS NOT NULL
                  AND date_format( wgm.join_time, '%y%m%d' ) = date_format( date_sub( curdate(), INTERVAL 1 DAY ), '%y%m%d' )
            ) AS ydJoinGroupMemberNumber,
            (
                SELECT
                    COUNT( DISTINCT wgm.user_id )
                FROM
                    we_group_member wgm
                WHERE
                    wgm.state  LIKE CONCAT('%',#{state,jdbcType=VARCHAR},'%')
                  AND wgm.quit_time IS NOT NULL
                  AND date_format( wgm.quit_time, '%y%m%d' ) = date_format( date_sub( curdate(), INTERVAL 1 DAY ), '%y%m%d' )
            ) AS ydExitGroupMemberNumber
    </select>


    <select id="countStoreShopGuideDrum" resultType="org.scrm.domain.storecode.vo.drum.WeStoreShopGuideDrumVo">
        SELECT
            a.store_name,
            a.customerNumber
        FROM
            (
                SELECT
                    wsc.store_name,
                    (
                        SELECT
                            COUNT( DISTINCT wc.external_userid )
                        FROM
                            we_customer wc
                        WHERE
                            wc.state = wsc.shop_guide_state
                          AND date_format( wc.add_time, '%Y-%m-%d' ) BETWEEN #{beginTime}
                            AND #{endTime}
                    ) AS customerNumber
                FROM
                    we_store_code wsc
                where  wsc.del_flag=0
                GROUP BY
                    wsc.id
            ) a
        ORDER BY
            customerNumber DESC
            LIMIT 10
    </select>

    <select id="countStoreShopGroupDrum" resultType="org.scrm.domain.storecode.vo.drum.WeStoreGroupDrumVo">
        SELECT
            a.store_name,
            a.customerNumber
        FROM
            (
                SELECT
                    wsc.store_name,
                    (
                        SELECT
                            COUNT( DISTINCT wgm.user_id )
                        FROM
                            we_group_member wgm
                        WHERE
                            wgm.state = wsc.group_code_state AND wgm.quit_scene IS NULL
                          AND date_format( wgm.join_time, '%Y-%m-%d' ) BETWEEN #{beginTime}
                            AND #{endTime}
                    ) AS customerNumber
                FROM
                    we_store_code wsc
                where  wsc.del_flag=0
                GROUP BY
                    wsc.id
            ) a
        ORDER BY
            customerNumber DESC
            LIMIT 10
    </select>


    <select id="countShopGuideReport" resultType="org.scrm.domain.storecode.vo.datareport.WeStoreShopGuideReportVo">
        SELECT
        wscc.store_code_id,
        date_format(wsc.create_time,'%Y-%m-%d') as createTime,
        wsc.area,
        wsc.store_name,
        COUNT(IF(date_format( wscc.create_time, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} AND #{weStoreCode.endTime},unionid,0)) as totalScannNumber,
        (SELECT COUNT(DISTINCT wc.external_userid) FROM we_customer wc WHERE wc.state=wsc.shop_guide_state
        AND date_format(wc.add_time, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} AND #{weStoreCode.endTime}
        ) as customerTotalNumber,

        COUNT(DISTINCT if(date_format(wscc.create_time,'%y%m%d') = date_format(curdate(),'%y%m%d'),unionid,0)) as tdScannNumber,

        (SELECT COUNT(DISTINCT wc.external_userid) FROM we_customer wc WHERE wc.state=wsc.shop_guide_state
        AND date_format(wc.add_time, '%Y-%m-%d' )  = date_format(curdate(),'%Y-%m-%d')
        ) as tdCustomerNumber
        FROM
        we_store_code_count wscc
        LEFT JOIN we_store_code wsc ON wscc.store_code_id=wsc.id
        WHERE wscc.source=1 and wsc.del_flag=0
        <if test="weStoreCode.area !=null  and weStoreCode.area !=''">and wsc.area = #{weStoreCode.area}</if>
        <if test="weStoreCode.storeName != null and weStoreCode.storeName != ''">and wsc.store_name = #{weStoreCode.storeName}</if>
        <if test="weStoreCode.storeCodeId !=null"> and wsc.id=#{weStoreCode.storeCodeId}</if>
        GROUP BY wscc.store_code_id
        ORDER BY date_format(wsc.create_time,'%Y-%m-%d') DESC
    </select>


    <select id="countStoreGroupReport" resultType="org.scrm.domain.storecode.vo.datareport.WeStoreGroupReportVo">
        SELECT
        wscc.store_code_id,
        date_format(wsc.create_time,'%Y-%m-%d') as createTime,
        wsc.address,
        wsc.area,
        wsc.store_name,
        COUNT(IF(date_format( wscc.create_time, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} AND #{weStoreCode.endTime},unionid,0)) as totalStoreGroupScannNumber,
        (SELECT COUNT(DISTINCT wgm.user_id) FROM we_group_member wgm WHERE wgm.state=wsc.group_code_state and wgm.quit_scene IS NULL
        AND date_format(wgm.create_time, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} AND #{weStoreCode.endTime}
        ) as totalJoinGroupMemberNumber,

        (SELECT COUNT(DISTINCT wgm.user_id) FROM we_group_member wgm WHERE wgm.state=wsc.group_code_state and wgm.quit_scene IS NOT NULL
        AND date_format(wgm.create_time, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} AND #{weStoreCode.endTime}
        ) as totalExitGroupMemberNumber,

        COUNT(DISTINCT if(date_format(wscc.create_time,'%y%m%d') = date_format(curdate(),'%y%m%d'),unionid,0)) as tdStoreGroupScannNumber,

        (SELECT COUNT(DISTINCT wgm.user_id) FROM we_group_member wgm WHERE wgm.state=wsc.group_code_state  and wgm.quit_scene IS NULL
        AND date_format(wgm.create_time, '%Y-%m-%d' )  = date_format(curdate(),'%Y-%m-%d')
        ) as tdJoinGroupMemberNumber,

        (SELECT COUNT(DISTINCT wgm.user_id) FROM we_group_member wgm WHERE wgm.state=wsc.group_code_state  and wgm.quit_scene IS NOT NULL
        AND date_format(wgm.create_time, '%Y-%m-%d' )  = date_format(curdate(),'%Y-%m-%d')
        ) as tdExitGroupMemberNumber

        FROM
        we_store_code_count wscc
        LEFT JOIN we_store_code wsc ON wscc.store_code_id=wsc.id
        WHERE wscc.source=2 and wsc.del_flag=0
        <if test="weStoreCode.area !=null  and weStoreCode.area !=''">and wsc.area = #{weStoreCode.area}</if>
        <if test="weStoreCode.storeName != null and weStoreCode.storeName != ''">and wsc.store_name = #{weStoreCode.storeName}</if>
        <if test="weStoreCode.storeCodeId !=null"> and wsc.id=#{weStoreCode.storeCodeId}</if>
        GROUP BY wscc.store_code_id
        ORDER BY date_format(wsc.create_time,'%Y-%m-%d') DESC
    </select>

    <select id="countStoreShopGuideTrend" resultType="org.scrm.domain.storecode.vo.trend.WeStoreShopGuideTrendVo">
        SELECT
        sdd.date as dataTime,
        (
        SELECT
        count(DISTINCT ws.unionid)
        FROM
        we_store_code_count ws
        WHERE ws.source=1 and
        DATE_FORMAT( ws.create_time, '%Y-%m-%d' )= DATE_FORMAT( sdd.date, '%Y-%m-%d' )

        <choose>
            <when test="weStoreCode.storeCodeId != null">
                <choose>
                    <when test="weStoreCode.area !=null and weStoreCode.area !=''">
                        AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId} and wsc.area =  #{weStoreCode.area})
                    </when>
                    <otherwise>
                        AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId})
                    </otherwise>
                </choose>
            </when>
            <when test="weStoreCode.storeCodeId == null and  weStoreCode.area !=null and weStoreCode.area !=''">
                AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.area =  #{weStoreCode.area})
            </when>
        </choose>
        ) AS totalShopGuideScannNumber,
        (
        SELECT
        count(DISTINCT wc.external_userid)
        FROM
        we_customer wc
        WHERE
        DATE_FORMAT( wc.add_time, '%Y-%m-%d' )= DATE_FORMAT( sdd.date, '%Y-%m-%d' )
        <choose>
            <when test="weStoreCode.shopGuideState != null  and weStoreCode.shopGuideState !=''">
                and wc.state  LIKE CONCAT('%',#{weStoreCode.shopGuideState,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                <choose>
                    <when test="weStoreCode.storeCodeId != null">
                        <choose>
                            <when test="weStoreCode.area !=null and weStoreCode.area !=''">
                                AND wc.state in (SELECT wsc.shop_guide_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId} and wsc.area =  #{weStoreCode.area})
                            </when>
                            <otherwise>
                                AND wc.state in (SELECT wsc.shop_guide_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId})
                            </otherwise>
                        </choose>
                    </when>
                    <when test="weStoreCode.storeCodeId == null and  weStoreCode.area !=null and weStoreCode.area !=''">
                        AND wc.state in (SELECT wsc.shop_guide_state from we_store_code wsc  where wsc.area =  #{weStoreCode.area})
                    </when>
                </choose>
            </otherwise>
        </choose>

        ) AS customerTotalNumber
        FROM
        sys_dim_date sdd
        WHERE
        DATE_FORMAT( sdd.date, '%Y-%m-%d' ) BETWEEN #{weStoreCode.beginTime} and #{weStoreCode.endTime}
    </select>


    <select id="countStoreGroupTrend" resultType="org.scrm.domain.storecode.vo.trend.WeStoreGroupTrendVo">
        SELECT
        sdd.date as dataTime,

        -- 店铺引导扫码数
        (
        SELECT
        count(DISTINCT ws.unionid)
        FROM
        we_store_code_count ws
        WHERE
        DATE_FORMAT(ws.create_time, '%Y-%m-%d') = DATE_FORMAT(sdd.date, '%Y-%m-%d')
        AND ws.source = 2
        <choose>
            <when test="weStoreCode.storeCodeId != null">
                <choose>
                    <when test="weStoreCode.area !=null and weStoreCode.area !=''">
                        AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId} and wsc.area =  #{weStoreCode.area})
                    </when>
                    <otherwise>
                        AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId})
                    </otherwise>
                </choose>
            </when>
            <when test="weStoreCode.storeCodeId == null and  weStoreCode.area !=null and weStoreCode.area !=''">
                AND ws.store_code_id in (SELECT wsc.id from we_store_code wsc  where wsc.area =  #{weStoreCode.area})
            </when>
        </choose>
        ) AS totalStoreGroupScannNumber,

        -- 加入群组会员数
        (
        SELECT count(DISTINCT wgm.user_id) from we_group_member wgm
        WHERE wgm.quit_scene IS NULL
         AND  DATE_FORMAT(wgm.join_time, '%Y-%m-%d') = DATE_FORMAT(sdd.date, '%Y-%m-%d')
        <choose>
            <when test="weStoreCode.groupCodeState != null  and weStoreCode.groupCodeState !=''">
                and wgm.state  LIKE CONCAT('%',#{weStoreCode.groupCodeState,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                <choose>
                    <when test="weStoreCode.storeCodeId != null">
                        <choose>
                            <when test="weStoreCode.area !=null and weStoreCode.area !=''">
                                AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId} and wsc.area =  #{weStoreCode.area})
                            </when>
                            <otherwise>
                                AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId})
                            </otherwise>
                        </choose>
                    </when>
                    <when test="weStoreCode.storeCodeId == null and  weStoreCode.area !=null and weStoreCode.area !=''">
                        AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.area =  #{weStoreCode.area})
                    </when>
                </choose>
            </otherwise>
        </choose>

        ) AS totalJoinGroupMemberNumber,

        -- 退出群组会员数
        (
        SELECT count(DISTINCT wgm.user_id) from we_group_member wgm
        WHERE wgm.quit_scene IS NOT NULL
        AND  DATE_FORMAT(wgm.quit_time, '%Y-%m-%d') = DATE_FORMAT(sdd.date, '%Y-%m-%d')
        <choose>
            <when test="weStoreCode.groupCodeState != null  and weStoreCode.groupCodeState !=''">
                and wgm.state  LIKE CONCAT('%',#{weStoreCode.groupCodeState,jdbcType=VARCHAR},'%')
            </when>
            <otherwise>
                <choose>
                    <when test="weStoreCode.storeCodeId != null">
                        <choose>
                            <when test="weStoreCode.area !=null and weStoreCode.area !=''">
                                AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId} and wsc.area =  #{weStoreCode.area})
                            </when>
                            <otherwise>
                                AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.id=#{weStoreCode.storeCodeId})
                            </otherwise>
                        </choose>
                    </when>
                    <when test="weStoreCode.storeCodeId == null and  weStoreCode.area !=null and weStoreCode.area !=''">
                        AND wgm.state in (SELECT wsc.group_code_state from we_store_code wsc  where wsc.area =  #{weStoreCode.area})
                    </when>
                </choose>
            </otherwise>
        </choose>
        ) AS totalExitGroupMemberNumber

        FROM
        sys_dim_date sdd
        WHERE
        DATE_FORMAT(sdd.date, '%Y-%m-%d') BETWEEN #{weStoreCode.beginTime} and #{weStoreCode.endTime}
    </select>

    <select id="findWeStoreCodeTables" resultType="org.scrm.domain.storecode.vo.WeStoreCodeTableVo">
        SELECT
            wc.avatar,
            wc.customer_name,
            wc.customer_type,
            wc.external_userid AS externalUserid,
            wc.add_user_id,
            wc.add_time,
            wc.gender,
        (SELECT GROUP_CONCAT(su.user_name) FROM sys_user su WHERE su.we_user_id=wc.add_user_id) as addUserName,



        <choose>
            <when test="query.groupCodeState !=null  and query.groupCodeState !=''">
                (SELECT count(*) from we_group_member wgm WHERE wc.external_userid = wgm.user_id
                and wgm.state=#{query.groupCodeState}
                and wgm.quit_time  IS  NULL) as joinGroupNumber,
            </when>
            <otherwise>
                 0 as joinGroupNumber,
            </otherwise>
        </choose>

            <choose>
                <when test="query.groupCodeState !=null  and query.groupCodeState !=''">
                    IF((SELECT count(*) from we_group_member wgm WHERE wc.external_userid = wgm.user_id
                        and wgm.state=#{query.groupCodeState}
                    and wgm.quit_time  IS  NULL)>0,1,0) as isJoinGroup
                </when>
                <otherwise>
                     0 as isJoinGroup
                </otherwise>
            </choose>

        FROM
        we_customer wc
        <where>
            <if test="query.shopGuideState != null and query.shopGuideState !=''">
                and wc.state = #{query.shopGuideState}
            </if>
            <if test="query.customerName != null and query.customerName !='' ">
                and  wc.customer_name  LIKE CONCAT('%',#{query.customerName,jdbcType=VARCHAR},'%')
            </if>

            <if test="query.startAddTime != null and query.startAddTime !='' and query.endAddTime != null and query.endAddTime !='' ">
                AND  date_format(wc.add_time,'%Y-%m-%d') BETWEEN #{query.startAddTime} AND #{query.endAddTime}
            </if>
            <choose>
                <when test="query.isJoinGroup != null">
                    <if test="query.isJoinGroup ==1 ">
                        HAVING(joinGroupNumber)>0
                    </if>
                    <if test="query.isJoinGroup ==0 ">
                        HAVING(joinGroupNumber)&lt;=0
                    </if>
                </when>
            </choose>
        </where>
    </select>



</mapper>
