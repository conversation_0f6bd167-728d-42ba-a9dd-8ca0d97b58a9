{"groups": [{"name": "security.captcha", "type": "org.scrm.gateway.config.properties.CaptchaProperties", "sourceType": "org.scrm.gateway.config.properties.CaptchaProperties"}, {"name": "security.ignore", "type": "org.scrm.gateway.config.properties.IgnoreWhiteProperties", "sourceType": "org.scrm.gateway.config.properties.IgnoreWhiteProperties"}, {"name": "security.xss", "type": "org.scrm.gateway.config.properties.XssProperties", "sourceType": "org.scrm.gateway.config.properties.XssProperties"}], "properties": [{"name": "security.captcha.enabled", "type": "java.lang.Bo<PERSON>an", "description": "验证码开关", "sourceType": "org.scrm.gateway.config.properties.CaptchaProperties"}, {"name": "security.captcha.type", "type": "java.lang.String", "description": "验证码类型（math 数组计算 char 字符）", "sourceType": "org.scrm.gateway.config.properties.CaptchaProperties"}, {"name": "security.ignore.whites", "type": "java.util.List<java.lang.String>", "description": "放行白名单配置，网关不校验此处的白名单", "sourceType": "org.scrm.gateway.config.properties.IgnoreWhiteProperties"}, {"name": "security.xss.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Xss开关", "sourceType": "org.scrm.gateway.config.properties.XssProperties"}, {"name": "security.xss.exclude-urls", "type": "java.util.List<java.lang.String>", "description": "排除路径", "sourceType": "org.scrm.gateway.config.properties.XssProperties"}], "hints": []}