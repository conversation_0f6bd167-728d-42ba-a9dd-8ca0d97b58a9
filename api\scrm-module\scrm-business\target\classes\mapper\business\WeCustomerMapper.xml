<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.scrm.mapper.WeCustomerMapper">



    <select id="findWeCustomerListIds" resultType="java.lang.String">
        SELECT wcr.id FROM we_customer wcr
        LEFT JOIN we_flower_customer_tag_rel wcrs ON wcr.external_userid = wcrs.external_userid AND wcr.add_user_id = wcrs.user_id
       <where>
            <if test="weCustomerList.isJoinBlacklist != null">
                AND wcr.is_join_blacklist=#{weCustomerList.isJoinBlacklist}
            </if>
            <if test="weCustomerList.gender !=null">
                AND wcr.gender=#{weCustomerList.gender}
            </if>
            <if test="weCustomerList.genders !=null and weCustomerList.genders !=''">
                AND wcr.gender in
                <foreach collection="weCustomerList.genders.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>
           <if test="weCustomerList.noContainTrackStates !=null and weCustomerList.noContainTrackStates !=''">
               AND wcr.track_state not in
               <foreach collection="weCustomerList.noContainTrackStates.split(',')" item="item" index="index" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>

            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>

            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

               <if test="weCustomerList.customerTypes !=null and weCustomerList.customerTypes !=''">
                   AND wcr.customer_type in
                   <foreach collection="weCustomerList.customerTypes.split(',')" item="item" index="index" open="(" close=")" separator=",">
                       #{item}
                   </foreach>
               </if>

            <if test="weCustomerList.externalUserid !=null and weCustomerList.externalUserid !=''">
                AND wcr.external_userid=#{weCustomerList.externalUserid}
            </if>

           <if test="weCustomerList.externalUserids !=null and weCustomerList.externalUserids !=''">
               AND wcr.external_userid in
               <foreach collection="weCustomerList.externalUserids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>

            <if test="weCustomerList.firstUserId !=null and weCustomerList.firstUserId !=''">
                AND wcr.add_user_id=#{weCustomerList.firstUserId}
            </if>

           <if test="weCustomerList.userIds !=null and weCustomerList.userIds !=''">
               AND wcr.add_user_id  in
               <foreach collection="weCustomerList.userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                   #{item}
               </foreach>
           </if>

           <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
                <if test="weCustomerList.delFlag==1">
                    AND wcr.loss_time IS NOT NULL
                </if>
            </if>

            <if test="weCustomerList.phone != null and weCustomerList.phone !=''">
               AND wcr.phone = #{weCustomerList.phone,jdbcType=VARCHAR}
            </if>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''))
                LIKE CONCAT('%',TRIM(#{weCustomerList.name, jdbcType=VARCHAR}),'%')
            </if>
            <if test="weCustomerList.userIds !=null and weCustomerList.userIds !=''">
                AND wcr.add_user_id in
                <foreach collection="weCustomerList.userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

           <if test="weCustomerList.remarkCorpName != null and weCustomerList.remarkCorpName !=''">
               AND wcr.remark_corp_name   LIKE CONCAT('%',#{weCustomerList.remarkCorpName,jdbcType=VARCHAR},'%')
           </if>

           <if test="weCustomerList.remarkCustomerName != null and weCustomerList.remarkCustomerName !=''">
              AND  wcr.remark_customer_name  LIKE CONCAT('%',#{weCustomerList.remarkCustomerName,jdbcType=VARCHAR},'%')
           </if>

            <if test="weCustomerList.excludeTagIds !=null and weCustomerList.excludeTagIds !=''">
                <foreach item="excludeTagId" index="index" collection="weCustomerList.excludeTagIds.split(',')">
                    AND !FIND_IN_SET(#{excludeTagId},wcr.tag_ids)
                </foreach>
            </if>
            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>
           <if test="weCustomerList.lossBeginTime !=null and weCustomerList.lossBeginTime != '' and weCustomerList.lossEndTime !='' and weCustomerList.lossEndTime != null">
               AND  date_format(wcr.loss_time,'%Y-%m-%d') BETWEEN #{weCustomerList.lossBeginTime} AND #{weCustomerList.lossEndTime}
           </if>
           <if test="weCustomerList.noTagCheck">
               AND wcrs.tag_id IS NULL
           </if>


           <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
               <choose>
                   <when test="weCustomerList.isContain == 2">
                       and wcrs.tag_id in
                       <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                           #{tagId}
                       </foreach>
                   </when>
                   <when test="weCustomerList.isContain == 1">
                       and wcrs.tag_id in
                       <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                           #{tagId}
                       </foreach>
                   </when>
               </choose>
           </if>






                          <choose>
                              <when test="weCustomerList.noRepeat">
                                  GROUP BY wcr.id, wcr.external_userid
                              </when>
                              <otherwise>
                                  GROUP BY wcr.id
                              </otherwise>
                          </choose>


                <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
                        <choose>
                            <when test="weCustomerList.isContain == 2">
                                HAVING
                                COUNT(DISTINCT wcrs.tag_id) &gt;= 1
                            </when>
                            <when test="weCustomerList.isContain == 1">
                                HAVING
                                COUNT(DISTINCT wcrs.tag_id) = #{weCustomerList.tagNumber}
                            </when>
                            <when  test="weCustomerList.isContain == 3">
                                HAVING
                                SUM(CASE WHEN wcrs.tag_id IN
                                <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                                    #{tagId}
                                </foreach>
                                THEN 1 ELSE 0 END) = 0
                            </when>
                        </choose>
                    </if>

             ORDER BY wcr.add_time desc
            <if test="pageDomain !=null">
                <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                    limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
                </if>
            </if>
        </where>

    </select>



    <select id="findWeUserIds" resultType="java.lang.String">
         select we_user_id from  sys_user
    </select>




    <select id="findWeCustomerListIdsByApp" resultType="java.lang.String">
        SELECT wcr.id FROM we_customer wcr
        LEFT JOIN sys_user su on su.we_user_id=wcr.add_user_id
        <where>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''),IFNULL(su.user_name,''))
                LIKE CONCAT('%',#{weCustomerList.name,jdbcType=VARCHAR},'%')
            </if>
            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>

            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>


            <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
                <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')">
                    AND FIND_IN_SET(#{tagId},wcr.tag_ids)
                </foreach>
            </if>

            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>

            <if test="weCustomerList.firstUserId !=null">
                AND wcr.add_user_id=#{weCustomerList.firstUserId}
            </if>

            <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
            </if>

        </where>
        ORDER BY wcr.add_time desc
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>
    </select>



    <select id="countWeCustomerListByApp" resultType="long">
        SELECT count(wcr.id) FROM we_customer wcr
        LEFT JOIN sys_user su on su.we_user_id=wcr.add_user_id
        <where>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''),IFNULL(su.user_name,''))
                LIKE CONCAT('%',#{weCustomerList.name,jdbcType=VARCHAR},'%')
            </if>

            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>

            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>


            <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
                <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')">
                    AND FIND_IN_SET(#{tagId},wcr.tag_ids)
                </foreach>
            </if>

            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>

            <if test="weCustomerList.firstUserId !=null">
                AND wcr.add_user_id=#{weCustomerList.firstUserId}
            </if>

            <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
            </if>

        </where>
    </select>


    <select id="findWeCustomerList" resultType="org.scrm.domain.customer.vo.WeCustomersVo">
        SELECT
        wc.id,
        wc.unionid,
        wc.avatar,
        wc.customer_name,
        wc.customer_type as customerType,
        wc.external_userid,
        wc.add_user_id as firstUserId,
        wc.track_state,
        wc.add_method,
        wc.add_time as firstAddTime,
        wc.birthday,
        wc.gender,
        wc.phone,
        wc.email,
        wc.address,
        wc.qq,
        wc.is_join_blacklist as isJoinBlacklist,
        wc.position,
        wc.corp_name as corpName,
        wc.remark_corp_name as remarkCorpName,
        wc.other_descr as otherDescr,
        wc.remark_customer_name as remarkCustomerName,
        wc.remark_customer_descr as remarkCustomerDescr,
        wc.remark_phone as remarkPhone,
        timestampdiff(day,wc.add_time, wc.loss_time) as retentionDays,
        (SELECT GROUP_CONCAT(DISTINCT wu.user_name) FROM sys_user wu WHERE  wu.we_user_id=wc.add_user_id) as userName,
        (SELECT GROUP_CONCAT(wt.tag_id) FROM we_flower_customer_tag_rel wfctr
        LEFT JOIN we_tag wt ON wt.tag_id= wfctr.tag_id
        WHERE wfctr.external_userid=wc.external_userid AND wc.add_user_id=wfctr.user_id AND wt.tag_type=3  and wfctr.del_flag=0)  as personTagIds,
        (SELECT GROUP_CONCAT(wt.name) FROM we_flower_customer_tag_rel wfctr
        LEFT JOIN we_tag wt ON wt.tag_id= wfctr.tag_id
        WHERE wfctr.external_userid=wc.external_userid AND wc.add_user_id=wfctr.user_id AND wt.tag_type=3  and wfctr.del_flag=0)  as personTagNames,
        (SELECT GROUP_CONCAT(wt.name) FROM we_flower_customer_tag_rel wfctr
        LEFT JOIN we_tag wt ON wt.tag_id= wfctr.tag_id
        WHERE wfctr.external_userid=wc.external_userid AND wc.add_user_id=wfctr.user_id AND wt.tag_type=1  and wfctr.del_flag=0)  as tagNames,
        (SELECT GROUP_CONCAT(wt.tag_id) FROM we_flower_customer_tag_rel wfctr
        LEFT JOIN we_tag wt ON wt.tag_id= wfctr.tag_id
        WHERE wfctr.external_userid=wc.external_userid AND wc.add_user_id=wfctr.user_id AND wt.tag_type=1 and wfctr.del_flag=0)  as tagIds,
        wc.update_time as updateTime,
        wc.area,
        wc.loss_time
        FROM
        we_customer wc
        WHERE
            wc.id in
            <foreach collection="ids" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        ORDER BY wc.add_time desc
    </select>


    <select id="countWeCustomerList" resultType="long">
        SELECT
            IFNULL(
                <choose>
                    <when test="weCustomerList.noRepeat">
                        count(DISTINCT wcr.external_userid)
                    </when>
                    <otherwise>
                        count(DISTINCT wcr.id)
                    </otherwise>
                </choose>,0
            )
        FROM we_customer wcr
        LEFT JOIN we_flower_customer_tag_rel wcrs ON wcr.external_userid = wcrs.external_userid AND wcr.add_user_id = wcrs.user_id
        <where>
            <if test="weCustomerList.isJoinBlacklist != null">
                AND wcr.is_join_blacklist=#{weCustomerList.isJoinBlacklist}
            </if>
            <if test="weCustomerList.gender !=null">
                AND wcr.gender=#{weCustomerList.gender}
            </if>
            <if test="weCustomerList.genders !=null and weCustomerList.genders !=''">
                AND wcr.gender in
                <foreach collection="weCustomerList.genders.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>
            <if test="weCustomerList.noContainTrackStates !=null and weCustomerList.noContainTrackStates !=''">
                AND wcr.track_state not in
                <foreach collection="weCustomerList.noContainTrackStates.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>

            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

            <if test="weCustomerList.customerTypes !=null and weCustomerList.customerTypes !=''">
                AND wcr.customer_type in
                <foreach collection="weCustomerList.customerTypes.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.externalUserid !=null and weCustomerList.externalUserid !=''">
                AND wcr.external_userid=#{weCustomerList.externalUserid}
            </if>

            <if test="weCustomerList.remarkCorpName != null and weCustomerList.remarkCorpName !=''">
                AND wcr.remark_corp_name   LIKE CONCAT('%',#{weCustomerList.remarkCorpName,jdbcType=VARCHAR},'%')
            </if>

            <if test="weCustomerList.remarkCustomerName != null and weCustomerList.remarkCustomerName !=''">
                AND  wcr.remark_customer_name  LIKE CONCAT('%',#{weCustomerList.remarkCustomerName,jdbcType=VARCHAR},'%')
            </if>

            <if test="weCustomerList.externalUserids !=null and weCustomerList.externalUserids !=''">
                AND wcr.external_userid in
                <foreach collection="weCustomerList.externalUserids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.firstUserId !=null and weCustomerList.firstUserId !=''">
                AND wcr.add_user_id=#{weCustomerList.firstUserId}
            </if>

            <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
                <if test="weCustomerList.delFlag==1">
                    AND wcr.loss_time IS NOT NULL
                </if>
            </if>
            <if test="weCustomerList.phone != null and weCustomerList.phone !=''">
                AND wcr.phone = #{weCustomerList.phone,jdbcType=VARCHAR}
            </if>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''))
                LIKE CONCAT('%',#{weCustomerList.name,jdbcType=VARCHAR},'%')
            </if>
            <if test="weCustomerList.userIds !=null and weCustomerList.userIds !=''">
                AND wcr.add_user_id in
                <foreach collection="weCustomerList.userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>




            <if test="weCustomerList.excludeTagIds !=null and weCustomerList.excludeTagIds !=''">
                <foreach item="excludeTagId" index="index" collection="weCustomerList.excludeTagIds.split(',')">
                    AND !FIND_IN_SET(#{excludeTagId},wcr.tag_ids)
                </foreach>
            </if>
            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>

            <if test="weCustomerList.lossBeginTime !=null and weCustomerList.lossBeginTime != '' and weCustomerList.lossEndTime !='' and weCustomerList.lossEndTime != null">
                AND  date_format(wcr.loss_time,'%Y-%m-%d') BETWEEN #{weCustomerList.lossBeginTime} AND #{weCustomerList.lossEndTime}
            </if>
        </where>

        <if test="weCustomerList.noTagCheck">
            AND wcrs.tag_id IS NULL
        </if>

        <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
            <choose>
                <when test="weCustomerList.isContain == 2">
                    and wcrs.tag_id in
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                </when>
                <when test="weCustomerList.isContain == 3">
                    HAVING
                    SUM(CASE WHEN wcrs.tag_id IN
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                    THEN 1 ELSE 0 END) = 0
                </when>
                <otherwise>
                    and wcrs.tag_id in
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                </otherwise>
            </choose>
        </if>

        <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
            <choose>
                <when test="weCustomerList.isContain == 2">
                    HAVING
                    COUNT(DISTINCT wcrs.tag_id) &gt;= 1
                </when>
                <when test="weCustomerList.isContain == 1">
                    HAVING
                    COUNT(DISTINCT wcrs.tag_id) = #{weCustomerList.tagNumber}
                </when>
            </choose>
        </if>

    </select>

    <select id="noRepeatCountCustomer" resultType="long">
        SELECT
          IFNULL(COUNT(DISTINCT  wcr.external_userid),0)
        FROM
        we_customer wcr
        LEFT JOIN we_flower_customer_tag_rel wcrs ON wcr.external_userid = wcrs.external_userid AND wcr.add_user_id = wcrs.user_id
        <where>
            <if test="weCustomerList.isJoinBlacklist != null">
                AND wcr.is_join_blacklist=#{weCustomerList.isJoinBlacklist}
            </if>
            <if test="weCustomerList.gender !=null">
                AND wcr.gender=#{weCustomerList.gender}
            </if>
            <if test="weCustomerList.genders !=null and weCustomerList.genders !=''">
                AND wcr.gender in
                <foreach collection="weCustomerList.genders.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>
            <if test="weCustomerList.noContainTrackStates !=null and weCustomerList.noContainTrackStates !=''">
                AND wcr.track_state not in
                <foreach collection="weCustomerList.noContainTrackStates.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>

            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

            <if test="weCustomerList.customerTypes !=null and weCustomerList.customerTypes !=''">
                AND wcr.customer_type in
                <foreach collection="weCustomerList.customerTypes.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.externalUserid !=null">
                AND wcr.external_userid=#{weCustomerList.externalUserid}
            </if>

            <if test="weCustomerList.externalUserids !=null and weCustomerList.externalUserids !=''">
                AND wcr.external_userid in
                <foreach collection="weCustomerList.externalUserids.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.firstUserId !=null">
                AND wcr.add_user_id=#{weCustomerList.firstUserId}
            </if>

            <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
            </if>
            <if test="weCustomerList.phone != null and weCustomerList.phone !=''">
                AND wcr.phone = #{weCustomerList.phone,jdbcType=VARCHAR}
            </if>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''))
                LIKE CONCAT('%',#{weCustomerList.name,jdbcType=VARCHAR},'%')
            </if>
            <if test="weCustomerList.userIds !=null and weCustomerList.userIds !=''">
                AND wcr.add_user_id in
                <foreach collection="weCustomerList.userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>

            <if test="weCustomerList.remarkCorpName != null and weCustomerList.remarkCorpName !=''">
                AND wcr.remark_corp_name   LIKE CONCAT('%',#{weCustomerList.remarkCorpName,jdbcType=VARCHAR},'%')
            </if>

            <if test="weCustomerList.remarkCustomerName != null and weCustomerList.remarkCustomerName !=''">
                AND  wcr.remark_customer_name  LIKE CONCAT('%',#{weCustomerList.remarkCustomerName,jdbcType=VARCHAR},'%')
            </if>



            <if test="weCustomerList.excludeTagIds !=null and weCustomerList.excludeTagIds !=''">
                <foreach item="excludeTagId" index="index" collection="weCustomerList.excludeTagIds.split(',')">
                    AND !FIND_IN_SET(#{excludeTagId},wcr.tag_ids)
                </foreach>
            </if>
            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>
        </where>


            <if test="weCustomerList.noTagCheck">
                AND wcrs.tag_id IS NULL
            </if>

        <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
            <choose>
                <when test="weCustomerList.isContain == 2">
                    and wcrs.tag_id in
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                </when>
                <when test="weCustomerList.isContain == 3">
                    HAVING
                    SUM(CASE WHEN wcrs.tag_id IN
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                    THEN 1 ELSE 0 END) = 0
                </when>

                <otherwise>
                    and wcrs.tag_id in
                    <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')" open="(" separator="," close=")">
                        #{tagId}
                    </foreach>
                </otherwise>
            </choose>
        </if>


        <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
                <choose>
                    <when test="weCustomerList.isContain == 2">
                        HAVING
                        COUNT(DISTINCT wcrs.tag_id) &gt;= 1
                    </when>
                    <when test="weCustomerList.isContain == 1">
                        HAVING
                        COUNT(DISTINCT wcrs.tag_id) = #{weCustomerList.tagNumber}
                    </when>
                </choose>
            </if>

    </select>


    <select id="findWecustomerGroups" resultType="org.scrm.domain.customer.vo.WeCustomerDetailInfoVo$Groups">
        SELECT
            wg.chat_id,
            wg.group_name,
            wgm.join_time,
            wgm.join_scene,
            (SELECT wu.user_name FROM sys_user wu WHERE wu.we_user_id=wg.owner) as leaderName
        FROM
            we_group_member wgm
                LEFT JOIN we_group wg ON wg.chat_id = wgm.chat_id
        WHERE wgm.del_flag=0 and wg.del_flag=0 AND wgm.user_id=#{userId}
        GROUP BY  wg.chat_id
    </select>

    <select id="findUserNameByUserId" resultType="String">
      SELECT DISTINCT user_name FROM sys_user WHERE we_user_id=#{weUserId} and del_flag = 0
    </select>


    <select id="findCustomerByOperUseridAndCustomerId" resultType="org.scrm.domain.customer.vo.WeCustomerPortraitVo">
        SELECT
            wc.id as customerId,
            wc.customer_name as name,
            wc.other_descr as remark,
            wc.other_descr as otherDescr,
            wc.phone as remarkMobiles,
            wc.birthday,
            wc.email,
            wc.address,
            wc.qq,
            wc.position,
            wc.corp_area as corpArea,
            wc.other_descr as description,
            wc.add_user_id as user_id,
            wc.avatar,
            wc.external_userid,
            wc.gender,
            wc.track_state,
            wc.customer_type,
            GROUP_CONCAT(if(wt.tag_type=1,wt.`tag_id`,null )) as tagIds,
            GROUP_CONCAT(if(wt.tag_type=1,wt.`name`,null )) as tagNames,
            GROUP_CONCAT(if(wt.tag_type=3,wt.`tag_id`,null )) as personTagIds,
            GROUP_CONCAT(if(wt.tag_type=3,wt.`name`,null )) as personTagNames,
            wc.track_content as trackContent,
            wc.province_id,
            wc.city_id,
            wc.area_id,
            wc.area,
            wc.customer_full_name as customerFullName,
            wc.add_method,
            wc.add_time,
            wc.remark_corp_name as remarkCorpName,
            wc.remark_customer_name as remarkCustomerName,
            wc.remark_customer_descr as remarkCustomerDescr,
            wc.remark_phone as remarkPhone
        FROM
            we_customer wc
                LEFT JOIN we_flower_customer_tag_rel wfcrf ON wfcrf.user_id = wc.add_user_id  AND wfcrf.external_userid=wc.external_userid AND wfcrf.del_flag=0
                LEFT JOIN we_tag wt ON wt.tag_id = wfcrf.tag_id
        WHERE wc.external_userid=#{externalUserid} and  wc.add_user_id=#{userid}
    </select>


    <select id="countSocialConn" resultType="org.scrm.domain.customer.vo.WeCustomerSocialConnVo">
        SELECT
            (
                SELECT
                    count(DISTINCT wc.add_user_id)
                FROM
                    sys_user wu WHERE wc.add_user_id = wu.we_user_id and wu.del_flag=0
            ) AS addEmployeNum,
            (
                SELECT
                    COUNT(*)
                FROM
                    we_group_member wgm where  wgm.user_id=wc.external_userid and wgm.quit_scene  is  NULL
            ) AS addGroupNum,
            (
                SELECT
                    COUNT(*)
                FROM we_group_member where chat_id in (
                    SELECT
                        chat_id
                    FROM
                        `we_group_member` where user_id=wc.external_userid and quit_scene  is  NULL
                ) and user_id=#{userid} and quit_scene  is  NULL
            ) AS commonGroupNum
        FROM we_customer wc WHERE wc.external_userid=#{externalUserid}  and wc.add_user_id=#{userid}
    </select>

    <select id="findWeUserByCutomerId" resultType="org.scrm.domain.customer.vo.WeCustomerAddUserVo">
        SELECT
            wu.user_name,
            wc.add_user_id as userId,
            wu.avatar as headImageUrl,
            wc.add_time as createTime,
            wc.add_method as addMethod,
            wc.add_time as firstAddTime,
            wc.track_state as trackState,
            wc.track_time as trackTime
        FROM
            we_customer wc
                INNER JOIN sys_user wu ON wc.add_user_id = wu.we_user_id
        WHERE wc.external_userid=#{externalUserid} and wu.del_flag=0
    </select>


    <insert id="batchAddOrUpdate">
        INSERT INTO we_customer(
        id,
        external_userid,
        customer_name,
        avatar,
        customer_type,
        gender,
        unionid,
        birthday,
        corp_name,
        `position`,
        create_time,
        create_by,
        add_user_id,
        add_time,
        qq,
        email,
        address,
        phone,
        add_method,
        del_flag,
        `state`,
        update_by,
        update_time,
        other_descr,
        remark_name,
        create_by_id,
        update_by_id,
        tag_ids
        ) values
        <foreach collection="weCustomers" item="item" index="index" separator=",">
            (#{item.id},#{item.externalUserid},#{item.customerName},#{item.avatar},#{item.customerType},#{item.gender}, #{item.unionid},#{item.birthday},
            #{item.corpName},#{item.position}, #{item.createTime},
            #{item.createBy},#{item.addUserId},#{item.addTime},#{item.qq},#{item.email},#{item.address},#{item.phone},#{item.addMethod},#{item.delFlag},
            #{item.state},#{item.updateBy},#{item.updateTime},#{item.otherDescr},#{item.remarkName},#{item.createById},#{item.updateById},
             #{item.tagIds}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        customer_name=IFNULL(VALUES(customer_name),we_customer.customer_name),
        avatar=IFNULL(VALUES(avatar), we_customer.avatar),
        customer_type=IFNULL(VALUES(customer_type),we_customer.customer_type),
        gender=IFNULL(VALUES(gender), we_customer.gender),
        unionid=IFNULL(VALUES(unionid), we_customer.unionid),
        birthday=IFNULL(VALUES(birthday), we_customer.birthday),
        corp_name=IFNULL(VALUES(corp_name), we_customer.corp_name),
        `position`=IFNULL(VALUES(`position`), we_customer.position),
        add_user_id=IFNULL(VALUES(add_user_id), we_customer.add_user_id),
        add_time=IFNULL(VALUES(add_time),we_customer.add_time),
        qq=IFNULL(VALUES(qq), we_customer.qq),
        email=IFNULL(VALUES(email), we_customer.email),
        address=IFNULL(VALUES(address), we_customer.address),
        phone=IFNULL(VALUES(phone), we_customer.phone),
        add_method=IFNULL(VALUES(add_method), we_customer.add_method),
        del_flag=IFNULL(VALUES(del_flag),we_customer.del_flag),
        `state`=IFNULL(VALUES(`state`),we_customer.state),
        create_by=IFNULL(VALUES(create_by),we_customer.create_by),
        create_by_id=IFNULL(VALUES(create_by_id),we_customer.create_by_id),
        update_by=IFNULL(VALUES(update_by),we_customer.update_by),
        update_by_id=IFNULL(VALUES(update_by_id),we_customer.update_by_id),
        update_time=IFNULL(VALUES(update_time),we_customer.update_time),
        other_descr=IFNULL(VALUES(other_descr),we_customer.other_descr),
        tag_ids=IFNULL(VALUES(tag_ids),we_customer.tag_ids),
        remark_name=IFNULL(VALUES(remark_name),we_customer.remark_name);
    </insert>


    <select id="findCurrentTenantSysUser" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT * FROM sys_user where  del_flag = 0
    </select>


    <select id="findCurrentSysUserInfo" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT * FROM sys_user where user_id=#{userId} and del_flag=0
    </select>

    <select id="findSysUserInfoByWeUserId" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT * FROM sys_user where we_user_id= #{weUserId} and del_flag=0
    </select>

    <select id="getCustomerListByCondition" resultType="org.scrm.domain.WeCustomer">
        select distinct wc.external_userid,
                    wc.add_user_id
        from we_customer wc
                 left join sys_user su on wc.add_user_id = su.we_user_id and su.del_flag = 0
                 left join sys_user_dept sud
                           on sud.we_user_id = su.we_user_id and sud.del_flag = 0
                 left join we_flower_customer_tag_rel wfctr
                           on wfctr.external_userid = wc.external_userid and wfctr.del_flag = 0
        <where>
            <if test="userIds != null and userIds != ''">
                and su.we_user_id in
                <foreach collection="userIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="deptIds != null and deptIds != ''">
                and sud.dept_id in
                <foreach collection="deptIds.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="tagIds != null and tagIds != ''">
                <foreach item="tagId" index="index" collection="tagIds.split(',')">
                    AND FIND_IN_SET(#{tagId},wfctr.tag_id)
                </foreach>
            </if>

            <if test="beginTime !=null and beginTime != ''">
                and  date_format(wc.add_time,'%Y-%m-%d') &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and  date_format(wc.add_time,'%Y-%m-%d') &lt;= #{endTime}
            </if>
            <if test="stateList != null and stateList.size() > 0">
                and wc.state in
                <foreach item="state" collection="states" index="index" open="(" separator="," close=")">
                    #{state}
                </foreach>
            </if>
        </where>
        and wc.del_flag = 0
    </select>


    <select id="findAllSysUser" resultType="org.scrm.base.core.domain.entity.SysUser">
        SELECT * FROM sys_user where del_flag=0
    </select>


    <delete id="deleteWeCustomer">
        DELETE from we_customer where external_userid=#{externalUserId} and add_user_id=#{userId}
    </delete>


    <select id="findWeCustomerListEuIds" resultType="java.lang.String">
        SELECT wcr.external_userid FROM we_customer wcr
        LEFT JOIN sys_user su on su.we_user_id=wcr.add_user_id
        <where>
            <if test="weCustomerList.name != null and weCustomerList.name !=''">
                AND  CONCAT( IFNULL(wcr.customer_name,''),IFNULL(su.user_name,''))
                LIKE CONCAT('%',#{weCustomerList.name,jdbcType=VARCHAR},'%')
            </if>
            <if test="weCustomerList.customerType !=null">
                AND wcr.customer_type=#{weCustomerList.customerType}
            </if>

            <if test="weCustomerList.trackState !=null">
                AND wcr.track_state=#{weCustomerList.trackState}
            </if>
            <if test="weCustomerList.isFilterLossCustomer">
                AND wcr.track_state != 5
            </if>
            <if test="weCustomerList.addMethod !=null">
                AND wcr.add_method=#{weCustomerList.addMethod}
            </if>
            <if test="weCustomerList.tagIds !=null and weCustomerList.tagIds !=''">
                <foreach item="tagId" index="index" collection="weCustomerList.tagIds.split(',')">
                    AND FIND_IN_SET(#{tagId},wcr.tag_ids)
                </foreach>
            </if>

            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                AND  date_format(wcr.add_time,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>

            <if test="weCustomerList.firstUserId !=null">
                AND wcr.add_user_id in
                <foreach collection="weCustomerList.firstUserId.split(',')" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>

            </if>

            <if test="weCustomerList.delFlag != null">
                AND wcr.del_flag = #{weCustomerList.delFlag}
            </if>
        </where>
        ORDER BY wcr.add_time desc
    </select>


    <select id="countCustomerChannel" resultType="org.scrm.domain.customer.vo.WeCustomerChannelCountVo">
        select
        sdd.date,
        count(wc.id) as customer_number,
        count(case when wc.del_flag = 0 then 1 end) as  efficient_number
        from
        (
        SELECT
            `date`
        FROM
        sys_dim_date
        <where>
            <if test="startTime != null "><!-- 开始时间检索 -->
                and `date` &gt;= date_format(#{startTime},'%y-%m-%d')
            </if>
            <if test="endTime != null "><!-- 结束时间检索 -->
                and `date` &lt;= date_format(#{endTime},'%y-%m-%d')
            </if>
        </where>) as sdd
        left join we_customer wc on
        date_format(wc.add_time, '%Y-%m-%d')= date_format(sdd.date, '%Y-%m-%d')
        <if test="state != null and state !=''">
            AND wc.state=#{state}
        </if>
        order by sdd.date asc
    </select>


    <select id="totalScanCodeNumber" resultType="Integer">
        SELECT count(*) from we_customer
        <where>
            <if test="state != null and state != ''">
                state=#{state}
            </if>
        </where>
    </select>

    <select id="getCustomerNumByState" resultType="org.scrm.domain.customer.vo.WeCustomerChannelCountVo">
        select
        date_format(add_time,'%Y-%m-%d') as `date`,
        count(1) as customer_number,
        count(case when del_flag = 0 then 1 end) as  efficient_number
        from we_customer
        where id in (select id
        from we_customer where state=#{state}
        <if test="startTime != null "><!-- 开始时间检索 -->
            and date_format(add_time,'%y-%m-%d') &gt;= date_format(#{startTime},'%y-%m-%d')
        </if>
        <if test="endTime != null "><!-- 结束时间检索 -->
            and date_format(add_time,'%y-%m-%d') &lt;= date_format(#{endTime},'%y-%m-%d')
        </if>
        )
        group by add_time
    </select>
    <select id="getCustomerSimpleInfo" resultType="org.scrm.domain.customer.vo.WeCustomerSimpleInfoVo">
        select  external_userid,customer_name,avatar from we_customer
        <where>
            and external_userid in
            <foreach collection="externalUserIds" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        group by external_userid,customer_name,avatar;
    </select>


    <delete id="physicalDelete" >
        delete from we_customer where external_userid=#{externalUserid} and add_user_id=#{weUserId}
    </delete>

    <update id="setWeCustomerExtend">
        UPDATE we_customer
        SET extend = 0
        WHERE
        add_user_id IN
        <foreach collection="addUserIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="findLossWeCustomerCount" resultType="org.scrm.domain.customer.vo.WeCustomerDelCountVo">
        SELECT
            date as xTime,
            (SELECT count(wc.external_userid) from we_customer wc where DATE_FORMAT(wc.loss_time,'%Y-%m-%d') &lt;=date
            AND (wc.track_state=5 or wc.del_flag=1)
            ) as lossCnt,
            (SELECT count(wc.external_userid) from we_customer wc where DATE_FORMAT(wc.loss_time,'%Y-%m-%d') &lt;= date
            AND wc.track_state=5
            ) as delCnt,
            (SELECT count(wc.external_userid) from we_customer wc where DATE_FORMAT(wc.loss_time,'%Y-%m-%d') &lt;= date
            AND wc.del_flag=1
            ) as userDelCnt,
            (SELECT count(wc.external_userid) from we_customer wc where DATE_FORMAT(wc.loss_time,'%Y-%m-%d') =date
            AND wc.track_state=5
            ) as tdDelCnt,
            (SELECT count(wc.external_userid) from we_customer wc where DATE_FORMAT(wc.loss_time,'%Y-%m-%d') =date
            AND wc.del_flag=1
            ) as tdUserDelCnt
        FROM
        sys_dim_date
        <where>
            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
               date_format(date,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>
        </where>
        ORDER BY date ASC
        <if test="pageDomain !=null">
            <if test="pageDomain.pageNum !=null and pageDomain.pageSize !=null">
                limit ${pageDomain.pageSize * (pageDomain.pageNum-1)}, #{pageDomain.pageSize}
            </if>
        </if>
    </select>

    <select id="findLossWeCustomerCountNumber" resultType="long">
        SELECT
           count(*)
        FROM
        sys_dim_date
        <where>
            <if test="weCustomerList.beginTime !=null and weCustomerList.beginTime != '' and weCustomerList.endTime !='' and weCustomerList.endTime != null">
                date_format(date,'%Y-%m-%d') BETWEEN #{weCustomerList.beginTime} AND #{weCustomerList.endTime}
            </if>
        </where>
    </select>

    <update id="delExternalContact">
        UPDATE we_customer
        SET loss_time = now(),
            del_flag = 1
        WHERE
            add_user_id = #{addUserId}
          AND external_userid = #{externalUserID}
    </update>

    <select id="findWeCorpCustomers" resultType="org.scrm.domain.WeCorpCustomerVo">
        SELECT
        remark_corp_name,
        corp_area,
        count(DISTINCT external_userid) as corpCustomerNumber,
        count(DISTINCT add_user_id) as fllowUserNumber
        FROM
        we_customer
        WHERE
        remark_corp_name IS NOT NULL AND remark_corp_name != '' and track_state != 5 and del_flag = 0
        <if test="remarkCorpName !=null and remarkCorpName !='' ">
            and remark_corp_name LIKE CONCAT('%',#{remarkCorpName,jdbcType=VARCHAR},'%')
        </if>
        <if test="corpArea !=null  and corpArea !=''">
            and corp_area LIKE CONCAT('%',#{corpArea,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY
        remark_corp_name,corp_area
    </select>

    <select id="countWeCorpCustomers" resultType="long">
        SELECT COUNT(*)
        FROM (
                 SELECT COUNT(id) AS id_count
                 FROM we_customer
                WHERE
                remark_corp_name IS NOT NULL AND remark_corp_name != '' and track_state != 5 and del_flag = 0
                <if test="remarkCorpName !=null and remarkCorpName !='' ">
                    and remark_corp_name LIKE CONCAT('%',#{remarkCorpName,jdbcType=VARCHAR},'%')
                </if>
                <if test="corpArea !=null  and corpArea !=''">
                    and corp_area LIKE CONCAT('%',#{corpArea,jdbcType=VARCHAR},'%')
                </if>
                GROUP BY
                remark_corp_name,corp_area
             ) AS subquery
    </select>

    <select id="countWeCorpCustomerNumbers" resultType="long">
        SELECT
            count(DISTINCT external_userid)
        FROM
            we_customer
        WHERE
            remark_corp_name IS NOT NULL AND remark_corp_name != '' and track_state != 5 and del_flag = 0
    </select>


</mapper>