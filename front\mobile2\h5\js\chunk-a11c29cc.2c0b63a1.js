(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a11c29cc"],{"63a6":function(e,t,s){"use strict";s("f182")},ad32:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e._self._c;return t("div",[t("van-search",{attrs:{placeholder:"搜索关键词",shape:"round","show-action":""},on:{search:e.search,clear:e.clear},scopedSlots:e._u([{key:"action",fn:function(){return[t("div",{on:{click:function(t){return e.search()}}},[e._v("搜索")])]},proxy:!0}]),model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}}),t("div",{staticClass:"desc"},[e._v(" "+e._s(e.data.descrition)+" ")]),t("PullRefreshScrollLoadList",{ref:"prsl",staticClass:"list",attrs:{params:{keywordGroupId:e.$route.query.id,keyword:e.keyword},request:e.getDetailList},scopedSlots:e._u([{key:"default",fn:function(s){return e._l(s,(function(s,a){return t("li",{key:a,staticClass:"fxbw li"},[t("div",{staticClass:"blod",domProps:{innerHTML:e._s(e.keyword?s.keyword.replace(e.keyword,`<span class='heighlight'>${e.keyword}</span>`):s.keyword)}}),t("div",{staticClass:"heighlight",on:{click:function(t){e.show=!0,e.focus=s}}},[e._v(" 加入群聊 ")])])}))}}])}),t("van-dialog",{attrs:{title:e.focus.codeName},model:{value:e.show,callback:function(t){e.show=t},expression:"show"}},[t("div",{staticClass:"ac mb10"},[t("img",{attrs:{src:e.focus.groupCodeUrl}}),t("div",{},[e._v("长按二维码添加企业客群")])])])],1)},o=[],r=s("b775");const{get:i}=r["c"],c="/keywordGroup",n=(e,t=sessionStorage.unionId)=>i(c+"/getBaseInfo",{keywordGroupId:e,unionId:t}),d=e=>i(c+"/findGroupSubs",e);var l={data(){return{getDetailList:d,data:{},focus:{},keyword:"",show:!1}},created(){this.getDetail()},methods:{getDetail(){this.$toast.loading(),n(this.$route.query.id).then(({data:e})=>{this.data=e||{}}).finally(()=>{this.$toast.clear()})},search(){this.$refs.prsl.getList(1)},clear(){this.keyword="",this.search()}}},u=l,h=(s("63a6"),s("2877")),f=Object(h["a"])(u,a,o,!1,null,"a67e572a",null);t["default"]=f.exports},f182:function(e,t,s){}}]);