(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0db2ff"],{"6f92":function(n,t,r){"use strict";r.r(t),r.d(t,"recordPhoneCall",(function(){return s})),r.d(t,"getCallStatistics",(function(){return a})),r.d(t,"getCallRecords",(function(){return u}));var e=r("b775");const o=window.sysConfig.services.wecom,c=o+"/phone";function s(n){return Object(e["a"])({url:c+"/recordCall",method:"post",data:n})}function a(n){return Object(e["a"])({url:c+"/statistics",params:{sopBaseId:n}})}function u(n){return Object(e["a"])({url:c+"/records",params:n})}}}]);